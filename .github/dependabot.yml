# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  - package-ecosystem: "gomod" # Change this based on your project
    directory: "/" # Location of package manifests (e.g., go.mod)
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 0 # Prevent Dependabot from creating PRs
