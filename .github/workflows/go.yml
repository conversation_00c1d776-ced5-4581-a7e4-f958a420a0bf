# This workflow will build a golang project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-go

name: Go build

on:
  # push:
   # branches: [ "main","qa-stable" ]
  workflow_dispatch:
  
jobs:
  build:
    runs-on: self-hosted
    steps:
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23.2'
    - name: Checkout Code
      uses: actions/checkout@v4
    - name: Build Provider
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-provider provider/main.go
    - name: Build Enhancer
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-enhancer enhancer/main.go
    - name: Build Prioritiser
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-prioritiser prioritiser/main.go
    - name: Build ExternalDC
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-externaldc externaldc/main.go
    - name: Build PServer
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-pserver pserver/main.go
    - name: Build Analyzer
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-analyzer analyzer/cmd/worker/main.go
    - name: Build Fetch8k
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o precize-fetch8k fetch8k/8k-filings.go
    - name: Build Vader Monitor
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o vader-m vader/monitor/monitor.go
    - name: Build Vader Deploy
      run: env GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o vader-d vader/deploy/deploy.go
