package main

import (
	"flag"
	"fmt"
	"sync"
	"time"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/services"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/cron"
	"github.com/precize/analyzer/pkg/kv"
	"github.com/precize/email"
	"github.com/precize/logger"
)

const (
	BATCH_SIZE               = 10                        // BATCH_SIZE is the number of tenants to process in a batch
	DEFAULT_CONFIG_FILE_PATH = "./local-application.yml" // CONFIG_FILE is the path to the configuration file
)

var writeInterval time.Duration // The fetch interval

func main() {
	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("analyzer")
		}
	}()

	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	debug := flag.Bool("debug", false, "Debug mode")
	flag.Parse()

	if configFilePath == nil || *configFilePath == "" {
		*configFilePath = DEFAULT_CONFIG_FILE_PATH
	}

	// Initialize the logger
	logger.InitializeLogs("analyzer", *debug)

	// Load the configuration
	if err := config.LoadConfig(configFilePath); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error loading config: %s", err))
		return
	}

	// Watch the configuration file for changes
	go config.WatchConfigFile(*configFilePath, func() {
		writeInterval = config.GetFetchInterval()
	})

	// Create a new badger db instance
	kv, err := kv.NewBadgerDB()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error creating badger db: %s", err))
		return
	}
	defer kv.Close()

	// Create a new elasticsearch client
	es := elasticsearch.NewClient()

	if _, err := es.Ping(); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error pinging elasticsearch: %s", err))
		return
	}

	// Setup mappings
	mappings := map[string]esjson.MappingType{
		elasticsearch.EVENTS_STORE_INDEX:     esjson.EVENTS_STORE_INDEX,
		elasticsearch.ENTITY_BEHAVIOUR_INDEX: esjson.ENTITY_BEHAVIOUR_INDEX,
		elasticsearch.ANOMALIES_INDEX:        esjson.ANOMALIES_INDEX,
	}

	// Ensure the events_store index exists
	if err := es.EnsureIndicesExist(mappings); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error ensuring events index exists: %s", err))
		return
	}

	// Get the worker interval from the configuration
	writeInterval = config.GetFetchInterval()

	logger.Print(logger.INFO, fmt.Sprintf("Starting worker with interval: %s", writeInterval))

	// Fetch all tenant ids
	tenants, err := es.FetchTenantIds()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error fetching tenant ids: %s", err))
		return
	}

	err = loadExistingEntityBehaviours(es, tenants)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error loading existing entity behaviours: %s", err))
		return
	}

	// Start the cron scheduler
	scheduler := cron.New()

	// Define the function to refresh entity behaviour
	refreshEntityBehaviour := func() {
		logger.Print(logger.INFO, "Aggregating entity behaviour for the past 3 months…")

		now := time.Now().UTC()
		start := time.Date(now.Year(), now.Month()-2, 1, 0, 0, 0, 0, time.UTC) // 1st day of 3 months ago
		end := now

		for _, tenantID := range tenants {
			entityBehaviours, err := es.AggregateEntityBehaviourWithTimestamp(tenantID, start, end)
			if err != nil {
				logger.Print(logger.ERROR,
					fmt.Sprintf("Error aggregating entity behaviour for tenant %s: %s", tenantID, err))
				continue
			}

			if len(entityBehaviours) > 0 {
				if err := es.StoreEntityBehaviour(entityBehaviours); err != nil {
					logger.Print(logger.ERROR,
						fmt.Sprintf("Error storing entity behaviour for tenant %s: %s", tenantID, err))
				}
			}
		}
	}

	refreshJob := &cron.CronJob{
		CronScheduler: scheduler,
		Expression:    "59 23 * * *", // 23:59 everyday.
		ExecuteFunc: func() {
			now := time.Now()
			tomorrow := now.AddDate(0, 0, 1)
			if tomorrow.Day() == 1 {
				refreshEntityBehaviour()
			}
		},
	}
	cron.RegisterCronJob(refreshJob)

	unprocessedReadJob := &cron.CronJob{
		CronScheduler: scheduler,
		Expression:    "0 0 * * 0", // every week
		ExecuteFunc: func() {
			cleanUnprocessedReadActivityWorker(tenants, es)
		},
	}
	cron.RegisterCronJob(unprocessedReadJob)

	// Start the cron scheduler
	logger.Print(logger.INFO, "Starting the cron scheduler...")
	scheduler.Start()

	activityWorker(tenants, kv, es)
}

// Shared processor
func processCloudEvents(kv *kv.BadgerDB, es *elasticsearch.Client, wg *sync.WaitGroup, tid string, isRead bool, currentTime time.Time) {
	eventType := "write"
	if isRead {
		eventType = "read"
	}

	var lastEventTimeKey string
	if isRead {
		lastEventTimeKey = fmt.Sprintf("last_read_event_time_%s", tid)
	} else {
		lastEventTimeKey = fmt.Sprintf("last_event_time_%s", tid)
	}

	lastEventTime, err := kv.Get([]byte(lastEventTimeKey))
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error getting last_event_time for tenant %s: %s", tid, err))
		return
	}
	if lastEventTime == "" {
		lastEventTime = currentTime.Add(-60 * time.Minute).UTC().Format(utils.ESTimeLayout)
	}

	parsedLastEventTime, err := time.Parse(utils.ESTimeLayout, lastEventTime)
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error parsing last_event_time for tenant %s: %s", tid, err))
	} else {
		parsedLastEventTime = parsedLastEventTime.UTC()
		if parsedLastEventTime.Before(currentTime.Add(-time.Hour * 24 * 7)) {
			lastEventTime = currentTime.Add(-time.Hour * 24 * 7).UTC().Format(utils.ESTimeLayout)
		}
	}

	events, newLastEventTime, err := es.FetchAggregatedData(tid, lastEventTime, isRead, isRead)
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error fetching aggregated %s data for tenant %s: %s", eventType, tid, err))
		return
	}
	if len(events) == 0 {
		logger.Print(logger.INFO, fmt.Sprintf("No new %s events fetched for tenant %s.", eventType, tid))
		return
	}

	logger.Print(logger.INFO, fmt.Sprintf("Fetched %d %s events for tenant %s", len(events), eventType, tid))

	ids := make([]string, len(events))
	for i, ev := range events {
		ids[i] = ev.ID
	}

	foundDocs, notFoundIDs, err := es.GetExistingEvents(elasticsearch.EVENTS_STORE_INDEX, ids)
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error checking document existence for tenant %s: %s", tid, err))
		return
	}

	bulkBody, existingEvents := elasticsearch.PrepareMergedEvents(elasticsearch.EVENTS_STORE_INDEX, events, foundDocs, notFoundIDs)

	if !isRead {
		anomalyChan := make(chan elasticsearch.Anomaly, 1000)
		defer close(anomalyChan)

		wg.Add(1)
		go func() {
			defer wg.Done()
			services.ProcessAnomalies(es, anomalyChan, nil, nil, nil)
		}()

		entityBehaviours := elasticsearch.GetEntityBehaviour(tid)
		if len(entityBehaviours) > 0 && entityBehaviours != nil {
			notFoundIDsMap := make(map[string]struct{}, len(notFoundIDs))
			for _, id := range notFoundIDs {
				notFoundIDsMap[id] = struct{}{}
			}

			allEvents := make([]elasticsearch.Event, 0, len(events))
			for _, ev := range events {
				if _, ok := notFoundIDsMap[ev.ID]; ok {
					allEvents = append(allEvents, ev)
				}
			}
			allEvents = append(allEvents, existingEvents...)

			for _, event := range allEvents {
				var username string
				if ak, ok := event.Additional["accessKeyId"]; ok {
					username = ak.(string)
				} else {
					username = event.Username
				}
				for _, behaviour := range entityBehaviours {
					if behaviour.EntityName == username && behaviour.TenantID == event.TenantID {
						for anomaly := range services.DetectAnomalies(*behaviour, event) {
							anomalyChan <- anomaly
						}
					}
				}
			}
		}
	}

	err = es.BulkUpsertEvents(elasticsearch.EVENTS_STORE_INDEX, bulkBody)
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error bulk upserting %s events for tenant %s: %s", eventType, tid, err))
		return
	}

	// If read, delete them after processing
	if isRead {
		logger.Print(logger.INFO, fmt.Sprintf("Deleting read events for tenant %s", tid))
		err = es.DeleteReadOnlyEventsInRange(tid, lastEventTime, currentTime, true)
		if err != nil {
			logger.Print(logger.INFO, fmt.Sprintf("Error deleting read events for tenant %s: %s", tid, err))
			return
		}
	}

	err = kv.Set([]byte(lastEventTimeKey), newLastEventTime)
	if err != nil {
		logger.Print(logger.INFO, fmt.Sprintf("Error setting last_event_time for tenant %s: %s", tid, err))
		return
	}
	logger.Print(logger.INFO, fmt.Sprintf("Updated last_event_time for tenant %s to %s", tid, newLastEventTime))
}

// Activity Worker
func activityWorker(tenants []string, kv *kv.BadgerDB, es *elasticsearch.Client) {
	for {
		start := time.Now()

		logger.Print(logger.INFO, "Starting new fetch cycle")

		// Start Processing 10 tenants at a time
		tenantCount := len(tenants)

		for batchStart := 0; batchStart < tenantCount; batchStart += BATCH_SIZE {
			batchEnd := batchStart + BATCH_SIZE
			if batchEnd > tenantCount {
				batchEnd = tenantCount
			}
			batch := tenants[batchStart:batchEnd]

			var wg sync.WaitGroup
			wg.Add(len(batch))

			for _, tenantID := range batch {
				go func(tid string) {
					defer wg.Done()
					processCloudEvents(kv, es, &wg, tid, false, start) // write
					processCloudEvents(kv, es, &wg, tid, true, start)  // read
				}(tenantID)
			}
			wg.Wait()
		}

		// Sleep for the remaining time in the interval
		// unless the aggregation cycle took longer than the interval
		elapsed := time.Since(start)
		if elapsed < writeInterval {
			logger.Print(logger.INFO, fmt.Sprintf("Aggregation cycle completed in %s, sleeping for %s", elapsed, writeInterval-elapsed))
			time.Sleep(writeInterval - elapsed)
		}
	}
}

// Cleans up unprocessed read activities for tenants.
func cleanUnprocessedReadActivityWorker(tenants []string, es *elasticsearch.Client) {
	end := time.Now().Add(-7 * 24 * time.Hour).UTC()
	start := end.Add(-7 * 24 * time.Hour).Format(utils.ESTimeLayout)

	logger.Print(logger.INFO, "Running cleanup for unprocessed read activities...")
	logger.Print(logger.INFO, fmt.Sprintf("\tCleaning data between %s and %s", start, end))

	tenantCount := len(tenants)

	for batchStart := 0; batchStart < tenantCount; batchStart += 1 {
		batchEnd := batchStart + BATCH_SIZE
		if batchEnd > tenantCount {
			batchEnd = tenantCount
		}
		batch := tenants[batchStart:batchEnd]

		var wg sync.WaitGroup
		wg.Add(len(batch))

		for _, tenantID := range batch {
			go func(tid string) {
				defer wg.Done()

				// Fetch the aggregated read data for this tenant from lastEventTime to now
				readEvents, _, err := es.FetchAggregatedDataWithTimestamp(tid, start, end, true, false)
				if err != nil {
					logger.Print(logger.INFO, fmt.Sprintf("Error fetching aggregated unprocessed read data for tenant %s: %s", tid, err))
					return
				}

				// If no new read events are fetched, return
				if len(readEvents) == 0 {
					logger.Print(logger.INFO, fmt.Sprintf("No new unprocessed read events fetched for tenant %s.", tid))
					return
				}

				logger.Print(logger.INFO, fmt.Sprintf("Fetched %d unprocessed read events for tenant %s", len(readEvents), tid))

				ids := make([]string, len(readEvents))
				for i, readEvent := range readEvents {
					ids[i] = readEvent.ID
				}

				// Check which event_ids already exist in the events_store
				// index and get a list of found existing events and a list of
				// not found event_ids
				foundDocs, notFoundIDs, err := es.GetExistingEvents(elasticsearch.EVENTS_STORE_INDEX, ids)
				if err != nil {
					logger.Print(logger.INFO, fmt.Sprintf("Error checking document existence for tenant %s: %s", tid, err))
					return
				}

				bulkBody, _ := elasticsearch.PrepareMergedEvents(elasticsearch.EVENTS_STORE_INDEX, readEvents, foundDocs, notFoundIDs)

				// Bulk upsert the new read events
				// If the event_id is found in the existing events, update the existing event
				// else insert the new event
				err = es.BulkUpsertEvents(elasticsearch.EVENTS_STORE_INDEX, bulkBody)
				if err != nil {
					logger.Print(logger.INFO, fmt.Sprintf("Error bulk upserting unprocessed read events for tenant %s: %s", tid, err))
					return
				}

				// Delete these read events from the database
				logger.Print(logger.INFO, fmt.Sprintf("Deleting unprocessed read events for tenant %s", tid))
				err = es.DeleteReadOnlyEventsInRange(tid, start, end, false)
				if err != nil {
					logger.Print(logger.INFO, fmt.Sprintf("Error deleting unprocessed read events for tenant %s: %s", tid, err))
					return
				}
			}(tenantID)
		}
		wg.Wait()
	}
}

func loadExistingEntityBehaviours(es *elasticsearch.Client, tenants []string) error {
	for _, tenant := range tenants {
		behaviours, err := elasticsearch.FetchLatestEntityBehaviourByTenant(es, tenant)
		if err != nil {
			logger.Print(logger.INFO, fmt.Sprintf("Error loading entity behaviours for tenant %s: %s", tenant, err))
			return err
		}
		if behaviours != nil {
			elasticsearch.SetEntityBehaviour(tenant, behaviours)
		}
	}

	logger.Print(logger.INFO, "Loaded existing entity behaviours")
	return nil
}
