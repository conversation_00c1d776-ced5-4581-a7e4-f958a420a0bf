package elasticsearch

func (es *Client) FetchExistingAnomalies(docIds []string) ([]Anomaly, []string, error) {
	return es.FetchExistingAnomaliesFromIndex(ANOMALIES_INDEX, docIds)
}

func (es *Client) FetchExistingAnomaliesFromIndex(index string, docIds []string) ([]Anomaly, []string, error) {
	foundAnomaly, notFoundAnomalyIds, err := GetExistingDocs[Anomaly](es, index, docIds)
	if err != nil {
		return nil, docIds, err
	}
	return foundAnomaly, notFoundAnomalyIds, nil
}
