package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/analyzer/internal/utils"
)

type AnomalyType string

const (
	ANOMALY_SERVERITY_HIGH   = "High"
	ANOMALY_SERVERITY_MEDIUM = "Medium"
	ANOMALY_SERVERITY_LOW    = "Low"

	// Anomaly types
	HIGH_ERROR_COUNT              AnomalyType = "HighErrorCount"
	UNUSUAL_EVENT_SOURCE          AnomalyType = "UnusualEventSource"
	UNUSUAL_REGION                AnomalyType = "UnusualRegion"
	UNUSUAL_SOURCE_APP            AnomalyType = "UnusualSourceApp"
	SHARED_SERVICE_ACCOUNT_VIA_IP AnomalyType = "SharedServiceAccountViaIP"
	HIGH_FREQUENCY_OF_EVENTS      AnomalyType = "HighFrequencyOfEvents"
	PERSONAL_EMAIL_USAGE          AnomalyType = "PersonalEmailUsage"
)

var anomalyIssue = map[AnomalyType]string{
	HIGH_ERROR_COUNT:              "High Error Count",
	UNUSUAL_EVENT_SOURCE:          "Unusual Event Source",
	UNUSUAL_REGION:                "Unusual Region",
	UNUSUAL_SOURCE_APP:            "Unusual Source App",
	SHARED_SERVICE_ACCOUNT_VIA_IP: "Shared Service Account Via IP",
	HIGH_FREQUENCY_OF_EVENTS:      "High Frequency of Events",
	PERSONAL_EMAIL_USAGE:          "Personal Email Usage",
}

func GetAnomalyIssue(a AnomalyType) (string, bool) {
	issue, ok := anomalyIssue[a]
	return issue, ok
}

// Base template for anomalies
var anomalyTemplates = map[AnomalyType]string{
	HIGH_ERROR_COUNT:              "A high number of error events were detected in a short period, indicating possible system instability, misconfigurations, or misuse. This detection is based on current activity.",
	UNUSUAL_EVENT_SOURCE:          "An event originated from a source not seen for this user between %s and %s, suggesting unusual access or unfamiliar tool usage.",
	UNUSUAL_REGION:                "Activity was detected from a geographic region not previously associated with this user between %s and %s, which may indicate unauthorized access or location policy violations.",
	UNUSUAL_SOURCE_APP:            "An application not used by this user between %s and %s was observed, potentially pointing to risky integrations or automation.",
	SHARED_SERVICE_ACCOUNT_VIA_IP: "A service account was accessed from multiple IP addresses in a pattern not observed between %s and %s. This may indicate account sharing or poor access controls.",
	HIGH_FREQUENCY_OF_EVENTS:      "This identity triggered a much higher number of events compared to its normal activity between %s and %s. This may suggest automation, abuse, or unusual workload.",
	PERSONAL_EMAIL_USAGE:          "A personal email (e.g., Gmail, Yahoo) was used for access. This violates policy or suggests a non-corporate identity. This anomaly is detected from recent activity.",
}

// Descriptions that do NOT use behavior context
var staticAnomalies = map[AnomalyType]bool{
	HIGH_ERROR_COUNT:     true,
	PERSONAL_EMAIL_USAGE: true,
}

// GetAnomalyDescription returns a clear human-readable description.
// behaviourStart/End are expected in RFC3339 format (ISO-8601).
func GetAnomalyDescription(a AnomalyType, behaviourStart, behaviourEnd string) string {
	template, ok := anomalyTemplates[a]
	if !ok {
		return "No description available for this anomaly type."
	}

	if staticAnomalies[a] {
		return template
	}

	startFormatted := formatDate(behaviourStart)
	endFormatted := formatDate(behaviourEnd)

	return fmt.Sprintf(template, startFormatted, endFormatted)
}

// formatDate parses and formats date into "Jan 02, 2006" format.
func formatDate(dateStr string) string {
	t, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		return dateStr // fallback to raw if parsing fails
	}
	return t.Format("Jan 02, 2006")
}

type Anomaly struct {
	ID                 string      `json:"id"`
	AccountID          string      `json:"accountId"`
	TenantID           string      `json:"tenantId"`
	Username           string      `json:"username"`
	AnomalyType        AnomalyType `json:"anomalyType"`
	AnomalyDescription string      `json:"anomalyDescription"`
	RefDoc             []string    `json:"refDoc"`
	Count              int         `json:"count"`
	LastDetectedAt     string      `json:"lastDetectedAt"`
	FirstDetectedAt    string      `json:"firstDetectedAt"`
	AnomalyEntity      string      `json:"anomalyEntity"`
	AdditionalDetails  string      `json:"additionalDetails"`
	ServiceCode        string      `json:"serviceCode"`
	Severity           string      `json:"severity"`
}

func (es *Client) StoreAnomalies(anomalies []Anomaly, anomalies_index string) error {
	ctx := context.Background()

	// Use a string builder to create a bulk request payload
	var bulkBuilder strings.Builder

	for _, anomaly := range anomalies {
		// Prepare the bulk index action and document
		actionLine := fmt.Sprintf(`{"index":{"_index":"%s","_id":"%s"}}`, anomalies_index, anomaly.ID)
		docLine := utils.ToJSON(anomaly)

		bulkBuilder.WriteString(actionLine + "\n")
		bulkBuilder.WriteString(docLine + "\n")
	}

	if bulkBuilder.Len() == 0 {
		return nil // Nothing to index
	}

	// Execute the bulk request
	res, err := es.Bulk(
		strings.NewReader(bulkBuilder.String()),
		es.Client.Bulk.WithContext(ctx),
		es.Client.Bulk.WithIndex(anomalies_index),
	)

	if err != nil {
		return fmt.Errorf("failed to execute bulk request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		return fmt.Errorf("bulk request failed with status: %s", res.Status())
	}

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("bulk request failed and error response could not be parsed: %w", err)
		}
		return fmt.Errorf("bulk request failed: %v", raw)
	}

	return nil
}
