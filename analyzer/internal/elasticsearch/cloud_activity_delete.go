package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

func (es *Client) DeleteReadOnlyEventsInRange(tid string, startTimeString string, endTime time.Time, isProcessed bool) error {
	startTime, err := time.Parse(utils.ESTimeLayout, startTimeString)
	if err != nil {
		return fmt.Errorf("failed to parse start time: %w", err)
	}

	ctx := context.Background()

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"readOnly": true,
						},
					},
					{
						"term": map[string]any{
							"tenantId.keyword": tid,
						},
					},
					{
						"range": map[string]any{
							"createdTime": map[string]any{
								"gte": startTime.Format(utils.ESTimeLayout),
								"lt":  endTime.Format(utils.ESTimeLayout),
							},
						},
					},
				},
			},
		},
	}

	if isProcessed {
		query["query"].(map[string]any)["bool"].(map[string]any)["must"] = append(query["query"].(map[string]any)["bool"].(map[string]any)["must"].([]map[string]any), map[string]any{
			"term": map[string]any{
				"isProcessed": true,
			},
		})
	}

	prettyQuery, err := json.MarshalIndent(query, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to encode delete query: %w", err)
	}
	queryReader := strings.NewReader(string(prettyQuery))

	logger.Print(logger.INFO, "Delete Query String", queryReader)

	res, err := es.Client.DeleteByQuery(
		[]string{CLOUD_ACTIVITY_INDEX},
		queryReader,
		es.Client.DeleteByQuery.WithContext(ctx),
		es.Client.DeleteByQuery.WithConflicts("proceed"),
		es.Client.DeleteByQuery.WithRefresh(true),
		es.Client.DeleteByQuery.WithPretty(),
	)
	if err != nil {
		return fmt.Errorf("delete-by-query failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("delete failed and could not parse response: %w", err)
		}
		return fmt.Errorf("delete-by-query error: %v", raw)
	}

	return nil
}
