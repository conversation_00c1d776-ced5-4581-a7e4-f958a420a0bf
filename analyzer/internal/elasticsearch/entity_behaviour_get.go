package elasticsearch

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"regexp"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

var (
	lastestEntityBehaviour map[string][]*EntityBehaviour
	entityBehaviourMutex   sync.RWMutex
)

type ActivityOverTime struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

type EntityBehaviour struct {
	ID                   string             `json:"id,omitempty"`
	AccountIDs           []string           `json:"accountIds,omitempty"`
	TenantID             string             `json:"tenantId,omitempty"`
	EntityType           string             `json:"entityType,omitempty"`
	ActivityOverTime     []ActivityOverTime `json:"activityOverTime,omitempty"`
	EntityName           string             `json:"entityName,omitempty"`
	ResourceTypes        []string           `json:"resourceTypes,omitempty"`
	EventSources         []string           `json:"eventSources,omitempty"`
	SourceIPs            []string           `json:"sourceIps,omitempty"`
	SourceApps           []string           `json:"sourceApps,omitempty"`
	Regions              []string           `json:"regions,omitempty"`
	CreatedAt            string             `json:"createdAt,omitempty"`
	UpdatedAt            string             `json:"updatedAt,omitempty"`
	EventNames           []string           `json:"eventNames,omitempty"`
	Count                int                `json:"count,omitempty"`
	TotalEvents          int                `json:"totalEvents,omitempty"`
	EntityBehaviourStart string             `json:"entityBehaviourStart,omitempty"`
	EntityBehaviourEnd   string             `json:"entityBehaviourEnd,omitempty"`
}

type EntityBehaviourAggregationResponse struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
	} `json:"hits"`
	Aggregations struct {
		AccessKeys TermsAggregation `json:"access_keys"`
		Users      TermsAggregation `json:"users"`
	} `json:"aggregations"`
}

type TermsAggregation struct {
	DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
	SumOtherDocCount        int      `json:"sum_other_doc_count"`
	Buckets                 []Bucket `json:"buckets"`
}

type Bucket struct {
	Key              string                    `json:"key"`
	DocCount         int                       `json:"doc_count"`
	AccountIds       *TermsAggregation         `json:"accountIds,omitempty"`
	Regions          *TermsAggregation         `json:"regions,omitempty"`
	EventNames       *TermsAggregation         `json:"event_names,omitempty"`
	EventSources     *TermsAggregation         `json:"event_sources,omitempty"`
	SourceApps       *TermsAggregation         `json:"source_apps,omitempty"`
	SourceIPs        *TermsAggregation         `json:"source_ips,omitempty"`
	ResourceTypes    *TermsAggregation         `json:"resource_types,omitempty"`
	ActivityOverTime *DateHistogramAggregation `json:"activity_over_time,omitempty"`
}

type DateHistogramAggregation struct {
	Buckets []DateHistogramBucket `json:"buckets"`
}

type DateHistogramBucket struct {
	KeyAsString string `json:"key_as_string"`
	Key         int64  `json:"key"`
	DocCount    int    `json:"doc_count"`
}

func (es *Client) AggregateEntityBehaviour(tid string) ([]*EntityBehaviour, error) {
	return es.AggregateEntityBehaviourWithTimestamp(tid, time.Now().Add(-7*24*time.Hour), time.Now())
}

func generateRandomHash() string {
	randomBytes := make([]byte, 32)
	if _, err := io.ReadFull(rand.Reader, randomBytes); err != nil {
		log.Printf("error generating random bytes: %v", err)
		return ""
	}
	hash := sha256.Sum256(randomBytes)
	return hex.EncodeToString(hash[:])
}

func (es *Client) AggregateEntityBehaviourWithTimestamp(tid string, startTime time.Time, endTime time.Time) ([]*EntityBehaviour, error) {
	now := endTime.UTC().Format(utils.ESTimeLayout)
	hash := generateRandomHash()
	queryString, err := buildAggregationQuery(tid, startTime, endTime)
	if err != nil {
		return nil, err
	}
	logger.Print(logger.INFO, fmt.Sprintf("Querying entity behaviour for tenant %s", tid))
	logger.Print(logger.INFO, fmt.Sprintf("Query string: %s", queryString))

	response, err := ExecuteQuery[EntityBehaviourAggregationResponse](es, CLOUD_ACTIVITY_INDEX, queryString)
	if err != nil {
		return nil, err
	}

	// logger.Print(logger.INFO, fmt.Sprintf("Response: %v", response))

	var entityBehaviours []*EntityBehaviour

	for _, bucket := range response.Aggregations.AccessKeys.Buckets {
		var accountIds []string
		for _, accountBucket := range bucket.AccountIds.Buckets {
			if accountBucket.Key == "" {
				continue
			}
			if slices.Contains(accountIds, accountBucket.Key) {
				continue
			}
			accountIds = append(accountIds, accountBucket.Key)
		}

		var sourceIps []string
		for _, sourceIpBucket := range bucket.SourceIPs.Buckets {
			if sourceIpBucket.Key == "" {
				continue
			}
			if slices.Contains(sourceIps, sourceIpBucket.Key) {
				continue
			}
			sourceIps = append(sourceIps, sourceIpBucket.Key)
		}

		docID := utils.GenerateCombinedHashID(tid, "access_key", bucket.Key, startTime.Format(utils.ESTimeLayout), endTime.Format(utils.ESTimeLayout), hash)

		entityBehaviour := &EntityBehaviour{
			ID:                   docID,
			TenantID:             tid,
			EntityType:           "access_key",
			EntityName:           bucket.Key,
			AccountIDs:           accountIds,
			SourceIPs:            sourceIps,
			CreatedAt:            now,
			UpdatedAt:            now,
			EntityBehaviourStart: startTime.Format(utils.ESTimeLayout),
			EntityBehaviourEnd:   endTime.Format(utils.ESTimeLayout),
		}
		entityBehaviours = append(entityBehaviours, entityBehaviour)
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	for _, bucket := range response.Aggregations.Users.Buckets {
		var accountIds = make(map[string]struct{})
		for _, accountBucket := range bucket.AccountIds.Buckets {
			if accountBucket.Key == "" {
				continue
			}
			if _, ok := accountIds[accountBucket.Key]; !ok {
				accountIds[accountBucket.Key] = struct{}{}
			}
		}

		var sourceIps = make(map[string]struct{})
		for _, sourceIpBucket := range bucket.SourceIPs.Buckets {
			if sourceIpBucket.Key == "" {
				continue
			}
			if _, ok := sourceIps[sourceIpBucket.Key]; !ok {
				sourceIps[sourceIpBucket.Key] = struct{}{}
			}
		}

		var sourceApps = make(map[string]struct{})
		for _, sourceAppBucket := range bucket.SourceApps.Buckets {
			if sourceAppBucket.Key == "" {
				continue
			}
			if _, ok := sourceApps[sourceAppBucket.Key]; !ok {
				sourceApps[sourceAppBucket.Key] = struct{}{}
			}
		}

		var regions = make(map[string]struct{})
		for _, regionBucket := range bucket.Regions.Buckets {
			if regionBucket.Key == "" {
				continue
			}
			if _, ok := regions[regionBucket.Key]; !ok {
				regions[regionBucket.Key] = struct{}{}
			}
		}

		var eventNames = make(map[string]struct{})
		for _, eventNameBucket := range bucket.EventNames.Buckets {
			if eventNameBucket.Key == "" {
				continue
			}
			if _, ok := eventNames[eventNameBucket.Key]; !ok {
				eventNames[eventNameBucket.Key] = struct{}{}
			}
		}

		var eventSources = make(map[string]struct{})
		for _, eventSourceBucket := range bucket.EventSources.Buckets {
			if eventSourceBucket.Key == "" {
				continue
			}
			if _, ok := eventSources[eventSourceBucket.Key]; !ok {
				eventSources[eventSourceBucket.Key] = struct{}{}
			}
		}

		var resourceTypes = make(map[string]struct{})
		for _, resourceTypeBucket := range bucket.ResourceTypes.Buckets {
			if resourceTypeBucket.Key == "" {
				continue
			}
			if _, ok := resourceTypes[resourceTypeBucket.Key]; !ok {
				resourceTypes[resourceTypeBucket.Key] = struct{}{}
			}
		}

		var activityOverTime []ActivityOverTime
		for _, activityBucket := range bucket.ActivityOverTime.Buckets {
			activityOverTime = append(activityOverTime, ActivityOverTime{
				Date:  time.Unix(activityBucket.Key/1000, 0).UTC().Format(utils.ESTimeLayout),
				Count: activityBucket.DocCount,
			})
		}

		docID := utils.GenerateCombinedHashID(tid, "entity", bucket.Key, startTime.Format(utils.ESTimeLayout), endTime.Format(utils.ESTimeLayout), hash)

		entityType := "entity"
		if strings.Contains(bucket.Key, "@") {
			matched := emailRegex.MatchString(bucket.Key)
			if matched {
				if strings.HasSuffix(bucket.Key, ".gserviceaccount.com") {
					entityType = "gcp_service_account"
				} else {
					entityType = "user"
				}
			}
		}

		accountIdsKeys := []string{}
		for accountId := range accountIds {
			accountIdsKeys = append(accountIdsKeys, accountId)
		}

		sourceIpsKeys := []string{}
		for sourceIp := range sourceIps {
			sourceIpsKeys = append(sourceIpsKeys, sourceIp)
		}

		sourceAppsKeys := []string{}
		for sourceApp := range sourceApps {
			sourceAppsKeys = append(sourceAppsKeys, sourceApp)
		}

		regionsKeys := []string{}
		for region := range regions {
			regionsKeys = append(regionsKeys, region)
		}

		eventNamesKeys := []string{}
		for eventName := range eventNames {
			eventNamesKeys = append(eventNamesKeys, eventName)
		}

		eventSourcesKeys := []string{}
		for eventSource := range eventSources {
			eventSourcesKeys = append(eventSourcesKeys, eventSource)
		}

		resourceTypesKeys := []string{}
		for resourceType := range resourceTypes {
			resourceTypesKeys = append(resourceTypesKeys, resourceType)
		}

		entityBehaviour := &EntityBehaviour{
			ID:                   docID,
			TenantID:             tid,
			EntityType:           entityType,
			EntityName:           bucket.Key,
			AccountIDs:           accountIdsKeys,
			SourceIPs:            sourceIpsKeys,
			SourceApps:           sourceAppsKeys,
			Regions:              regionsKeys,
			EventNames:           eventNamesKeys,
			EventSources:         eventSourcesKeys,
			ResourceTypes:        resourceTypesKeys,
			ActivityOverTime:     activityOverTime,
			CreatedAt:            now,
			UpdatedAt:            now,
			EntityBehaviourStart: startTime.Format(utils.ESTimeLayout),
			EntityBehaviourEnd:   endTime.Format(utils.ESTimeLayout),
		}

		entityBehaviours = append(entityBehaviours, entityBehaviour)
	}

	if lastestEntityBehaviour == nil {
		lastestEntityBehaviour = make(map[string][]*EntityBehaviour)
	}

	SetEntityBehaviour(tid, entityBehaviours)
	return entityBehaviours, nil
}

func buildAggregationQuery(tid string, startTime time.Time, endTime time.Time) (string, error) {
	params := esjson.QueryParams{
		StartTime: startTime.UTC().Format(utils.ESTimeLayout),
		EndTime:   endTime.UTC().Format(utils.ESTimeLayout),
		TenantId:  tid,
	}
	return esjson.LoadQuery(esjson.ENTITY_BEHAVIOUR_AGGREGATION, &params)
}

func GetEntityBehaviour(tid string) []*EntityBehaviour {
	entityBehaviourMutex.RLock()
	defer entityBehaviourMutex.RUnlock()

	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return nil
	}
	return lastestEntityBehaviour[tid]
}

func GetEntityBehaviourByUsername(tid string, username string) *EntityBehaviour {
	entityBehaviourMutex.RLock()
	defer entityBehaviourMutex.RUnlock()

	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return nil
	}
	for _, behaviour := range lastestEntityBehaviour[tid] {
		if behaviour.EntityName == username {
			return behaviour
		}
	}
	return nil
}

func UpdateEntityBehaviourByUsername(es *Client, tid string, username string, behaviour *EntityBehaviour) error {
	logger.Print(logger.INFO, "Updating entity behaviour for username:", username)
	logger.Print(logger.INFO, "Behaviour:", behaviour)
	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return fmt.Errorf("entity behaviour not found")
	}
	for i, b := range lastestEntityBehaviour[tid] {
		if b.EntityName == username {
			lastestEntityBehaviour[tid][i] = behaviour
			return nil
		}
	}
	return fmt.Errorf("entity behaviour not found")
}

type FetchEntityBehaviourResponse struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		Hits []struct {
			Source EntityBehaviour `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

func FetchEntityBehaviour(es *Client, tid string, username string, startTime, endTime time.Time) ([]*EntityBehaviour, error) {
	return FetchEntityBehaviourFromIndex(es, ENTITY_BEHAVIOUR_INDEX, tid, username, startTime, endTime)
}

func FetchEntityBehaviourFromIndex(es *Client, index string, tid string, username string, startTime, endTime time.Time) ([]*EntityBehaviour, error) {
	params := esjson.QueryParams{
		StartTime: startTime.UTC().Format(utils.ESTimeLayout),
		EndTime:   endTime.UTC().Format(utils.ESTimeLayout),
		TenantId:  tid,
		Username:  username,
	}
	queryString, err := esjson.LoadQuery(esjson.FETCH_ENTITY_BEHAVIOUR, &params)
	if err != nil {
		return nil, err
	}

	response, err := ExecuteQuery[FetchEntityBehaviourResponse](es, index, queryString)
	if err != nil {
		return nil, err
	}

	if response.Hits.Total.Value == 0 {
		return nil, nil
	}

	entityBehaviours := make([]*EntityBehaviour, len(response.Hits.Hits))
	for i, hit := range response.Hits.Hits {
		entityBehaviours[i] = &hit.Source
	}

	return entityBehaviours, nil
}

func SetEntityBehaviour(tid string, ebs []*EntityBehaviour) {
	entityBehaviourMutex.RLock()
	defer entityBehaviourMutex.RUnlock()

	if len(ebs) == 0 {
		return
	}
	if lastestEntityBehaviour == nil {
		lastestEntityBehaviour = make(map[string][]*EntityBehaviour)
	}
	lastestEntityBehaviour[tid] = ebs
}

type ByEntityAgg struct {
	AfterKey map[string]string `json:"after_key"`
	Buckets  []struct {
		Key       map[string]string `json:"key"`
		DocCount  int               `json:"doc_count"`
		LatestDoc struct {
			Hits struct {
				Hits []struct {
					Source EntityBehaviour `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		} `json:"latest_doc"`
	} `json:"buckets"`
}

type EntityAggResponse struct {
	Aggregations struct {
		ByEntity ByEntityAgg `json:"by_entity"`
	} `json:"aggregations"`
}

func injectAfterKey(queryStr string, after map[string]any) string {
	var queryMap map[string]any
	if err := json.Unmarshal([]byte(queryStr), &queryMap); err != nil {
		return queryStr
	}

	// Locate and inject into composite aggregation
	if aggs, ok := queryMap["aggs"].(map[string]any); ok {
		if byEntity, ok := aggs["by_entity"].(map[string]any); ok {
			if composite, ok := byEntity["composite"].(map[string]any); ok {
				composite["after"] = after
			}
		}
	}

	modified, err := json.Marshal(queryMap)
	if err != nil {
		return queryStr
	}
	return string(modified)
}

func FetchLatestEntityBehaviourByTenant(es *Client, tid string) ([]*EntityBehaviour, error) {
	var all []*EntityBehaviour
	after := map[string]any{}

	for {
		params := esjson.QueryParams{TenantId: tid}
		query, err := esjson.LoadQuery(esjson.LATEST_ENTITY_BEHAVIOUR, &params)
		if err != nil {
			return nil, err
		}
		if len(after) > 0 {
			query = injectAfterKey(query, after)
		}

		resp, err := ExecuteQuery[EntityAggResponse](es, ENTITY_BEHAVIOUR_INDEX, query)
		if err != nil {
			return nil, err
		}

		for _, bucket := range resp.Aggregations.ByEntity.Buckets {
			if len(bucket.LatestDoc.Hits.Hits) == 0 {
				continue
			}
			entity := bucket.LatestDoc.Hits.Hits[0].Source
			all = append(all, &entity)
		}

		if len(resp.Aggregations.ByEntity.AfterKey) > 0 {
			after = make(map[string]any)
			for k, v := range resp.Aggregations.ByEntity.AfterKey {
				after[k] = v
			}
		} else {
			break
		}
	}

	return all, nil
}
