package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/kv"
)

func (es *Client) StoreEntityBehaviour(ebs []*EntityBehaviour) error {
	return es.StoreEntityBehaviourInIndex(ENTITY_BEHAVIOUR_INDEX, ebs)
}

func (es *Client) StoreEntityBehaviourInIndex(index string, ebs []*EntityBehaviour) error {
	ctx := context.Background()

	// Use a string builder to create a bulk request payload
	var bulkBuilder strings.Builder

	for _, eb := range ebs {
		// Prepare the bulk index action and document
		actionLine := fmt.Sprintf(`{"index":{"_index":"%s","_id":"%s"}}`, ENTITY_BEHAVIOUR_INDEX, eb.ID)
		docLine := utils.ToJSON(eb)

		bulkBuilder.WriteString(actionLine + "\n")
		bulkBuilder.WriteString(docLine + "\n")
	}

	if bulkBuilder.Len() == 0 {
		return nil // Nothing to index
	}

	// Execute the bulk request
	res, err := es.Bulk(
		strings.NewReader(bulkBuilder.String()),
		es.Client.Bulk.WithContext(ctx),
		es.Client.Bulk.WithIndex(ENTITY_BEHAVIOUR_INDEX),
	)

	if err != nil {
		return fmt.Errorf("failed to execute bulk request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		return fmt.Errorf("bulk request failed with status: %s", res.Status())
	}

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("bulk request failed and error response could not be parsed: %w", err)
		}
		return fmt.Errorf("bulk request failed: %v", raw)
	}

	return nil
}

func (es *Client) StoreEntityBehaviourInKV(kvStore kv.Store, ebs []*EntityBehaviour) error {
	for _, eb := range ebs {
		// Serialize the EntityBehaviour object to JSON
		doc, err := json.Marshal(eb)
		if err != nil {
			return fmt.Errorf("failed to marshal EntityBehaviour: %w", err)
		}

		// Use the EntityBehaviour ID as the key and JSON string as the value
		key := []byte(eb.ID)
		value := string(doc)

		// Store the key-value pair in the KV store
		if err := kvStore.Set(key, value); err != nil {
			return fmt.Errorf("failed to set key %s in KV store: %w", eb.ID, err)
		}
	}

	return nil
}
