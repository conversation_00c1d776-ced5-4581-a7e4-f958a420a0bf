package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

// PrepareMergedEvents prepares a list of events to be upserted, returning them in a bulk request body format
// and also returns the list of merged events for further processing if needed.
func PrepareMergedEvents(indexName string, newEvents, existingEvents []Event, notFoundIDs []string) (bulkBody string, mergedEvents []Event) {
	existingEventsMap := make(map[string]Event)
	for _, event := range existingEvents {
		existingEventsMap[event.ID] = event
	}

	notFoundMap := make(map[string]bool)
	for _, id := range notFoundIDs {
		notFoundMap[id] = true
	}

	var bulkBuilder strings.Builder
	var result []Event

	for _, newEvent := range newEvents {
		if _, isNew := notFoundMap[newEvent.ID]; isNew {
			actionLine := fmt.Sprintf(`{"index":{"_index":"%s","_id":"%s"}}`, indexName, newEvent.ID)
			docLine := utils.ToJSON(newEvent)

			bulkBuilder.WriteString(actionLine + "\n")
			bulkBuilder.WriteString(docLine + "\n")
			result = append(result, newEvent)
		} else if existingEvent, exists := existingEventsMap[newEvent.ID]; exists {
			merged := mergeEvents(existingEvent, newEvent)

			actionLine := fmt.Sprintf(`{"update":{"_index":"%s","_id":"%s"}}`, indexName, newEvent.ID)
			docLine := fmt.Sprintf(`{"doc":%s}`, utils.ToJSON(merged))

			bulkBuilder.WriteString(actionLine + "\n")
			bulkBuilder.WriteString(docLine + "\n")
			result = append(result, merged)
		}
	}

	return bulkBuilder.String(), result
}

// BulkUpsertEvents upserts the specified new events in the specified index
//
// If the event_id is found in the existing events, update the existing event
// else insert the new event
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		newEvents := []Event{...}
//		existingEvents := []Event{...}
//		notFoundIDs := []string{"1", "2", "3"}
//		err := es.BulkUpsertEvents("events_store", newEvents, existingEvents, notFoundIDs)
//		if err != nil {
//			log.Fatalf("Error bulk upserting events: %s", err)
//		}
//	}
func (es *Client) BulkUpsertEvents(indexName, bulkBody string) error {
	if bulkBody == "" {
		return nil
	}

	ctx := context.Background()
	res, err := es.Bulk(
		strings.NewReader(bulkBody),
		es.Bulk.WithContext(ctx),
		es.Bulk.WithIndex(indexName),
	)
	if err != nil {
		return fmt.Errorf("failed to execute bulk request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		logger.Print(logger.INFO, fmt.Sprintf("Bulk request failed with status: %s", res.Body))
		return fmt.Errorf("bulk request failed with status: %s", res.Status())
	}

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("bulk request failed and error response could not be parsed: %w", err)
		}
		return fmt.Errorf("bulk request failed: %v", raw)
	}

	return nil
}

// mergeEvents merges the existing event with the new event
//
// It returns the merged event
//
//	func main() {
//		existingEvent := Event{...}
//		newEvent := Event{...}
//		mergedEvent := mergeEvents(existingEvent, newEvent)
//	    // mergedEvent {
//		//	DocId: "1",
//		//	AccountId: "123",
//		//	TenantId: "456",
//		//	Username: "user",
//		//	ResourceName: "resource",
//		//	ResourceType: "type",
//		//	EventSource: []string{"source1", "source2"},
//		//	SourceIp: []string{"ip1", "ip2"},
//		//	SourceApp: []string{"app1", "app2"},
//		//	ServiceCode: "code",
//		//	CreatedAt: "2021-01-01T00:00:00Z",
//		//	LastUpdatedAt: "2021-01-01T00:00:00Z",
//		//	EventName: []string{"event1", "event2"},
//		//	Count: 2,
//		//	RefDoc: []string{"ref1", "ref2"},
//		// }
//	}
func mergeEvents(existing, new Event) Event {
	merged := new
	merged.CreatedAt = existing.CreatedAt
	merged.LastUpdatedAt = new.LastUpdatedAt
	merged.EventSource = utils.MergeStringSlice(existing.EventSource, new.EventSource)
	merged.SourceIp = utils.MergeStringSlice(existing.SourceIp, new.SourceIp)
	merged.SourceApp = utils.MergeStringSlice(existing.SourceApp, new.SourceApp)
	merged.EventName = utils.MergeStringSlice(existing.EventName, new.EventName)
	merged.Count = existing.Count + new.Count
	merged.ErrorCount = existing.ErrorCount + new.ErrorCount

	return merged
}
