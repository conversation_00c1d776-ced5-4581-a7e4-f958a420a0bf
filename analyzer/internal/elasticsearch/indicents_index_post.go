package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/common"
)

func (es *Client) StoreAnomaliesIntoIncidents(anomalies []Anomaly, indexName string) error {
	ctx := context.Background()

	// Use a string builder to create a bulk request payload
	var bulkBuilder strings.Builder

	for _, anomaly := range anomalies {
		// Prepare the bulk index action and document
		incident := convertAnomalyToIncident(anomaly)
		actionLine := fmt.Sprintf(`{"index":{"_index":"%s","_id":"%s"}}`, indexName, incident.ID)
		docLine := utils.ToJSON(incident)

		bulkBuilder.WriteString(actionLine + "\n")
		bulkBuilder.WriteString(docLine + "\n")
	}

	if bulkBuilder.Len() == 0 {
		return nil // Nothing to index
	}

	// Execute the bulk request
	res, err := es.Bulk(
		strings.NewReader(bulkBuilder.String()),
		es.Client.Bulk.WithContext(ctx),
		es.Client.Bulk.WithIndex(indexName),
	)

	if err != nil {
		return fmt.Errorf("failed to execute bulk request: %w", err)
	}
	defer res.Body.Close()

	if res.StatusCode != 200 {
		return fmt.Errorf("bulk request failed with status: %s", res.Status())
	}

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("bulk request failed and error response could not be parsed: %w", err)
		}
		return fmt.Errorf("bulk request failed: %v", raw)
	}

	return nil
}

func convertAnomalyToIncident(anomaly Anomaly) common.Incident {
	var serviceID int
	switch anomaly.ServiceCode {
	case "aws":
		serviceID = 1000
	case "gcp":
		serviceID = 3000
	case "azure":
		serviceID = 2000
	default:
		serviceID = 0
	}

	type AdditionalData struct {
		Data         string   `json:"data"`
		Count        int      `json:"count"`
		ActivityDocs []string `json:"activityDocs"`
	}

	additionalData := AdditionalData{
		Data:         anomaly.AdditionalDetails,
		Count:        anomaly.Count,
		ActivityDocs: anomaly.RefDoc,
	}

	additionalDetailsString := utils.ToJSON(additionalData)

	issue, ok := GetAnomalyIssue(anomaly.AnomalyType)
	if !ok {
		issue = anomaly.AnomalyDescription
	}

	return common.Incident{
		ID:             utils.GenerateCombinedHashID(anomaly.ID, anomaly.TenantID),
		AlertID:        anomaly.ID,
		Issue:          issue,
		AccountID:      anomaly.AccountID,
		EntityID:       anomaly.Username,
		EntityType:     "ACTIVITY_IDENTITY",
		Source:         "precize",
		SourceRisk:     "Low",
		IssueSeverity:  "Low",
		PrecizeRisk:    "Low",
		SourceScore:    2.0,
		CreatedAt:      anomaly.FirstDetectedAt,
		UpdatedAt:      anomaly.LastDetectedAt,
		ServiceID:      serviceID,
		Category:       string(anomaly.AnomalyType),
		Description:    anomaly.AnomalyDescription,
		Status:         "open",
		Stage:          "anomaly",
		TenantID:       anomaly.TenantID,
		InsertTime:     anomaly.LastDetectedAt,
		AdditionalData: additionalDetailsString,
		IsIncident:     true,
		Type:           "anomaly",
	}
}
