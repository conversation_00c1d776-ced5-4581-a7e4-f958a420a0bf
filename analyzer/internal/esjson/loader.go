package esjson

import (
	"embed"
	"encoding/json"
	"fmt"
	"strings"
	"text/template"
)

//go:embed templates/*.json
var templatesFS embed.FS

func LoadQuery(queryType QueryType, params *QueryParams) (string, error) {
	filename := fmt.Sprintf("templates/%s.json", queryType)

	queryBytes, err := templatesFS.ReadFile(filename)
	if err != nil {
		return "", fmt.Errorf("failed to read query file %s: %w", filename, err)
	}

	if !json.Valid(queryBytes) {
		return "", fmt.Errorf("invalid JSON in query template: %s", filename)
	}

	tmpl, err := template.New(string(queryType)).Parse(string(queryBytes))
	if err != nil {
		return "", fmt.Errorf("failed to parse query template: %w", err)
	}

	var queryBuilder strings.Builder
	if err := tmpl.Execute(&queryBuilder, params); err != nil {
		return "", fmt.Errorf("failed to execute query template: %w", err)
	}

	return queryBuilder.String(), nil
}

//go:embed mappings/*.json
var mappingFS embed.FS

func LoadMapping(mappingType MappingType) (string, error) {
	filename := fmt.Sprintf("mappings/%s.json", mappingType)

	mappingBytes, err := mappingFS.ReadFile(filename)
	if err != nil {
		return "", fmt.Errorf("failed to read mapping file %s: %w", filename, err)
	}

	if !json.Valid(mappingBytes) {
		return "", fmt.Errorf("invalid JSON in mapping template: %s", filename)
	}

	return string(mappingBytes), nil
}
