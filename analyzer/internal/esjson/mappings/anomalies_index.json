{"mappings": {"properties": {"id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "accountId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tenantId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "username": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "anomalyType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "anomalyDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "refDoc": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "count": {"type": "integer"}, "lastDetectedAt": {"type": "date", "format": "date_time"}, "firstDetectedAt": {"type": "date", "format": "date_time"}, "additionalDetails": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "serviceCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "severity": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}