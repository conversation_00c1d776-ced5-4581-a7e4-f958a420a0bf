{"mappings": {"properties": {"id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "accountId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tenantId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "originalUsername": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "username": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "resourceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "resourceType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eventSource": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sourceIp": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "region": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sourceApp": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "serviceCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "createdAt": {"type": "date", "format": "date_time"}, "updatedAt": {"type": "date", "format": "date_time"}, "eventName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eventType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "count": {"type": "integer"}, "errorCount": {"type": "integer"}, "assumedRole": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "additional": {"properties": {"accessKeyPrincipalAccountId": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "accessKeyPrincipalType": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}}}}}}