{"query": {"bool": {"must": [{"match": {"tenantId.keyword": "{{.TenantId}}"}}, {"range": {"eventTime": {"gte": "{{.StartTime}}", "lt": "{{.EndTime}}"}}}], "must_not": [], "should": []}}, "size": 0, "sort": {"eventTime": {"order": "desc"}}, "aggs": {"access_keys": {"terms": {"field": "accessKeyId.keyword"}, "aggs": {"source_ips": {"terms": {"field": "sourceIp.keyword", "size": 100}}, "accountIds": {"terms": {"field": "accountId.keyword", "size": 100}}}}, "users": {"terms": {"field": "username.keyword", "size": 100}, "aggs": {"accountIds": {"terms": {"field": "accountId.keyword", "size": 100}}, "event_names": {"terms": {"field": "eventName.keyword", "size": 100}}, "regions": {"terms": {"field": "region.keyword", "size": 100}}, "source_apps": {"terms": {"field": "sourceApp.keyword", "size": 100}}, "source_ips": {"terms": {"field": "sourceIp.keyword", "size": 100}}, "event_sources": {"terms": {"field": "eventSource.keyword", "size": 100}}, "resource_types": {"terms": {"field": "resources.resourceType.keyword", "size": 100}}, "activity_over_time": {"date_histogram": {"field": "eventTime", "calendar_interval": "week"}}}}}}