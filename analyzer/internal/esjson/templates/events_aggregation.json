{"query": {"bool": {"must": [{"match": {"tenantId.keyword": "{{.TenantId}}"}}, {"range": {"eventTime": {"gte": "{{.StartTime}}", "lt": "{{.EndTime}}"}}}], "must_not": [{"term": {"readOnly": "true"}}], "should": []}}, "size": 0, "sort": {"eventTime": {"order": "desc"}}, "aggs": {"groubbyresourcename": {"terms": {"field": "resources.resourceName.keyword", "size": 1000}, "aggs": {"groupbyusername": {"terms": {"field": "username.keyword", "size": 1000}, "aggs": {"groupbyeventname": {"terms": {"field": "eventName.keyword", "size": 1000}, "aggs": {"groupbysourceapp": {"terms": {"field": "sourceApp.keyword", "size": 1000}, "aggs": {"groupbysourceip": {"terms": {"field": "sourceIp.keyword", "size": 1000}, "aggs": {"matchedDoc": {"top_hits": {"_source": ["eventSource", "tenantId", "accountId", "eventName", "sourceIp", "sourceApp", "serviceCode", "eventTime", "eventStatus", "cloudTrailEvent", "resources.resourceType", "accessKeyId", "accessKeyPrincipalAccountId", "accessKeyPrincipalId", "accessKeyPrincipalType", "region"], "size": 1}}, "errorCount": {"filter": {"term": {"eventStatus.keyword": "0"}}}}}}}}}}}}}}}