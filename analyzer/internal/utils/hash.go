package utils

import (
	"crypto/sha256"
	"fmt"
	"strings"
)

func GenerateCombinedHashID(fields ...string) string {
	var hashString string

	for i, field := range fields {
		hashString = hashString + strings.ToLower(field)
		if i != len(fields)-1 {
			hashString = hashString + "#"
		}
	}

	hash := HashSha256([]byte(hashString))
	if len(hash) > 20 {
		hash = hash[:20]
	}

	return hash
}

func HashSha256(str []byte) string {
	sum := sha256.Sum256(str)
	return fmt.Sprintf("%x", sum)
}
