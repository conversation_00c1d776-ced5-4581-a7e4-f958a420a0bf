package utils

import (
	"encoding/json"
	"net/http"
)

type IPInfo struct {
	Region string
	City   string
}

func GetIPInfo(ipAddress string) (*IPInfo, error) {
	httpClient := &http.Client{}
	response, err := httpClient.Get("https://ipinfo.io/" + ipAddress + "/json")
	if err != nil {
		return nil, err
	}
	if response.StatusCode == 429 {
		return nil, nil
	}
	if response.StatusCode != 200 {
		return nil, nil
	}
	var ipInfo IPInfo
	json.NewDecoder(response.Body).Decode(&ipInfo)
	return &ipInfo, nil
}
