package utils_test

import (
	"testing"

	"github.com/precize/analyzer/internal/utils"
)

func TestGetIP(t *testing.T) {
	ipInfo, err := utils.GetIPInfo("*************")
	if err != nil {
		t.<PERSON>rrorf("Error getting IP info: %s", err)
		return
	}
	if ipInfo == nil {
		t.<PERSON>rrorf("Error getting IP info: %s", err)
		return
	}

	if ipInfo.Region != "Karnataka" {
		t.<PERSON><PERSON><PERSON>("Expected region to be Karnataka, got %s", ipInfo.Region)
	}

	if ipInfo.City != "Bengaluru" {
		t.<PERSON><PERSON><PERSON>("Expected city to be Bengaluru, got %s", ipInfo.City)
	}
}
