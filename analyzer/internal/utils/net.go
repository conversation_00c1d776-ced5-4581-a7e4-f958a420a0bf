package utils

import (
	"fmt"
	"net"
)

func IPToCIDR(ipStr string) (string, error) {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return "", fmt.Errorf("invalid IP address: %s", ipStr)
	}

	var prefixLength int
	if ip.To4() != nil {
		prefixLength = 32
	} else if ip.To16() != nil {
		prefixLength = 128
	} else {
		return "", fmt.Errorf("unknown IP type for: %s", ipStr)
	}

	cidr := fmt.Sprintf("%s/%d", ip.String(), prefixLength)
	return cidr, nil
}
