package utils

import (
	"encoding/json"
	"strings"

	"github.com/precize/logger"
)

func MergeStrings(a string, b string) string {
	var result string
	if a != "" && b != "" {
		if strings.Compare(a, b) == 0 {
			result = a
		} else {
			result = a + "; " + b
		}
	} else if a != "" {
		result = a
	} else if b != "" {
		result = b
	}
	return result
}

// MergeJSONStrings unmarshals a and b (JSON strings), merges them as maps, and returns a merged JSON string.
// If a key exists in both, the value from b takes precedence.
func MergeJSONStrings(a string, b string) string {
	mapA := make(map[string]any)
	mapB := make(map[string]any)

	// Unmarshal a
	if a != "" && a != "{}" {
		if err := json.Unmarshal([]byte(a), &mapA); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling JSON A:", err, "\n", a)
		}
	}

	// Unmarshal b
	if b != "" && b != "{}" {
		if err := json.Unmarshal([]byte(b), &mapB); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling JSON B:", err, "\n", b)
		}
	}

	// Merge b into a (b overrides a on conflict)
	for k, v := range mapB {
		mapA[k] = v
	}

	// Marshal merged result
	mergedBytes, err := json.Marshal(mapA)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshaling merged JSON:", err)
		return "{}"
	}
	return string(mergedBytes)
}
