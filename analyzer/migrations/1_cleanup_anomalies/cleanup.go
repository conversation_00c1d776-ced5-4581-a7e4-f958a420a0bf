/// Date: 2025-06-18
/// Author: <PERSON><PERSON><PERSON>
/// Target: Cloud Incidents, Anomalies
/// Purpose: Cleanup old `Precize Anomaly` incidents that are not actual anomalies
///
/// Earlier we had anomalies that were not concrete anomalies as week did a soft check
/// against user entity behaviour.

package main

import (
	"context"
	"flag"
	"fmt"
	"io"
	"log"
	"strings"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/logger"
)

func main() {
	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	flag.Parse()
	logger.InitializeLogs("migration", false)
	config.LoadConfig(configFilePath)
	es := elasticsearch.NewClient()
	deleteAnomalyFromIncidents(es)
	deleteAnomaliesIndex(es)
	deleteEntityBehaviourIndex(es)
	fmt.Println("Cleanup completed")
}

func deleteAnomalyFromIncidents(es *elasticsearch.Client) {
	var query = strings.NewReader(`{
		"query": {
		    "bool": {
			    "must": [
			      {
			        "term": {
			          "type.keyword": "anomaly"
			        }
			      }
			    ],
			    "should": []
		    }
		}
	}`)

	executeDelete(es, []string{elasticsearch.CLOUD_INCIDENTS_INDEX}, query)
}

func executeDelete(es *elasticsearch.Client, indices []string, query io.Reader) {
	res, err := es.DeleteByQuery(
		indices,
		query,
		es.DeleteByQuery.WithContext(context.Background()),
		es.DeleteByQuery.WithConflicts("proceed"),
		es.DeleteByQuery.WithWaitForCompletion(true),
		es.DeleteByQuery.WithPretty(),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Error getting response for indices:", indices, err)
	}
	defer res.Body.Close()

	if res.IsError() {
		logger.Print(logger.ERROR, "Error response for indices:", indices, res.String())
	} else {
		log.Println("Successfully processed delete request for indices", indices)
		log.Println(res.String())
	}
}

// Delete index
func deleteIndex(es *elasticsearch.Client, index string) {
	err := es.DeleteIndex(index)
	if err != nil {
		logger.Print(logger.ERROR, "Error getting response for index:", index, err)
	}
	log.Println("Successfully deleted index:", index)
}

// Delete anomalies index
func deleteAnomaliesIndex(es *elasticsearch.Client) {
	deleteIndex(es, elasticsearch.ANOMALIES_INDEX)
}

// delete entity_behaviour index
func deleteEntityBehaviourIndex(es *elasticsearch.Client) {
	deleteIndex(es, elasticsearch.ENTITY_BEHAVIOUR_INDEX)
}
