package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/logger"
)

// MigrationElasticsearchOps handles Elasticsearch operations for migration using *_new indices
type MigrationElasticsearchOps struct {
	es *elasticsearch.Client
}

func NewMigrationElasticsearchOps(es *elasticsearch.Client) *MigrationElasticsearchOps {
	return &MigrationElasticsearchOps{es: es}
}

// BulkUpsertEventsToMigrationIndex stores events in events_store_new index
func (m *MigrationElasticsearchOps) BulkUpsertEventsToMigrationIndex(
	events []elasticsearch.Event,
) error {
	const batchSize = 2000

	total := len(events)
	if total == 0 {
		return nil
	}

	var allErrors []string
	for i := 0; i < total; i += batchSize {
		end := i + batchSize
		if end > total {
			end = total
		}
		batch := events[i:end]

		if err := m.bulkUpsertBatch(batch); err != nil {
			allErrors = append(allErrors, fmt.Sprintf("batch %d-%d: %v", i, end, err))
		}
	}

	if len(allErrors) > 0 {
		return fmt.Errorf("some batches failed:\n%s", strings.Join(allErrors, "\n"))
	}

	return nil
}

func (m *MigrationElasticsearchOps) bulkUpsertBatch(batch []elasticsearch.Event) error {
	var bulk strings.Builder
	for _, ev := range batch {
		fmt.Fprintf(&bulk, `{"index":{"_id":"%s"}}%c`, ev.ID, '\n')
		if err := json.NewEncoder(&bulk).Encode(ev); err != nil {
			return fmt.Errorf("marshal event: %w", err)
		}
	}

	res, err := m.es.Bulk(
		strings.NewReader(bulk.String()),
		m.es.Bulk.WithContext(context.Background()),
		m.es.Bulk.WithIndex(EVENTS_STORE_NEW),
		m.es.Bulk.WithRefresh("true"),
	)
	if err != nil {
		return fmt.Errorf("bulk API call: %w", err)
	}
	defer res.Body.Close()

	var br struct {
		Errors bool `json:"errors"`
		Items  []map[string]struct {
			ID     string                 `json:"_id"`
			Status int                    `json:"status"`
			Error  map[string]interface{} `json:"error,omitempty"`
		} `json:"items"`
	}
	if err := json.NewDecoder(res.Body).Decode(&br); err != nil {
		return fmt.Errorf("decode bulk response: %w", err)
	}

	if br.Errors {
		var failed []string
		for _, op := range br.Items {
			for _, v := range op {
				if v.Error != nil {
					failed = append(failed,
						fmt.Sprintf("id=%s status=%d reason=%v",
							v.ID, v.Status, v.Error["reason"]))
				}
			}
		}
		return fmt.Errorf("bulk index partially failed – %d/%d docs rejected: %s",
			len(failed), len(batch), strings.Join(failed, "; "))
	}

	logger.Print(
		logger.INFO,
		fmt.Sprintf("Successfully stored %d events in %s", len(batch), EVENTS_STORE_NEW),
	)
	return nil
}

// StoreEntityBehaviorsToMigrationIndex stores entity behaviors in entity_behaviour_new index
func (m *MigrationElasticsearchOps) StoreEntityBehaviorsToMigrationIndex(
	behaviors []*elasticsearch.EntityBehaviour,
) error {
	if len(behaviors) == 0 {
		return nil
	}

	var bulk strings.Builder
	for _, b := range behaviors {
		id := b.ID

		// action line
		fmt.Fprintf(&bulk, `{"index":{"_id":"%s"}}%c`, id, '\n')

		// document line
		if err := json.NewEncoder(&bulk).Encode(b); err != nil {
			return fmt.Errorf("marshal entity behaviour: %w", err)
		}
	}

	res, err := m.es.Bulk(
		strings.NewReader(bulk.String()),
		m.es.Bulk.WithContext(context.Background()),
		m.es.Bulk.WithIndex(ENTITY_BEHAVIOUR_NEW),
		m.es.Bulk.WithRefresh("true"),
	)
	if err != nil {
		return fmt.Errorf("bulk API call: %w", err)
	}
	defer res.Body.Close()

	var br struct {
		Errors bool `json:"errors"`
		Items  []map[string]struct {
			ID     string                 `json:"_id"`
			Status int                    `json:"status"`
			Error  map[string]interface{} `json:"error,omitempty"`
		} `json:"items"`
	}
	if err := json.NewDecoder(res.Body).Decode(&br); err != nil {
		return fmt.Errorf("decode bulk response: %w", err)
	}

	if br.Errors {
		var failed []string
		for _, op := range br.Items {
			for _, v := range op { // key is "index"|"create"|"update"
				if v.Error != nil {
					failed = append(failed,
						fmt.Sprintf("id=%s status=%d reason=%v",
							v.ID, v.Status, v.Error["reason"]))
				}
			}
		}
		return fmt.Errorf("bulk index partially failed – %d/%d docs rejected: %s",
			len(failed), len(behaviors), strings.Join(failed, "; "))
	}

	logger.Print(logger.INFO,
		fmt.Sprintf("Successfully stored %d entity behaviours in %s",
			len(behaviors), ENTITY_BEHAVIOUR_NEW))
	return nil
}

// StoreAnomaliesToMigrationIndex stores anomalies in anomalies_new index
func (m *MigrationElasticsearchOps) StoreAnomaliesToMigrationIndex(
	anomalies []elasticsearch.Anomaly,
) error {
	if len(anomalies) == 0 {
		return nil
	}

	var bulk strings.Builder
	for _, a := range anomalies {
		// action line
		fmt.Fprintf(&bulk, `{"index":{"_id":"%s"}}%c`, a.ID, '\n')

		// document line
		if err := json.NewEncoder(&bulk).Encode(a); err != nil {
			return fmt.Errorf("marshal anomaly: %w", err)
		}
	}

	res, err := m.es.Bulk(
		strings.NewReader(bulk.String()),
		m.es.Bulk.WithContext(context.Background()),
		m.es.Bulk.WithIndex(ANOMALIES_NEW),
		m.es.Bulk.WithRefresh("true"),
	)
	if err != nil {
		return fmt.Errorf("bulk API call: %w", err)
	}
	defer res.Body.Close()

	var br struct {
		Errors bool `json:"errors"`
		Items  []map[string]struct {
			ID     string                 `json:"_id"`
			Status int                    `json:"status"`
			Error  map[string]interface{} `json:"error,omitempty"`
		} `json:"items"`
	}
	if err := json.NewDecoder(res.Body).Decode(&br); err != nil {
		return fmt.Errorf("decode bulk response: %w", err)
	}

	if br.Errors {
		var failed []string
		for _, op := range br.Items {
			for _, v := range op { // key is "index" | "create" | "update"
				if v.Error != nil {
					failed = append(failed,
						fmt.Sprintf("id=%s status=%d reason=%v",
							v.ID, v.Status, v.Error["reason"]))
				}
			}
		}
		return fmt.Errorf("bulk index partially failed – %d/%d docs rejected: %s",
			len(failed), len(anomalies), strings.Join(failed, "; "))
	}

	logger.Print(
		logger.INFO,
		fmt.Sprintf("Successfully stored %d anomalies in %s", len(anomalies), ANOMALIES_NEW),
	)
	return nil
}

// GetEntityBehaviorsFromMigrationIndex retrieves entity behaviors from entity_behaviour_new index
func (m *MigrationElasticsearchOps) GetEntityBehaviorsFromMigrationIndex(tenantID string) ([]*elasticsearch.EntityBehaviour, error) {
	query := fmt.Sprintf(`{
		"query": {
			"term": {
				"tenantId.keyword": "%s"
			}
		},
		"size": 10000
	}`, tenantID)

	res, err := m.es.Search(
		m.es.Search.WithIndex(ENTITY_BEHAVIOUR_NEW),
		m.es.Search.WithBody(strings.NewReader(query)),
	)
	if err != nil {
		return nil, fmt.Errorf("error searching entity behaviors: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search request failed: %s", res.Status())
	}

	var response struct {
		Hits struct {
			Hits []struct {
				Source elasticsearch.EntityBehaviour `json:"_source"`
			} `json:"hits"`
		} `json:"hits"`
	}

	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("error decoding response: %w", err)
	}

	var behaviors []*elasticsearch.EntityBehaviour
	for _, hit := range response.Hits.Hits {
		behavior := hit.Source
		behaviors = append(behaviors, &behavior)
	}

	return behaviors, nil
}

// CheckMigrationProgress checks how much data exists in migration indices
func (m *MigrationElasticsearchOps) CheckMigrationProgress() (map[string]int64, error) {
	indices := []string{EVENTS_STORE_NEW, ENTITY_BEHAVIOUR_NEW, ANOMALIES_NEW}
	counts := make(map[string]int64)

	for _, index := range indices {
		res, err := m.es.Count(
			m.es.Count.WithIndex(index),
		)
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error counting documents in %s: %s", index, err))
			counts[index] = -1
			continue
		}
		defer res.Body.Close()

		if res.IsError() {
			logger.Print(logger.ERROR, fmt.Sprintf("Count request failed for %s: %s", index, res.Status()))
			counts[index] = -1
			continue
		}

		var response struct {
			Count int64 `json:"count"`
		}

		if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error decoding count response for %s: %s", index, err))
			counts[index] = -1
			continue
		}

		counts[index] = response.Count
	}

	return counts, nil
}

// ValidateMigrationIndices checks if migration indices exist and are accessible
func (m *MigrationElasticsearchOps) ValidateMigrationIndices() error {
	indices := []string{EVENTS_STORE_NEW, ENTITY_BEHAVIOUR_NEW, ANOMALIES_NEW}

	for _, index := range indices {
		res, err := m.es.Indices.Exists([]string{index})
		if err != nil {
			return fmt.Errorf("error checking if index %s exists: %w", index, err)
		}
		defer res.Body.Close()

		if res.StatusCode != 200 {
			return fmt.Errorf("migration index %s does not exist or is not accessible", index)
		}
	}

	logger.Print(logger.INFO, "All migration indices are accessible")
	return nil
}
