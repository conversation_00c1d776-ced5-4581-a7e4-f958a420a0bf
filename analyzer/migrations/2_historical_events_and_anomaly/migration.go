package main

import (
	"flag"
	"fmt"
	"sync"
	"time"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/services"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/kv"
	"github.com/precize/logger"
)

const (
	BATCH_SIZE                      = 1 // Smaller batch for migration
	MIGRATION_WORKER_LIMIT          = 2 // Conservative worker limit
	ENTITY_BEHAVIOR_BASELINE_MONTHS = 3 // Months of entity behavior to collect before migration
	ENTITY_BEHAVIOR_ROLLING_MONTHS  = 3 // Rolling window for entity behavior collection during migration
	MIGRATION_DEFAULT_MONTHS        = 6 // Default migration window in months

	// Migration-specific index names
	EVENTS_STORE_NEW     string = "events_store_new"
	ENTITY_BEHAVIOUR_NEW string = "entity_behaviour"
	ANOMALIES_NEW        string = "anomalies"
	CLOUD_INCIDENTS_NEW  string = "cloud_incidents"
)

type MigrationContext struct {
	es                 *elasticsearch.Client
	migrationOps       *MigrationElasticsearchOps
	kv                 *kv.BadgerDB
	tenants            []string
	migrationStartDate time.Time
	migrationEndDate   time.Time
	behaviorStartDate  time.Time // 3+ months before migration
	dryRun             bool
	skipExistingData   bool
}

func main() {
	logger.InitializeLogs("migration", false)
	logger.Print(logger.INFO, "Starting comprehensive historical data migration...")

	configFilePath := flag.String("config", "../local-application.yml", "Path to application.yml")
	tenant := flag.String("tenant", "", "Tenant ID")
	dryRun := flag.Bool("dry-run", false, "Run migration without storing data")
	skipExisting := flag.Bool("skip-existing", true, "Skip data that already exists in new indices")
	startMonthsBack := flag.Int("months-back", 6, "Migration start months back")
	flag.Parse()

	if err := config.LoadConfig(configFilePath); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error loading config: %s", err))
		return
	}

	kvStore, err := kv.NewBadgerDB()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error creating badger db: %s", err))
		return
	}
	defer kvStore.Close()

	es := elasticsearch.NewClient()
	if _, err := es.Ping(); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error pinging elasticsearch: %s", err))
		return
	}

	tenants, err := es.FetchTenantIds()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error fetching tenant ids: %s", err))
		return
	}

	if *tenant != "" {
		tenants = []string{*tenant}
	}

	// Calculate migration periods
	migrationEndDate := time.Now().UTC()
	migrationStartDate := migrationEndDate.AddDate(0, -*startMonthsBack, 0)
	behaviorStartDate := migrationStartDate.AddDate(0, -ENTITY_BEHAVIOR_BASELINE_MONTHS, 0) // Entity behavior baseline collection before migration

	// Process one tenant at a time
	for _, tenant := range tenants {
		ctx := &MigrationContext{
			es:                 es,
			migrationOps:       NewMigrationElasticsearchOps(es),
			kv:                 kvStore,
			tenants:            []string{tenant},
			migrationStartDate: migrationStartDate,
			migrationEndDate:   migrationEndDate,
			behaviorStartDate:  behaviorStartDate,
			dryRun:             *dryRun,
			skipExistingData:   *skipExisting,
		}

		logger.Print(logger.INFO, fmt.Sprintf("Migration Configuration:"))
		logger.Print(logger.INFO, fmt.Sprintf("  Entity Behavior Collection: %s to %s",
			behaviorStartDate.Format("2006-01-02"), migrationStartDate.Format("2006-01-02")))
		logger.Print(logger.INFO, fmt.Sprintf("  Migration Period: %s to %s",
			migrationStartDate.Format("2006-01-02"), migrationEndDate.Format("2006-01-02")))
		logger.Print(logger.INFO, fmt.Sprintf("  Tenant: %s", tenant))
		logger.Print(logger.INFO, fmt.Sprintf("  Dry Run: %v", *dryRun))

		if err := runComprehensiveMigration(ctx); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Migration failed: %s", err))
			return
		}

		logger.Print(logger.INFO, "Comprehensive historical migration completed successfully!")

		// Sleep for 5 minutes before proceeding to the next step
		logger.Print(logger.INFO, "Sleeping for 5 minutes...")
		time.Sleep(5 * time.Minute)
	}
}

// Helper function to check if a given week end is at month boundary
func isEndOfMonth(weekEnd time.Time) bool {
	nextWeek := weekEnd.AddDate(0, 0, 7)
	return weekEnd.Month() != nextWeek.Month() || weekEnd.Year() != nextWeek.Year()
}

func runComprehensiveMigration(ctx *MigrationContext) error {
	// Step 1: Create migration indices
	logger.Print(logger.INFO, "Step 1: Setting up migration indices...")
	if err := setupMigrationIndices(ctx); err != nil {
		return fmt.Errorf("failed to setup migration indices: %w", err)
	}

	// Step 2: Collect baseline entity behaviors (3 months before migration)
	logger.Print(logger.INFO, "Step 2: Collecting baseline entity behaviors...")
	if err := collectBaselineEntityBehaviors(ctx); err != nil {
		return fmt.Errorf("failed to collect baseline entity behaviors: %w", err)
	}

	// Step 3: Run chronological migration with proper entity behavior context
	logger.Print(logger.INFO, "Step 3: Running chronological migration...")
	if err := runChronologicalMigration(ctx); err != nil {
		return fmt.Errorf("failed to run chronological migration: %w", err)
	}

	return nil
}

func setupMigrationIndices(ctx *MigrationContext) error {
	mappings := map[string]esjson.MappingType{
		EVENTS_STORE_NEW:     esjson.EVENTS_STORE_INDEX,
		ENTITY_BEHAVIOUR_NEW: esjson.ENTITY_BEHAVIOUR_INDEX,
		ANOMALIES_NEW:        esjson.ANOMALIES_INDEX,
	}

	if ctx.dryRun {
		logger.Print(logger.INFO, "DRY RUN: Would create migration indices")
		return nil
	}

	return ctx.es.EnsureIndicesExist(mappings)
}

func collectBaselineEntityBehaviors(ctx *MigrationContext) error {
	logger.Print(logger.INFO, fmt.Sprintf("Collecting entity behaviors from %s to %s (%d months baseline)",
		ctx.behaviorStartDate.Format("2006-01-02"), ctx.migrationStartDate.Format("2006-01-02"), ENTITY_BEHAVIOR_BASELINE_MONTHS))

	// Process baseline collection using rolling window approach
	// Start from migration start and work backwards in rolling windows
	currentEnd := ctx.migrationStartDate
	windowNum := 1

	for currentEnd.After(ctx.behaviorStartDate) {
		windowStart := currentEnd.AddDate(0, -ENTITY_BEHAVIOR_ROLLING_MONTHS, 0)
		if windowStart.Before(ctx.behaviorStartDate) {
			windowStart = ctx.behaviorStartDate
		}

		logger.Print(logger.INFO, fmt.Sprintf("Processing baseline window %d (%d-month rolling): %s to %s",
			windowNum, ENTITY_BEHAVIOR_ROLLING_MONTHS, windowStart.Format("2006-01-02"), currentEnd.Format("2006-01-02")))

		if err := processEntityBehaviorMonth(ctx, windowStart, currentEnd); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error processing baseline window %d: %s", windowNum, err))
			// Continue with next window instead of failing
		}

		// Move to next rolling window (step back by rolling window size)
		currentEnd = windowStart
		windowNum++

		time.Sleep(500 * time.Millisecond) // To avoid overwhelming the system
	}

	logger.Print(logger.INFO, fmt.Sprintf("Completed baseline collection: %d windows processed with %d-month rolling window",
		windowNum-1, ENTITY_BEHAVIOR_ROLLING_MONTHS))
	return nil
}

func runChronologicalMigration(ctx *MigrationContext) error {
	logger.Print(logger.INFO, "Starting chronological migration with entity behavior context...")

	currentWeek := ctx.migrationStartDate
	weekNum := 1

	for currentWeek.Before(ctx.migrationEndDate) {
		weekEnd := currentWeek.AddDate(0, 0, 7)
		if weekEnd.After(ctx.migrationEndDate) {
			weekEnd = ctx.migrationEndDate
		}

		logger.Print(logger.INFO, fmt.Sprintf("Processing migration week %d: %s to %s",
			weekNum, currentWeek.Format("2006-01-02"), weekEnd.Format("2006-01-02")))

		// Process this week's events and detect anomalies
		if err := processMigrationWeek(ctx, currentWeek, weekEnd, weekNum); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error processing migration week %d: %s", weekNum, err))
			// Continue with next week
		}

		// Update entity behaviors only at month boundaries (simulate monthly cron)
		if isEndOfMonth(weekEnd) {
			// Calculate rolling window for entity behavior aggregation
			monthEnd := weekEnd
			monthStart := monthEnd.AddDate(0, -ENTITY_BEHAVIOR_ROLLING_MONTHS, 0) // Rolling window back from current week

			logger.Print(logger.INFO, fmt.Sprintf("Month boundary reached - updating entity behaviors for period %s to %s",
				monthStart.Format("2006-01-02"), monthEnd.Format("2006-01-02")))

			if err := processEntityBehaviorMonth(ctx, monthStart, monthEnd); err != nil {
				logger.Print(logger.ERROR, fmt.Sprintf("Error updating monthly entity behaviors: %s", err))
			}
		}

		currentWeek = weekEnd
		weekNum++

		// Delay between weeks to avoid overwhelming ES
		time.Sleep(1 * time.Second)
	}

	logger.Print(logger.INFO, fmt.Sprintf("Completed chronological migration: %d weeks processed", weekNum-1))
	return nil
}

func processEntityBehaviorMonth(ctx *MigrationContext, startTime, endTime time.Time) error {
	var wg sync.WaitGroup
	tenantChan := make(chan string, len(ctx.tenants))

	// Fill tenant channel
	for _, tenant := range ctx.tenants {
		tenantChan <- tenant
	}
	close(tenantChan)

	// Process tenants in parallel
	for i := 0; i < MIGRATION_WORKER_LIMIT; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for tenantID := range tenantChan {
				logger.Print(logger.INFO, fmt.Sprintf("Worker %d: Processing tenant %s", workerID, tenantID))
				if err := aggregateEntityBehaviorForTenant(ctx, tenantID, startTime, endTime); err != nil {
					logger.Print(logger.ERROR, fmt.Sprintf("Worker %d: Error aggregating entity behavior for tenant %s: %s",
						workerID, tenantID, err))
				}
				time.Sleep(2 * time.Second)
			}
		}(i)
	}

	wg.Wait()
	return nil
}

func aggregateEntityBehaviorForTenant(ctx *MigrationContext, tenantID string, startTime, endTime time.Time /* isBaseline bool */) error {
	// Get entity behaviors for this time period
	entityBehaviours, err := ctx.es.AggregateEntityBehaviourWithTimestamp(tenantID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("error aggregating entity behaviour: %w", err)
	}

	if len(entityBehaviours) == 0 {
		return nil
	}

	logger.Print(logger.INFO, fmt.Sprintf("Tenant %s: %d entity behaviors aggregated", tenantID, len(entityBehaviours)))

	if ctx.dryRun {
		logger.Print(logger.INFO, fmt.Sprintf("DRY RUN: Would store %d entity behaviors for tenant %s",
			len(entityBehaviours), tenantID))
		return nil
	}

	// Store in migration index
	logger.Print(logger.INFO, fmt.Sprintf("Storing %d entity behaviors for tenant %s", len(entityBehaviours), tenantID))
	if err := storeEntityBehaviorsInMigrationIndex(ctx, entityBehaviours); err != nil {
		return fmt.Errorf("error storing entity behaviors: %w", err)
	}

	// Also update in-memory cache for anomaly detection
	elasticsearch.SetEntityBehaviour(tenantID, entityBehaviours)

	return nil
}

func processMigrationWeek(ctx *MigrationContext, startTime, endTime time.Time, weekNum int) error {
	var wg sync.WaitGroup

	// Process tenants in smaller batches
	for i := 0; i < len(ctx.tenants); i += BATCH_SIZE {
		batchEnd := i + BATCH_SIZE
		if batchEnd > len(ctx.tenants) {
			batchEnd = len(ctx.tenants)
		}

		batch := ctx.tenants[i:batchEnd]

		for _, tenantID := range batch {
			wg.Add(1)
			go func(tid string) {
				defer wg.Done()
				if err := processTenantMigrationWeek(ctx, tid, startTime, endTime, weekNum); err != nil {
					logger.Print(logger.ERROR, fmt.Sprintf("Error processing tenant %s for week %d: %s",
						tid, weekNum, err))
				}
			}(tenantID)
		}

		wg.Wait() // Wait for each batch to complete
	}

	return nil
}

func processTenantMigrationWeek(ctx *MigrationContext, tenantID string, startTime, endTime time.Time, weekNum int) error {
	// Simulate historical processing time (end of the week)
	weekProcessingTime := endTime

	// Fetch events for this week
	events, _, err := ctx.es.FetchAggregatedDataWithTimestamp(tenantID,
		startTime.Format(utils.ESTimeLayout), weekProcessingTime, false, false)
	if err != nil {
		return fmt.Errorf("error fetching aggregated data: %w", err)
	}

	if len(events) == 0 {
		return nil
	}

	// Filter events to this week only
	var weekEvents []elasticsearch.Event
	for _, event := range events {
		eventTime, err := time.Parse(utils.ESTimeLayout, event.LastUpdatedAt)
		if err != nil {
			continue
		}
		if (eventTime.Equal(startTime) || eventTime.After(startTime)) && eventTime.Before(endTime) {
			weekEvents = append(weekEvents, event)
		}
	}

	if len(weekEvents) == 0 {
		return nil
	}

	logger.Print(logger.INFO, fmt.Sprintf("Tenant %s week %d: Processing %d events",
		tenantID, weekNum, len(weekEvents)))

	// Store events in migration index
	if err := storeEventsInMigrationIndex(ctx, weekEvents); err != nil {
		return fmt.Errorf("error storing events: %w", err)
	}

	// Detect anomalies using current entity behaviors
	anomaliesDetected := 0
	entityBehaviours := elasticsearch.GetEntityBehaviour(tenantID)

	if len(entityBehaviours) > 0 {
		anomalyChan := make(chan elasticsearch.Anomaly, 1000)

		// Start anomaly processor
		var processorWg sync.WaitGroup
		processorWg.Add(1)
		go func() {
			defer processorWg.Done()
			if !ctx.dryRun {
				cloud_incidents_index := CLOUD_INCIDENTS_NEW
				anomalies_index := ANOMALIES_NEW
				entity_index := ENTITY_BEHAVIOUR_NEW
				services.ProcessAnomalies(ctx.es, anomalyChan, &cloud_incidents_index, &anomalies_index, &entity_index)
			} else {
				// Dry run: just count
				for anomaly := range anomalyChan {
					anomaliesDetected++
					logger.Print(logger.INFO, fmt.Sprintf("DRY RUN: Would create anomaly %s for %s",
						string(anomaly.AnomalyType), anomaly.Username))
				}
			}
		}()

		// Detect anomalies for each event
		for _, event := range weekEvents {
			var username string
			if accessKey, isAccessKey := event.Additional["accessKeyId"]; isAccessKey {
				username = accessKey.(string)
			} else {
				username = event.Username
			}

			for _, behaviour := range entityBehaviours {
				if behaviour.EntityName == username && behaviour.TenantID == event.TenantID {
					eventAnomalies := services.DetectAnomaliesWithTimestamp(*behaviour, event, weekProcessingTime)

					for anomaly := range eventAnomalies {
						anomaly.FirstDetectedAt = event.LastUpdatedAt
						anomalyChan <- anomaly
						if ctx.dryRun {
							anomaliesDetected++
						}
					}
					break
				}
			}
		}

		close(anomalyChan)
		processorWg.Wait()
	}

	logger.Print(logger.INFO, fmt.Sprintf("Tenant %s week %d: %d events processed, %d anomalies detected",
		tenantID, weekNum, len(weekEvents), anomaliesDetected))

	return nil
}

func storeEventsInMigrationIndex(ctx *MigrationContext, events []elasticsearch.Event) error {
	if ctx.dryRun {
		logger.Print(logger.INFO, fmt.Sprintf("DRY RUN: Would store %d events in %s", len(events), EVENTS_STORE_NEW))
		return nil
	}

	return ctx.migrationOps.BulkUpsertEventsToMigrationIndex(events)
}

func storeEntityBehaviorsInMigrationIndex(ctx *MigrationContext, behaviors []*elasticsearch.EntityBehaviour) error {
	if ctx.dryRun {
		logger.Print(logger.INFO, fmt.Sprintf("DRY RUN: Would store %d entity behaviors in %s", len(behaviors), ENTITY_BEHAVIOUR_NEW))
		return nil
	}

	return ctx.migrationOps.StoreEntityBehaviorsToMigrationIndex(behaviors)
}
