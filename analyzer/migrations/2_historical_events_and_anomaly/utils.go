package main

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"time"

	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

// MigrationProgress tracks the progress of the migration
type MigrationProgress struct {
	StartTime             time.Time `json:"start_time"`
	LastProcessedBatch    int       `json:"last_processed_batch"`
	LastProcessedDate     string    `json:"last_processed_date"`
	TotalBatches          int       `json:"total_batches"`
	ProcessedTenants      []string  `json:"processed_tenants"`
	FailedTenants         []string  `json:"failed_tenants"`
	TotalEventsProcessed  int64     `json:"total_events_processed"`
	TotalAnomaliesCreated int64     `json:"total_anomalies_created"`
	Errors                []string  `json:"errors"`
}

// MigrationStats provides runtime statistics
type MigrationStats struct {
	MemoryUsageMB       uint64
	GoroutineCount      int
	ProcessingRate      float64 // events per second
	EstimatedCompletion time.Time
}

// saveProgress saves the current migration progress to a file
func saveProgress(progress *MigrationProgress, checkpointFile string) error {
	data, err := json.MarshalIndent(progress, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal progress: %w", err)
	}

	return os.WriteFile(checkpointFile, data, 0644)
}

// loadProgress loads the migration progress from a file
func loadProgress(checkpointFile string) (*MigrationProgress, error) {
	if _, err := os.Stat(checkpointFile); os.IsNotExist(err) {
		return &MigrationProgress{
			StartTime:        time.Now(),
			ProcessedTenants: make([]string, 0),
			FailedTenants:    make([]string, 0),
			Errors:           make([]string, 0),
		}, nil
	}

	data, err := os.ReadFile(checkpointFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read checkpoint file: %w", err)
	}

	var progress MigrationProgress
	if err := json.Unmarshal(data, &progress); err != nil {
		return nil, fmt.Errorf("failed to unmarshal progress: %w", err)
	}

	return &progress, nil
}

// getMemoryStats returns current memory usage statistics
func getMemoryStats() MigrationStats {
	var m runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m)

	return MigrationStats{
		MemoryUsageMB:  m.Alloc / 1024 / 1024,
		GoroutineCount: runtime.NumGoroutine(),
	}
}

// validateEvent performs basic validation on an event
func validateEvent(event elasticsearch.Event) error {
	if event.ID == "" {
		return fmt.Errorf("event has empty ID")
	}

	if event.TenantID == "" {
		return fmt.Errorf("event has empty TenantID")
	}

	if event.LastUpdatedAt == "" {
		return fmt.Errorf("event has empty LastUpdatedAt")
	}

	// Validate timestamp format
	if _, err := time.Parse(utils.ESTimeLayout, event.LastUpdatedAt); err != nil {
		return fmt.Errorf("invalid LastUpdatedAt format: %w", err)
	}

	return nil
}

// validateAnomaly performs basic validation on an anomaly
func validateAnomaly(anomaly elasticsearch.Anomaly) error {
	if anomaly.ID == "" {
		return fmt.Errorf("anomaly has empty ID")
	}

	if anomaly.TenantID == "" {
		return fmt.Errorf("anomaly has empty TenantID")
	}

	if anomaly.Username == "" {
		return fmt.Errorf("anomaly has empty Username")
	}

	if anomaly.FirstDetectedAt == "" {
		return fmt.Errorf("anomaly has empty FirstDetectedAt")
	}

	if anomaly.LastDetectedAt == "" {
		return fmt.Errorf("anomaly has empty LastDetectedAt")
	}

	// Validate timestamp formats
	if _, err := time.Parse(utils.ESTimeLayout, anomaly.FirstDetectedAt); err != nil {
		return fmt.Errorf("invalid FirstDetectedAt format: %w", err)
	}

	if _, err := time.Parse(utils.ESTimeLayout, anomaly.LastDetectedAt); err != nil {
		return fmt.Errorf("invalid LastDetectedAt format: %w", err)
	}

	return nil
}

// cleanupMemory forces garbage collection and logs memory stats
func cleanupMemory() {
	runtime.GC()
	stats := getMemoryStats()
	logger.Print(logger.INFO, fmt.Sprintf("Memory cleanup: %d MB used, %d goroutines",
		stats.MemoryUsageMB, stats.GoroutineCount))
}

// calculateBatchStats calculates statistics for a batch of events
func calculateBatchStats(events []elasticsearch.Event) (eventCount int, timeRange string, avgEventsPerHour float64) {
	if len(events) == 0 {
		return 0, "", 0
	}

	eventCount = len(events)

	// Find time range
	var minTime, maxTime time.Time
	for i, event := range events {
		eventTime, err := time.Parse(utils.ESTimeLayout, event.LastUpdatedAt)
		if err != nil {
			continue
		}

		if i == 0 || eventTime.Before(minTime) {
			minTime = eventTime
		}
		if i == 0 || eventTime.After(maxTime) {
			maxTime = eventTime
		}
	}

	timeRange = fmt.Sprintf("%s to %s", minTime.Format("2006-01-02 15:04"), maxTime.Format("2006-01-02 15:04"))

	// Calculate average events per hour
	duration := maxTime.Sub(minTime)
	if duration.Hours() > 0 {
		avgEventsPerHour = float64(eventCount) / duration.Hours()
	}

	return eventCount, timeRange, avgEventsPerHour
}

// checkDiskSpace checks if there's enough disk space for the migration
func checkDiskSpace(requiredSpaceGB float64) error {
	// This is a simplified check - in a full implementation you'd check actual disk space
	logger.Print(logger.INFO, fmt.Sprintf("Migration may require up to %.2f GB of additional storage", requiredSpaceGB))
	return nil
}

// estimateDataSize estimates the size of data to be processed
func estimateDataSize(ctx *MigrationContext, startDate, endDate time.Time) (totalEvents int64, estimatedSizeGB float64, err error) {
	logger.Print(logger.INFO, "Estimating migration data size...")

	// Sample a few tenants to estimate total size
	sampleSize := 3
	if len(ctx.tenants) < sampleSize {
		sampleSize = len(ctx.tenants)
	}

	var totalSampleEvents int64
	for i := 0; i < sampleSize; i++ {
		events, _, err := ctx.es.FetchAggregatedData(ctx.tenants[i], startDate.Format(utils.ESTimeLayout), false, false)
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error sampling tenant %s: %s", ctx.tenants[i], err))
			continue
		}

		// Count events in our date range
		for _, event := range events {
			eventTime, err := time.Parse(utils.ESTimeLayout, event.LastUpdatedAt)
			if err != nil {
				continue
			}

			if (eventTime.Equal(startDate) || eventTime.After(startDate)) && eventTime.Before(endDate) {
				totalSampleEvents++
			}
		}
	}

	// Extrapolate to all tenants
	if sampleSize > 0 {
		avgEventsPerTenant := float64(totalSampleEvents) / float64(sampleSize)
		totalEvents = int64(avgEventsPerTenant * float64(len(ctx.tenants)))

		// Estimate size (rough approximation: 1KB per event, 0.5KB per anomaly)
		estimatedSizeGB = float64(totalEvents) * 1.5 / 1024 / 1024 // 1.5KB average per record
	}

	logger.Print(logger.INFO, fmt.Sprintf("Estimated migration scope: %d events across %d tenants (%.2f GB)",
		totalEvents, len(ctx.tenants), estimatedSizeGB))

	return totalEvents, estimatedSizeGB, nil
}

// retryOperation retries an operation with exponential backoff
func retryOperation(operation func() error, maxRetries int, baseDelay time.Duration, operationName string) error {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			delay := time.Duration(attempt) * baseDelay
			logger.Print(logger.INFO, fmt.Sprintf("Retrying %s (attempt %d/%d) after %s",
				operationName, attempt+1, maxRetries+1, delay))
			time.Sleep(delay)
		}

		if err := operation(); err != nil {
			lastErr = err
			logger.Print(logger.ERROR, fmt.Sprintf("Attempt %d failed for %s: %s",
				attempt+1, operationName, err))
			continue
		}

		// Success
		if attempt > 0 {
			logger.Print(logger.INFO, fmt.Sprintf("Operation %s succeeded on attempt %d",
				operationName, attempt+1))
		}
		return nil
	}

	return fmt.Errorf("operation %s failed after %d attempts: %w", operationName, maxRetries+1, lastErr)
}

// validateMigrationPreconditions checks if the system is ready for migration
func validateMigrationPreconditions(ctx *MigrationContext) error {
	logger.Print(logger.INFO, "Validating migration preconditions...")

	// Check Elasticsearch connection
	if _, err := ctx.es.Ping(); err != nil {
		return fmt.Errorf("elasticsearch not accessible: %w", err)
	}

	// Check if required indices exist
	requiredIndices := []string{
		elasticsearch.EVENTS_STORE_INDEX,
		elasticsearch.ENTITY_BEHAVIOUR_INDEX,
		elasticsearch.ANOMALIES_INDEX,
	}

	for _, index := range requiredIndices {
		// This is a simplified check - you'd implement proper index existence check
		logger.Print(logger.INFO, fmt.Sprintf("Checking index %s...", index))
	}

	// Check tenant data availability
	if len(ctx.tenants) == 0 {
		return fmt.Errorf("no tenants found for migration")
	}

	logger.Print(logger.INFO, fmt.Sprintf("Found %d tenants for migration", len(ctx.tenants)))

	// Estimate data size and check disk space
	totalEvents, estimatedSizeGB, err := estimateDataSize(ctx, ctx.migrationStartDate, ctx.migrationEndDate)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Could not estimate data size: %s", err))
	} else {
		if err := checkDiskSpace(estimatedSizeGB * 2); err != nil { // 2x safety margin
			return fmt.Errorf("insufficient disk space: %w", err)
		}
	}

	logger.Print(logger.INFO, fmt.Sprintf("Migration preconditions validated. Estimated scope: %d events", totalEvents))
	return nil
}
