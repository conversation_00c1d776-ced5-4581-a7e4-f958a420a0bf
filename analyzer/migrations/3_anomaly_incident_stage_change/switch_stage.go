/// Date: 2025-07-17
/// Author: <PERSON><PERSON><PERSON>
/// Target: Cloud Incidents
/// Purpose: Update stage from 'ignore' to 'anomaly' for anomaly type incidents
///
/// This script updates incidents with type 'anomaly' and stage 'ignore' to have
/// stage 'anomaly' instead, aligning the stage with the incident type.

package main

import (
	"context"
	"flag"
	"fmt"
	"io"
	"log"
	"strings"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/logger"
)

func main() {
	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	direction := flag.String("direction", "up", "Migration direction: 'up' or 'down'")
	flag.Parse()

	logger.InitializeLogs("migration", false)
	config.LoadConfig(configFilePath)
	es := elasticsearch.NewClient()

	switch *direction {
	case "up":
		updateAnomalyStageUp(es)
		fmt.Println("Migration UP completed: Updated stage from 'ignore' to 'anomaly'")
	case "down":
		updateAnomalyStageDown(es)
		fmt.Println("Migration DOWN completed: Reverted stage from 'anomaly' to 'ignore'")
	default:
		fmt.Printf("Invalid direction: %s. Use 'up' or 'down'\n", *direction)
	}
}

func updateAnomalyStageUp(es *elasticsearch.Client) {
	var query = strings.NewReader(`{
		"query": {
		    "bool": {
			    "must": [
			      {
			        "term": {
			          "type.keyword": "anomaly"
			        }
			      },
			      {
			        "term": {
			          "stage.keyword": "ignore"
			        }
			      }
			    ]
		    }
		},
		"script": {
		    "source": "ctx._source.stage = 'anomaly'",
		    "lang": "painless"
		}
	}`)

	executeUpdate(es, []string{elasticsearch.CLOUD_INCIDENTS_INDEX}, query)
}

func updateAnomalyStageDown(es *elasticsearch.Client) {
	var query = strings.NewReader(`{
		"query": {
		    "bool": {
			    "must": [
			      {
			        "term": {
			          "type.keyword": "anomaly"
			        }
			      },
			      {
			        "term": {
			          "stage.keyword": "anomaly"
			        }
			      }
			    ]
		    }
		},
		"script": {
		    "source": "ctx._source.stage = 'ignore'",
		    "lang": "painless"
		}
	}`)

	executeUpdate(es, []string{elasticsearch.CLOUD_INCIDENTS_INDEX}, query)
}

func executeUpdate(es *elasticsearch.Client, indices []string, query io.Reader) {
	res, err := es.UpdateByQuery(
		indices,
		es.UpdateByQuery.WithBody(query),
		es.UpdateByQuery.WithContext(context.Background()),
		es.UpdateByQuery.WithConflicts("proceed"),
		es.UpdateByQuery.WithWaitForCompletion(true),
		es.UpdateByQuery.WithPretty(),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Error getting response for indices:", indices, err)
	}
	defer res.Body.Close()

	if res.IsError() {
		logger.Print(logger.ERROR, "Error response for indices:", indices, res.String())
	} else {
		log.Println("Successfully processed update request for indices", indices)
		log.Println(res.String())
	}
}
