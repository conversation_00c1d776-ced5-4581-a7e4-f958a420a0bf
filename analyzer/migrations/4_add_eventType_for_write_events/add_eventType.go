/// Date: 2025-07-22
/// Author: <PERSON><PERSON><PERSON>
/// Target: Events Store
/// Purpose: Add field `eventType: "write"` to legacy events
///
/// This script updates events in EVENTS_STORE_INDEX that are missing the
/// `eventType` field. These are assumed to be old write events and will be
/// explicitly marked with `eventType: "write"`.

package main

import (
	"context"
	"flag"
	"io"
	"log"
	"strings"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/logger"
)

func main() {
	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	flag.Parse()

	logger.InitializeLogs("migration", false)
	config.LoadConfig(configFilePath)

	es := elasticsearch.NewClient()

	var query = strings.NewReader(`{
		"query": {
			"bool": {
				"must_not": [
					{
						"exists": {
							"field": "eventType"
						}
					}
				]
			}
		},
		"script": {
			"source": "ctx._source.eventType = 'write'",
			"lang": "painless"
		}
	}`)

	executeUpdate(es, []string{elasticsearch.EVENTS_STORE_INDEX}, query)
}

func executeUpdate(es *elasticsearch.Client, indices []string, query io.Reader) {
	res, err := es.UpdateByQuery(
		indices,
		es.UpdateByQuery.WithBody(query),
		es.UpdateByQuery.WithContext(context.Background()),
		es.UpdateByQuery.WithConflicts("proceed"),
		es.UpdateByQuery.WithWaitForCompletion(true),
		es.UpdateByQuery.WithPretty(),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Error getting response for indices:", indices, err)
		return
	}
	defer res.Body.Close()

	if res.IsError() {
		logger.Print(logger.ERROR, "Error response for indices:", indices, res.String())
	} else {
		log.Println("Successfully updated missing eventType fields to 'write'")
		log.Println(res.String())
	}
}
