package cron

import (
	"fmt"
	"time"

	"github.com/precize/logger"
	cron "github.com/robfig/cron/v3"
)

var cronJobs []*CronJob

func New() *cron.Cron {
	return cron.New()
}

func RegisterCronJob(cj *CronJob) {
	cronJobs = append(cronJobs, cj)
	cj.Update()
}

func NotifyCronJobs() {
	for _, cronJob := range cronJobs {
		cronJob.Update()
	}
}

type CronJob struct {
	CronScheduler *cron.Cron
	JobID         cron.EntryID
	Interval      func() time.Duration
	Expression    string
	ExecuteFunc   func()
}

func (cj *CronJob) Update() {
	if cj.JobID != 0 {
		cj.CronScheduler.Remove(cj.JobID)
	}

	switch {
	case cj.Expression != "":
		logger.Print(logger.INFO, fmt.Sprintf("Updating cron job with expression: %s", cj.Expression))
		cj.JobID, _ = cj.CronScheduler.AddFunc(cj.Expression, cj.ExecuteFunc)
		break
	default:
		logger.Print(logger.INFO, fmt.Sprintf("Updating cron job with interval: %s", cj.Interval()))
		cj.JobID, _ = cj.CronScheduler.AddFunc(fmt.Sprintf("@every %s", cj.Interval()), cj.ExecuteFunc)
		break
	}
}
