package kv

import (
	"fmt"
	"os"
	"path/filepath"

	badger "github.com/dgraph-io/badger/v4"
)

type BadgerDB struct {
	DB *badger.DB
}

var globalBadgerDB *BadgerDB

func NewBadgerDB() (*BadgerDB, error) {
	badgerPath := "/tmp/badgerkv"
	ex, err := os.Executable()
	if err == nil {
		exPath := filepath.Dir(ex)
		badgerPath = filepath.Join(exPath, "badgerkv")
	}
	db, err := badger.Open(badger.DefaultOptions(badgerPath))
	if err != nil {
		return nil, fmt.Errorf("error opening badger db: %w", err)
	}
	globalBadgerDB = &BadgerDB{DB: db}
	return globalBadgerDB, nil
}

func GetBadgerDB() *BadgerDB {
	if globalBadgerDB == nil {
		panic("badgerdb not initialized")
	}
	return globalBadgerDB
}

func (bdb *BadgerDB) Get(key []byte) (string, error) {
	var value string
	err := bdb.DB.View(func(txn *badger.Txn) error {
		item, err := txn.Get(key)
		if err == badger.ErrKeyNotFound {
			return nil
		}
		if err != nil {
			return err
		}

		return item.Value(func(val []byte) error {
			value = string(val)
			return nil
		})
	})

	return value, err
}

func (bdb *BadgerDB) Set(key []byte, value string) error {
	if value == "" {
		return nil
	}
	return bdb.DB.Update(func(txn *badger.Txn) error {
		return txn.Set(key, []byte(value))
	})
}

func (bdb *BadgerDB) Delete(key []byte) error {
	return bdb.DB.Update(func(txn *badger.Txn) error {
		return txn.Delete(key)
	})
}

func (bdb *BadgerDB) Close() error {
	return bdb.DB.Close()
}
