package kv

type Store interface {
	Get(key []byte) (string, error)
	Set(key []byte, value string) error
	Delete(key []byte) error
}

type InMemoryStore struct {
	Store map[string]string
}

func NewInMemoryStore() *InMemoryStore {
	return &InMemoryStore{Store: make(map[string]string)}
}

func (s *InMemoryStore) Get(key []byte) (string, error) {
	if value, ok := s.Store[string(key)]; ok {
		return value, nil
	}
	return "", nil
}

func (s *InMemoryStore) Set(key []byte, value string) error {
	if value == "" {
		return nil
	}
	s.Store[string(key)] = value
	return nil
}

func (s *InMemoryStore) Delete(key []byte) error {
	delete(s.Store, string(key))
	return nil
}
