package common

type AIResourcesDoc struct {
	ID                string `json:"id"`
	EntityID          string `json:"entityId"`
	EntityType        string `json:"entityType"`
	AccountID         string `json:"accountId"`
	StageCompleted    string `json:"stageCompleted"`
	Deleted           bool   `json:"deleted"`
	CollectedAt       int64  `json:"collectedAt"`
	CreatedDate       int64  `json:"createdDate"`
	ServiceID         int    `json:"serviceId"`
	TenantID          string `json:"tenantId"`
	EntityJson        string `json:"entityJson"`
	LastWriteTime     int64  `json:"lastWriteTime,omitempty"`
	LastWriteActivity *Event `json:"lastWriteActivity,omitempty"`
}

type PrecizeCreationsDoc struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Account     string `json:"account"`
	Service     string `json:"service"`
	CreatedDate string `json:"createdDate"`
	ServiceID   int    `json:"serviceId"`
	TenantID    string `json:"tenantId"`
	EntityJson  string `json:"entityJson"`
}
