package common

import (
	"encoding/json"
	"fmt"

	"github.com/precize/logger"
)

var (
	priorityProperties = map[string]struct{}{
		"UserData": {},
	}
)

func fetchLeafNodes(data map[string]any, result *any) {
	for _, v := range data {
		switch val := v.(type) {
		case map[string]any:
			fetchLeafNodes(val, result)
		case string:
			if *result == nil {
				*result = val
			} else {
				valStr := (*result).(string) + val
				*result = valStr
			}
		default:
			switch v := (*result).(type) {
			case []any:
				v = append(v, val)
				*result = v
			default:
				vArr := make([]any, 0)
				vArr = append(vArr, val)
				*result = vArr
			}

		}
	}
}

func ProcessCftTemplate(fileContentJson map[string]any, tenantID string) (string, error) {

	resources, ok := fileContentJson["Resources"].(map[string]any)
	if !ok {
		logger.Print(logger.INFO, "Resources filed not present in template")
		return "", fmt.Errorf("Resources filed not present in template")
	}

	priorityConfigs := make(map[string]any)

	for resourceName, resource := range resources {
		properties, ok := resource.(map[string]any)["Properties"].(map[string]any)
		if !ok {
			continue
		}

		for propName, propValue := range properties {

			if propValue, ok := propValue.(map[string]any); ok {

				if _, ok := priorityProperties[propName]; ok {
					var value any
					fetchLeafNodes(propValue, &value)
					if value != nil {
						priorityConfigs[resourceName] = map[string]any{
							propName: value,
						}
					}
				}
			}
		}
	}

	if len(priorityConfigs) > 0 {
		priorityConfigsJson, err := json.Marshal(priorityConfigs)
		if err != nil {
			logger.Print(logger.ERROR, "Erros Marshalling commitIdsUpdatedVars")
			return "", err
		}
		priorityConfigsJsonStr := string(priorityConfigsJson)
		return priorityConfigsJsonStr, nil
	}

	return "", nil
}
