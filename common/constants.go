package common

import (
	"strings"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	GITHUB    = "github"
	GITLAB    = "gitlab"
	BITBUCKET = "bitbucket"
	TERRAFORM = "terraform"
)

var (
	EntityTypeMapping map[string]string
)

func InitGenericConstants() {

	logger.Print(logger.INFO, "Initializing generic constants")

	EntityTypeMapping = make(map[string]string)

	entityTypeConstantsQuery := `{"query":{"bool":{"must":[{"match":{"type":"entityTypeMapping"}}],"must_not":[],"should":[]}}}`

	entityTypeConstantsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.GENERIC_MAPPING_INDEX}, entityTypeConstantsQuery)
	if err != nil {
		return
	}

	for _, entityTypeConstantsDoc := range entityTypeConstantsDocs {

		if eventEntityType, ok := entityTypeConstantsDoc["eventEntityType"].(string); ok {
			if entityType, ok := entityTypeConstantsDoc["entityType"].(string); ok {
				// Note that toLower is being done today because defender gives it like that
				EntityTypeMapping[strings.ToLower(eventEntityType)] = entityType
			}
		}
	}

	logger.Print(logger.INFO, "Initialized generic constants")
}
