package common

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"

	"github.com/precize/logger"
)

var key = []byte("p1C0$mdA8&asd#@!njcYEnC32**rB4iI")

func EncryptTextAES(text []byte) ([]byte, error) {

	block, err := aes.NewCipher(key)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create cipher", err)
		return nil, err
	}

	b := base64.StdEncoding.EncodeToString(text)
	ciphertext := make([]byte, aes.BlockSize+len(b))
	iv := ciphertext[:aes.BlockSize]

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		logger.Print(logger.ERROR, "Failed to read initialization vector", err)
		return nil, err
	}

	cfb := cipher.NewCFBEncrypter(block, iv)
	cfb.XORKeyStream(ciphertext[aes.BlockSize:], []byte(b))

	return ciphertext, nil
}

func DecryptTextAES(text []byte) ([]byte, error) {

	block, err := aes.NewCipher(key)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create cipher", err)
		return nil, err
	}

	if len(text) < aes.BlockSize {
		err = errors.New("ciphertext too short")
		logger.Print(logger.ERROR, "Cipher text length too short", err)
		return nil, err
	}

	iv := text[:aes.BlockSize]
	text = text[aes.BlockSize:]

	cfb := cipher.NewCFBDecrypter(block, iv)
	cfb.XORKeyStream(text, text)

	data, err := base64.StdEncoding.DecodeString(string(text))
	if err != nil {
		logger.Print(logger.ERROR, "Failed to decode base64", err)
		return nil, err
	}

	return data, nil
}
