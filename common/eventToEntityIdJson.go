package common

var (
	AwsEventsMappingJson = `
{
    "AddInstanceGroups": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "AddInstanceGroups",
        "fallbackPathToId": "$.responseElements.jobFlowId",
        "isDefault": null,
        "pathToId": "$.requestParameters.jobFlowId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "AddJobFlowSteps": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "AddJobFlowSteps",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.jobFlowId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "AddListenerCertificates": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ACM_MANAGED_CERTIFICATE",
        "event": "AddListenerCertificates",
        "fallbackPathToId": "$.requestParameters.certificates[*].certificateArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.certificates[*].certificateArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "AddPermission20150331v2": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "AddPermission20150331v2",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "AddRoleToDBCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "AddRoleToDBCluster",
        "fallbackPathToId": "$.requestParameters.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "AddRoleToDBInstance": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS",
        "event": "AddRoleToDBInstance",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "AddRoleToInstanceProfile": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "AddRoleToInstanceProfile",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.instanceProfileName",
        "source": "ec2.amazonaws.com"
    },
    "AddTags": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "AddTags",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "AddTagsToResource": {
        "cspId": 2049,
        "entityType": "RDS_CLUSTER_SNAPSHOT",
        "event": "AddTagsToResource",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceName",
        "source": "rds.amazonaws.com"
    },
    "AddTagsToStream": {
        "cspId": 2049,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "AddTagsToStream",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "AllocateAddress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_REGION",
        "event": "AllocateAddress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.allocationId",
        "source": "ec2.amazonaws.com"
    },
    "ApplySecurityGroupsToLoadBalancer": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "ApplySecurityGroupsToLoadBalancer",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "AssignIpv6Addresses": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "AssignIpv6Addresses",
        "fallbackPathToId": "$.requestParameters.AssignIpv6AddressesRequest.NetworkInterfaceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.AssignIpv6AddressesRequest.NetworkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "AssignPrivateIpAddresses": {
        "cspId": 2049,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "AssignPrivateIpAddresses",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "AssociateAddress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "AssociateAddress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.instanceId",
        "source": "ec2.amazonaws.com"
    },
    "AssociateDhcpOptions": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "AssociateDhcpOptions",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "AssociateIamInstanceProfile": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "AssociateIamInstanceProfile",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.AssociateIamInstanceProfileRequest.InstanceId",
        "source": "ec2.amazonaws.com"
    },
    "AssociateRouteTable": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "AssociateRouteTable",
        "fallbackPathToId": "$.requestParameters.routeTableId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "AssociateVPCWithHostedZone": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53",
        "event": "AssociateVPCWithHostedZone",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.hostedZoneId",
        "source": "route53.amazonaws.com"
    },
    "AssociateVpcCidrBlock": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "AssociateVpcCidrBlock",
        "fallbackPathToId": "$.requestParameters.AssociateVpcCidrBlockRequest.VpcId",
        "isDefault": false,
        "pathToId": "$.requestParameters.AssociateVpcCidrBlockRequest.VpcId",
        "source": "ec2.amazonaws.com"
    },
    "AttachGroupPolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "AttachGroupPolicy",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "AttachInternetGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_IGW",
        "event": "AttachInternetGateway",
        "fallbackPathToId": "$.requestParameters.internetGatewayId",
        "isDefault": false,
        "pathToId": "$.requestParameters.internetGatewayId",
        "source": "ec2.amazonaws.com"
    },
    "AttachLoadBalancerToSubnets": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "AttachLoadBalancerToSubnets",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "AttachNetworkInterface": {
        "cspId": 2049,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "AttachNetworkInterface",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "AttachUserPolicy": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "AttachUserPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.policyArn",
        "source": "iam.amazonaws.com"
    },
    "AttachVolume": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_VOLUME",
        "event": "AttachVolume",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.volumeId",
        "source": "ec2.amazonaws.com"
    },
    "AttachVpnGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "AttachVpnGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "AuthorizeSecurityGroupEgress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SECURITY_GROUP",
        "event": "AuthorizeSecurityGroupEgress",
        "fallbackPathToId": null,
        "isDefault": true,
        "pathToId": "$.requestParameters.groupId",
        "source": "ec2.amazonaws.com"
    },
    "AuthorizeSecurityGroupIngress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SECURITY_GROUP",
        "event": "AuthorizeSecurityGroupIngress",
        "fallbackPathToId": "$.requestParameters.groupId",
        "isDefault": false,
        "pathToId": "$.requestParameters.groupId",
        "source": "ec2.amazonaws.com"
    },
    "CancelKeyDeletion": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "CancelKeyDeletion",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "CancelSteps": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "CancelSteps",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "ChangePassword": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "ChangePassword",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "ChangeResourceRecordSets": {
        "cspId": 2049,
        "entityType": "ROUTE_53",
        "event": "ChangeResourceRecordSets",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.hostedZoneId",
        "source": "route53.amazonaws.com"
    },
    "ChangeTagsForResource": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53",
        "event": "ChangeTagsForResource",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.resourceId",
        "source": "route53.amazonaws.com"
    },
    "CopySnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "CopySnapshot",
        "fallbackPathToId": "$.responseElements.snapshotId",
        "isDefault": null,
        "pathToId": "$.responseElements.snapshotId",
        "source": "elasticache.amazonaws.com"
    },
    "CreateAccessKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "CreateAccessKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "CreateAccessPoint": {
        "cspId": 2049,
        "entityType": "AWS_EFS",
        "event": "CreateAccessPoint",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "CreateAlias": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "CreateAlias",
        "fallbackPathToId": "$.requestParameters.targetKeyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.targetKeyId",
        "source": "kms.amazonaws.com"
    },
    "CreateAlias20150331": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "CreateAlias20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "CreateApi": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateApi",
        "fallbackPathToId": "$.responseElements.apiId",
        "isDefault": null,
        "pathToId": "$.responseElements.apiId",
        "source": "apigateway.amazonaws.com"
    },
    "CreateApiKey": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateApiKey",
        "fallbackPathToId": "$.responseElements.id",
        "isDefault": null,
        "pathToId": "$.responseElements.id",
        "source": "apigateway.amazonaws.com"
    },
    "CreateApiMapping": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateApiMapping",
        "fallbackPathToId": "$.requestParameters.apiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.apiId",
        "source": "apigateway.amazonaws.com"
    },
    "CreateBasePathMapping": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53_DOMAIN",
        "event": "CreateBasePathMapping",
        "fallbackPathToId": "$.requestParameters.domainName",
        "isDefault": null,
        "pathToId": "$.requestParameters.domainName",
        "source": "apigateway.amazonaws.com"
    },
    "CreateBucket": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "CreateBucket",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "CreateCacheCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "CreateCacheCluster",
        "fallbackPathToId": "$.requestParameters.cacheClusterId",
        "isDefault": null,
        "pathToId": "$.requestParameters.cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "CreateCacheParameterGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "CreateCacheParameterGroup",
        "fallbackPathToId": "$.responseElements.aRN",
        "isDefault": null,
        "pathToId": "$.responseElements.aRN",
        "source": "elasticache.amazonaws.com"
    },
    "CreateCacheSubnetGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateCacheSubnetGroup",
        "fallbackPathToId": "$.responseElements.vpcId",
        "isDefault": false,
        "pathToId": "$.responseElements.vpcId",
        "source": "elasticache.amazonaws.com"
    },
    "CreateChangeSet": {
        "cspId": 2049,
        "entityType": "CLOUDFORMATION",
        "event": "CreateChangeSet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "CreateClassifier": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_GLUE_SECURITY_CONFIGURATION",
        "event": "CreateClassifier",
        "fallbackPathToId": "$.requestParameters.jsonClassifier.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.jsonClassifier.name",
        "source": "glue.amazonaws.com"
    },
    "CreateCloudFrontOriginAccessIdentity": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "CreateCloudFrontOriginAccessIdentity",
        "fallbackPathToId": "$.requestParameters.id",
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateCluster": {
        "cspId": 2049,
        "entityType": "REDSHIFT",
        "event": "CreateCluster",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "CreateClusterSnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "CreateClusterSnapshot",
        "fallbackPathToId": "$.requestParameters.clusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "CreateCustomerGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_REGION",
        "event": "CreateCustomerGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.awsRegion",
        "source": "ec2.amazonaws.com"
    },
    "CreateDBCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "CreateDBCluster",
        "fallbackPathToId": "$.requestParameters.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "CreateDBClusterParameterGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "CreateDBClusterParameterGroup",
        "fallbackPathToId": "$.responseElements.dBClusterParameterGroupArn",
        "isDefault": null,
        "pathToId": "$.responseElements.dBClusterParameterGroupArn",
        "source": "rds.amazonaws.com"
    },
    "CreateDBClusterSnapshot": {
        "cspId": 2049,
        "entityType": "RDS_CLUSTER_SNAPSHOT",
        "event": "CreateDBClusterSnapshot",
        "fallbackPathToId": "$.responseElements.dBClusterSnapshotIdentifier",
        "isDefault": null,
        "pathToId": "$.responseElements.dBClusterSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "CreateDBInstance": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "CreateDBInstance",
        "fallbackPathToId": "$.responseElements.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.responseElements.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "CreateDBSnapshot": {
        "cspId": 2049,
        "entityType": "RDS_SNAPSHOT",
        "event": "CreateDBSnapshot",
        "fallbackPathToId": "$.responseElements.dBSnapshotIdentifier",
        "isDefault": null,
        "pathToId": "$.responseElements.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "CreateDefaultSubnet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_SUBNET",
        "event": "CreateDefaultSubnet",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.subnet.subnetId",
        "source": "ec2.amazonaws.com"
    },
    "CreateDefaultVpc": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateDefaultVpc",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.vpc.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateDistribution": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "CreateDistribution",
        "fallbackPathToId": "$.responseElements.distribution.id",
        "isDefault": false,
        "pathToId": "$.responseElements.distribution.id",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateDistributionWithTags": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "CreateDistributionWithTags",
        "fallbackPathToId": "$.responseElements.distribution.id",
        "isDefault": false,
        "pathToId": "$.responseElements.distribution.id",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateDomainName": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53_DOMAIN",
        "event": "CreateDomainName",
        "fallbackPathToId": "$.requestParameters.createDomainNameInput.domainName",
        "isDefault": null,
        "pathToId": "$.responseElements.domainName",
        "source": "apigateway.amazonaws.com"
    },
    "CreateElasticsearchDomain": {
        "cspId": 2049,
        "entityType": "AWS_ELASTIC_SEARCH",
        "event": "CreateElasticsearchDomain",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.domainName",
        "source": "es.amazonaws.com"
    },
    "CreateEventSourceMapping20150331": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "CreateEventSourceMapping20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "CreateEventSubscription": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "CreateEventSubscription",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "CreateFileSystem": {
        "cspId": 2049,
        "entityType": "AWS_EFS",
        "event": "CreateFileSystem",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "CreateFleet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "CreateFleet",
        "fallbackPathToId": "$.responseElements.CreateFleetResponse.fleetInstanceSet.item[*].instanceIds.item[*]",
        "isDefault": false,
        "pathToId": "$.responseElements.CreateFleetResponse.fleetInstanceSet.item[*].instanceIds.item[*]",
        "source": "ec2.amazonaws.com"
    },
    "CreateFlowLogs": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateFlowLogs",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.CreateFlowLogsRequest.ResourceId.content",
        "source": "ec2.amazonaws.com"
    },
    "CreateGrant": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "CreateGrant",
        "fallbackPathToId": "$.requestParameters.keyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "CreateGroup": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "CreateGroup",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "CreateHostedZone": {
        "cspId": 2049,
        "entityType": "ROUTE_53",
        "event": "CreateHostedZone",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.hostedZone.id",
        "source": "route53.amazonaws.com"
    },
    "CreateImage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "CreateImage",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.imageId",
        "source": "ec2.amazonaws.com"
    },
    "CreateInternetGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_IGW",
        "event": "CreateInternetGateway",
        "fallbackPathToId": "$.responseElements.internetGateway.internetGatewayId",
        "isDefault": false,
        "pathToId": "$.responseElements.internetGateway.internetGatewayId",
        "source": "ec2.amazonaws.com"
    },
    "CreateInvalidation": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "CreateInvalidation",
        "fallbackPathToId": "$.responseElements.invalidation.id",
        "isDefault": false,
        "pathToId": "$.requestParameters.distributionId",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateKey": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "CreateKey",
        "fallbackPathToId": "$.responseElements.keyMetadata.keyId",
        "isDefault": null,
        "pathToId": "$.responseElements.keyMetadata.keyId",
        "source": "kms.amazonaws.com"
    },
    "CreateLBCookieStickinessPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "CreateLBCookieStickinessPolicy",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateLaunchTemplateVersion": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "CreateLaunchTemplateVersion",
        "fallbackPathToId": "$.requestParameters.CreateLaunchTemplateVersionRequest.LaunchTemplateData.ImageId",
        "isDefault": false,
        "pathToId": "$.requestParameters.CreateLaunchTemplateVersionRequest.LaunchTemplateData.ImageId",
        "source": "ec2.amazonaws.com"
    },
    "CreateListener": {
        "cspId": 2049,
        "entityType": "ELB",
        "event": "CreateListener",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateLoadBalancer": {
        "cspId": 2049,
        "entityType": "ELB",
        "event": "CreateLoadBalancer",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateLoadBalancerListeners": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "CreateLoadBalancerListeners",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateLoadBalancerPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "CreateLoadBalancerPolicy",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateLoginProfile": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "CreateLoginProfile",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "CreateMonitoringSubscription": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUD_FRONT",
        "event": "CreateMonitoringSubscription",
        "fallbackPathToId": "$.requestParameters.distributionId",
        "isDefault": false,
        "pathToId": "$.requestParameters.distributionId",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateMountTarget": {
        "cspId": 2049,
        "entityType": "AWS_EFS",
        "event": "CreateMountTarget",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "CreateNamedQuery": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ATHENA_QUERY_EXECUTION",
        "event": "CreateNamedQuery",
        "fallbackPathToId": "$.responseElements.namedQueryId",
        "isDefault": null,
        "pathToId": "$.responseElements.namedQueryId",
        "source": "athena.amazonaws.com"
    },
    "CreateNatGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateNatGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.CreateNatGatewayResponse.natGateway.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateNetworkAcl": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateNetworkAcl",
        "fallbackPathToId": "$.requestParameters.vpcId",
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateNetworkAclEntry": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_NACL",
        "event": "CreateNetworkAclEntry",
        "fallbackPathToId": "$.requestParameters.networkAclId",
        "isDefault": false,
        "pathToId": "$.requestParameters.networkAclId",
        "source": "ec2.amazonaws.com"
    },
    "CreateNetworkInterface": {
        "cspId": 2049,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "CreateNetworkInterface",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.networkInterface.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "CreateOptionGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "CreateOptionGroup",
        "fallbackPathToId": "$.responseElements.optionGroupArn",
        "isDefault": null,
        "pathToId": "$.responseElements.optionGroupArn",
        "source": "rds.amazonaws.com"
    },
    "CreatePlatformApplication": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SNS",
        "event": "CreatePlatformApplication",
        "fallbackPathToId": "$.responseElements.platformApplicationArn",
        "isDefault": null,
        "pathToId": "$.responseElements.platformApplicationArn",
        "source": "sns.amazonaws.com"
    },
    "CreatePolicy": {
        "cspId": 2049,
        "entityType": "IAM_POLICY",
        "event": "CreatePolicy",
        "fallbackPathToId": "$.responseElements.policy.policyName",
        "isDefault": null,
        "pathToId": "$.responseElements.policy.arn",
        "source": "iam.amazonaws.com"
    },
    "CreatePolicyVersion": {
        "cspId": 2049,
        "entityType": "IAM_POLICY",
        "event": "CreatePolicyVersion",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.policy.policyArn",
        "source": "iam.amazonaws.com"
    },
    "CreateQueue": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "CreateQueue",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "CreateReplicationGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "CreateReplicationGroup",
        "fallbackPathToId": "$.responseElements.aRN",
        "isDefault": null,
        "pathToId": "$.responseElements.aRN",
        "source": "elasticache.amazonaws.com"
    },
    "CreateResource": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateResource",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "CreateRestApi": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateRestApi",
        "fallbackPathToId": "$.responseElements.id",
        "isDefault": null,
        "pathToId": "$.responseElements.id",
        "source": "apigateway.amazonaws.com"
    },
    "CreateRole": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "CreateRole",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParametersl.roleName",
        "source": "iam.amazonaws.com"
    },
    "CreateRoute": {
        "cspId": 2049,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "CreateRoute",
        "fallbackPathToId": "$.requestParameters.routeTableId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeTableId",
        "source": "apigateway.amazonaws.com"
    },
    "CreateRouteTable": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "CreateRouteTable",
        "fallbackPathToId": "$.responseElements.routeTable.routeTableId",
        "isDefault": false,
        "pathToId": "$.responseElements.routeTable.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "CreateRule": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "CreateRule",
        "fallbackPathToId": "$.requestParameters.listenerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.listenerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "CreateSAMLProvider": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "CreateSAMLProvider",
        "fallbackPathToId": "$.requestParameters.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "iam.amazonaws.com"
    },
    "CreateSecurityConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_GLUE_SECURITY_CONFIGURATION",
        "event": "CreateSecurityConfiguration",
        "fallbackPathToId": "$.requestParameters.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "glue.amazonaws.com"
    },
    "CreateSecurityGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SECURITY_GROUP",
        "event": "CreateSecurityGroup",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.groupId",
        "source": "ec2.amazonaws.com"
    },
    "CreateServiceLinkedRole": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_IAM_ROLE",
        "event": "CreateServiceLinkedRole",
        "fallbackPathToId": "$.responseElements.role.roleName",
        "isDefault": null,
        "pathToId": "$.responseElements.role.roleName",
        "source": "iam.amazonaws.com"
    },
    "CreateServiceSpecificCredential": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "CreateServiceSpecificCredential",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "CreateSnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_SNAPSHOT",
        "event": "CreateSnapshot",
        "fallbackPathToId": "$.responseElements.snapshotId",
        "isDefault": false,
        "pathToId": "$.responseElements.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "CreateSnapshots": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_SNAPSHOT",
        "event": "CreateSnapshots",
        "fallbackPathToId": "$.responseElements.CreateSnapshotsResponse.snapshotSet.item.snapshotId",
        "isDefault": false,
        "pathToId": "$.responseElements.CreateSnapshotsResponse.snapshotSet.item.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "CreateStack": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "CreateStack",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.stackId",
        "source": "cloudformation.amazonaws.com"
    },
    "CreateStackInstances": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "CreateStackInstances",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "CreateStage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "CreateStage",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "CreateStream": {
        "cspId": 2049,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "CreateStream",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "CreateStreamingDistribution": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "CreateStreamingDistribution",
        "fallbackPathToId": "$.responseElements.streamingDistribution.id",
        "isDefault": false,
        "pathToId": "$.responseElements.streamingDistribution.id",
        "source": "cloudfront.amazonaws.com"
    },
    "CreateSubnet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_SUBNET",
        "event": "CreateSubnet",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.subnet.subnetId",
        "source": "ec2.amazonaws.com"
    },
    "CreateTable": {
        "cspId": 2049,
        "entityType": "AWS_DYNAMO_DATABASE",
        "event": "CreateTable",
        "fallbackPathToId": "$.requestParameters.tableName",
        "isDefault": null,
        "pathToId": "$.requestParameters.tableName",
        "source": "dynamodb.amazonaws.com"
    },
    "CreateTags": {
        "cspId": 2049,
        "entityType": "REDSHIFT",
        "event": "CreateTags",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceName",
        "source": "redshift.amazonaws.com"
    },
    "CreateTopic": {
        "cspId": 2049,
        "entityType": "SNS",
        "event": "CreateTopic",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.topicArn",
        "source": "sns.amazonaws.com"
    },
    "CreateTrail": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "CreateTrail",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "cloudtrail.amazonaws.com"
    },
    "CreateTransitGatewayVpcAttachment": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateTransitGatewayVpcAttachment",
        "fallbackPathToId": "$.requestParameters.CreateTransitGatewayVpcAttachmentRequest.VpcId",
        "isDefault": false,
        "pathToId": "$.requestParameters.CreateTransitGatewayVpcAttachmentRequest.VpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateUser": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "CreateUser",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "CreateVPCAssociationAuthorization": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53",
        "event": "CreateVPCAssociationAuthorization",
        "fallbackPathToId": "$.requestParameters.hostedZoneId",
        "isDefault": false,
        "pathToId": "$.requestParameters.hostedZoneId",
        "source": "route53.amazonaws.com"
    },
    "CreateVolume": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_VOLUME",
        "event": "CreateVolume",
        "fallbackPathToId": "$.responseElements.volumeId",
        "isDefault": false,
        "pathToId": "$.responseElements.volumeId",
        "source": "ec2.amazonaws.com"
    },
    "CreateVpc": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateVpc",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.vpc.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateVpcEndpoint": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "CreateVpcEndpoint",
        "fallbackPathToId": "$.requestParameters.CreateVpcEndpointRequest.VpcId",
        "isDefault": false,
        "pathToId": "$.requestParameters.CreateVpcEndpointRequest.VpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateVpcEndpointServiceConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "CreateVpcEndpointServiceConfiguration",
        "fallbackPathToId": "$.requestParameters.CreateVpcEndpointServiceConfigurationRequest.NetworkLoadBalancerArn.content",
        "isDefault": null,
        "pathToId": "$.requestParameters.CreateVpcEndpointServiceConfigurationRequest.NetworkLoadBalancerArn.content",
        "source": "ec2.amazonaws.com"
    },
    "CreateVpcPeeringConnection": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_VPC_PEERING_CONNECTION",
        "event": "CreateVpcPeeringConnection",
        "fallbackPathToId": "$.responseElements.vpcPeeringConnection.vpcPeeringConnectionId",
        "isDefault": false,
        "pathToId": "$.responseElements.vpcPeeringConnection.vpcPeeringConnectionId",
        "source": "ec2.amazonaws.com"
    },
    "DeactivateMFADevice": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "DeactivateMFADevice",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DecreaseReplicaCount": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "DecreaseReplicaCount",
        "fallbackPathToId": "$.responseElements.nodeGroups[*].nodeGroupMembers[*].cacheClusterId",
        "isDefault": null,
        "pathToId": "$.responseElements.nodeGroups[*].nodeGroupMembers[*].cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "DeleteAccessKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DeleteAccessKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteAccountPasswordPolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DeleteAccountPasswordPolicy",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteAlias": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "KMS",
        "event": "DeleteAlias",
        "fallbackPathToId": "$.resources[*].ARN",
        "isDefault": null,
        "pathToId": "$.resources[*].ARN",
        "source": "kms.amazonaws.com"
    },
    "DeleteAlias20150331": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "LAMBDA",
        "event": "DeleteAlias20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "DeleteApi": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteApi",
        "fallbackPathToId": "$.requestParameters.apiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.apiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteAuthorizer": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteAuthorizer",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteBasePathMapping": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53_DOMAIN",
        "event": "DeleteBasePathMapping",
        "fallbackPathToId": "$.requestParameters.domainName",
        "isDefault": null,
        "pathToId": "$.requestParameters.domainName",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteBucket": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "S3",
        "event": "DeleteBucket",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketEncryption": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "DeleteBucketEncryption",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketLifecycle": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "DeleteBucketLifecycle",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketOwnershipControls": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "DeleteBucketOwnershipControls",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketPolicy": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "DeleteBucketPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketPublicAccessBlock": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "DeleteBucketPublicAccessBlock",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketReplication": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "S3",
        "event": "DeleteBucketReplication",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteBucketTagging": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "DeleteBucketTagging",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "DeleteCacheCluster": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "DeleteCacheCluster",
        "fallbackPathToId": "$.requestParameters.cacheClusterId",
        "isDefault": null,
        "pathToId": "$.requestParameters.cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "DeleteChangeSet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "DeleteChangeSet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "DeleteClassifier": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_GLUE_SECURITY_CONFIGURATION",
        "event": "DeleteClassifier",
        "fallbackPathToId": "$.requestParameters.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "glue.amazonaws.com"
    },
    "DeleteCloudFrontOriginAccessIdentity": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "DeleteCloudFrontOriginAccessIdentity",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "DeleteCluster": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "REDSHIFT",
        "event": "DeleteCluster",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "DeleteClusterSnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "DeleteClusterSnapshot",
        "fallbackPathToId": "$.responseElements.clusterIdentifier",
        "isDefault": null,
        "pathToId": "$.responseElements.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "DeleteCrawler": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_GLUE_SECURITY_CONFIGURATION",
        "event": "DeleteCrawler",
        "fallbackPathToId": "$.requestParameters.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "glue.amazonaws.com"
    },
    "DeleteCustomerGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_REGION",
        "event": "DeleteCustomerGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.awsRegion",
        "source": "ec2.amazonaws.com"
    },
    "DeleteDBCluster": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "RDS_CLUSTER",
        "event": "DeleteDBCluster",
        "fallbackPathToId": "$.requestParameters.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "DeleteDBClusterSnapshot": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "RDS_CLUSTER_SNAPSHOT",
        "event": "DeleteDBClusterSnapshot",
        "fallbackPathToId": "$.requestParameters.dBClusterSnapshotIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "DeleteDBInstance": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "RDS",
        "event": "DeleteDBInstance",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "DeleteDBSnapshot": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "RDS_SNAPSHOT",
        "event": "DeleteDBSnapshot",
        "fallbackPathToId": "$.requestParameters.dBSnapshotIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "DeleteDeployment": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteDeployment",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteDistribution": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "CLOUD_FRONT",
        "event": "DeleteDistribution",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "DeleteDomainName": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "ROUTE_53_DOMAIN",
        "event": "DeleteDomainName",
        "fallbackPathToId": "$.requestParameters.domainName",
        "isDefault": null,
        "pathToId": "$.requestParameters.domainName",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteElasticsearchDomain": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_ELASTIC_SEARCH",
        "event": "DeleteElasticsearchDomain",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.domainId",
        "source": "es.amazonaws.com"
    },
    "DeleteEventSourceMapping20150331": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "LAMBDA",
        "event": "DeleteEventSourceMapping20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.functionArn",
        "source": "lambda.amazonaws.com"
    },
    "DeleteEventSubscription": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "DeleteEventSubscription",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "DeleteFileSystem": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_EFS",
        "event": "DeleteFileSystem",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "DeleteFileSystemPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "DeleteFileSystemPolicy",
        "fallbackPathToId": "$.requestParameters.fileSystemId",
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "DeleteFlowLogs": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "DeleteFlowLogs",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "CreateFunction20150331":{
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "DeleteFunction20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "DeleteFunction20150331": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "LAMBDA",
        "event": "DeleteFunction20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "DeleteGroup": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DeleteGroup",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteGroupPolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DeleteGroupPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteHostedZone": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "ROUTE_53",
        "event": "DeleteHostedZone",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "route53.amazonaws.com"
    },
    "DeleteImportedKeyMaterial": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "DeleteImportedKeyMaterial",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "DeleteIntegration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteIntegration",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteIntegrationResponse": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteIntegrationResponse",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteInternetGateway": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_IGW",
        "event": "DeleteInternetGateway",
        "fallbackPathToId": "$.requestParameters.internetGatewayId",
        "isDefault": false,
        "pathToId": "$.requestParameters.internetGatewayId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteKey": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "KMS",
        "event": "DeleteKey",
        "fallbackPathToId": "$.resources[*].ARN",
        "isDefault": null,
        "pathToId": "$.resources[*].ARN",
        "source": "kms.amazonaws.com"
    },
    "DeleteListener": {
        "cspId": 2049,
        "entityType": "ELB",
        "event": "DeleteListener",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DeleteLoadBalancer": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "ELB",
        "event": "DeleteLoadBalancer",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DeleteLoadBalancerListeners": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "ELB",
        "event": "DeleteLoadBalancerListeners",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DeleteLoadBalancerPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "DeleteLoadBalancerPolicy",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DeleteLoginProfile": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DeleteLoginProfile",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteMethod": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteMethod",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteMethodResponse": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteMethodResponse",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteModel": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteModel",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteMountTarget": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "DeleteMountTarget",
        "fallbackPathToId": "$.requestParameters.mountTargetId",
        "isDefault": null,
        "pathToId": "$.requestParameters.mountTargetId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "DeleteNamedQuery": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "ATHENA_QUERY_EXECUTION",
        "event": "DeleteNamedQuery",
        "fallbackPathToId": "$.requestParameters.namedQueryId",
        "isDefault": null,
        "pathToId": "$.requestParameters.namedQueryId",
        "source": "athena.amazonaws.com"
    },
    "DeleteNatGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "DeleteNatGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteNetworkAcl": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_NACL",
        "event": "DeleteNetworkAcl",
        "fallbackPathToId": "$.requestParameters.networkAclId",
        "isDefault": false,
        "pathToId": "$.requestParameters.networkAclId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteNetworkAclEntry": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_NACL",
        "event": "DeleteNetworkAclEntry",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkAclId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteNetworkInterface": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "DeleteNetworkInterface",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "DeletePolicy": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "IAM_POLICY",
        "event": "DeletePolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.policyArn",
        "source": "iam.amazonaws.com"
    },
    "DeletePolicyVersion": {
        "cspId": 2049,
        "entityType": "IAM_POLICY",
        "event": "DeletePolicyVersion",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.policyArn",
        "source": "iam.amazonaws.com"
    },
    "DeleteProvisionedConcurrencyConfig": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "DeleteProvisionedConcurrencyConfig",
        "fallbackPathToId": "$.requestParameters.functionName",
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "DeleteQueue": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "SQS",
        "event": "DeleteQueue",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "DeleteRequestValidator": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteRequestValidator",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteResource": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteResource",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteRestApi": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteRestApi",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteRole": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DeleteRole",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteRolePermissionsBoundary": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "IAM",
        "event": "DeleteRolePermissionsBoundary",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteRolePolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DeleteRolePolicy",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DeleteRoute": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "DeleteRoute",
        "fallbackPathToId": "$.requestParameters.routeId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteRouteTable": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "DeleteRouteTable",
        "fallbackPathToId": "$.requestParameters.routeTableId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteRule": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "DeleteRule",
        "fallbackPathToId": "$.requestParameters.ruleArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.ruleArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DeleteSSHPublicKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DeleteSSHPublicKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteSecurityConfiguration": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_GLUE_SECURITY_CONFIGURATION",
        "event": "DeleteSecurityConfiguration",
        "fallbackPathToId": "$.requestParameters.name",
        "isDefault": null,
        "pathToId": "$.requestParameters.name",
        "source": "glue.amazonaws.com"
    },
    "DeleteSecurityGroup": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "SECURITY_GROUP",
        "event": "DeleteSecurityGroup",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.groupId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteServerCertificate": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_IAM_SERVER_CERTIFICATE",
        "event": "DeleteServerCertificate",
        "fallbackPathToId": "$.requestParameters.serverCertificateName",
        "isDefault": null,
        "pathToId": "$.requestParameters.serverCertificateName",
        "source": "iam.amazonaws.com"
    },
    "DeleteServiceLinkedRole": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_IAM_ROLE",
        "event": "DeleteServiceLinkedRole",
        "fallbackPathToId": "$.requestParameters.roleName",
        "isDefault": null,
        "pathToId": "$.requestParameters.roleName",
        "source": "iam.amazonaws.com"
    },
    "DeleteServiceSpecificCredential": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DeleteServiceSpecificCredential",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteSnapshot": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "EBS_SNAPSHOT",
        "event": "DeleteSnapshot",
        "fallbackPathToId": "$.requestParameters.snapshotId",
        "isDefault": false,
        "pathToId": "$.requestParameters.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteStack": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "CLOUDFORMATION",
        "event": "DeleteStack",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "DeleteStackInstances": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "DeleteStackInstances",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "DeleteStackSet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "DeleteStackSet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "DeleteStage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "DeleteStage",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "DeleteStream": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "DeleteStream",
        "fallbackPathToId": "$.requestParameters.streamName",
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "DeleteSubnet": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_SUBNET",
        "event": "DeleteSubnet",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.subnetId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteTable": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AWS_DYNAMO_DATABASE",
        "event": "DeleteTable",
        "fallbackPathToId": "$.requestParameters.tableName",
        "isDefault": null,
        "pathToId": "$.requestParameters.tableName",
        "source": "dynamodb.amazonaws.com"
    },
    "DeleteTags": {
        "cspId": 2049,
        "entityType": "REDSHIFT",
        "event": "DeleteTags",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceName",
        "source": "redshift.amazonaws.com"
    },
    "DeleteTopic": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "SNS",
        "event": "DeleteTopic",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.topicArn",
        "source": "sns.amazonaws.com"
    },
    "DeleteTrail": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "CLOUD_TRAIL",
        "event": "DeleteTrail",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "cloudtrail.amazonaws.com"
    },
    "DeleteUser": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "USER",
        "event": "DeleteUser",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteUserPermissionsBoundary": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "DeleteUserPermissionsBoundary",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteUserPolicy": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DeleteUserPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DeleteVPCAssociationAuthorization": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53",
        "event": "DeleteVPCAssociationAuthorization",
        "fallbackPathToId": "$.requestParameters.hostedZoneId",
        "isDefault": false,
        "pathToId": "$.requestParameters.hostedZoneId",
        "source": "route53.amazonaws.com"
    },
    "DeleteVolume": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "EBS_VOLUME",
        "event": "DeleteVolume",
        "fallbackPathToId": "$.requestParameters.volumeId",
        "isDefault": false,
        "pathToId": "$.requestParameters.volumeId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteVpc": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "VPC",
        "event": "DeleteVpc",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "DeleteVpcPeeringConnection": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_VPC_PEERING_CONNECTION",
        "event": "DeleteVpcPeeringConnection",
        "fallbackPathToId": "$.responseElements.vpcPeeringConnection.vpcPeeringConnectionId",
        "isDefault": false,
        "pathToId": "$.responseElements.vpcPeeringConnection.vpcPeeringConnectionId",
        "source": "ec2.amazonaws.com"
    },
    "DeregisterImage": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "AMI",
        "event": "DeregisterImage",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.imageId",
        "source": "ec2.amazonaws.com"
    },
    "DeregisterInstancesFromLoadBalancer": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "DeregisterInstancesFromLoadBalancer",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DetachGroupPolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DetachGroupPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DetachLoadBalancerFromSubnets": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "DetachLoadBalancerFromSubnets",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "DetachRolePolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "DetachRolePolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "DetachUserPolicy": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "DetachUserPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "DetachVolume": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_VOLUME",
        "event": "DetachVolume",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.volumeId",
        "source": "ec2.amazonaws.com"
    },
    "DetachVpnGateway": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "DetachVpnGateway",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "DisableKey": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "DisableKey",
        "fallbackPathToId": "$.requestParameters.keyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "DisableKeyRotation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "KMS",
        "event": "DisableKeyRotation",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "DisassociateAddress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "DisassociateAddress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.associationId",
        "source": "ec2.amazonaws.com"
    },
    "DisassociateIamInstanceProfile": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "DisassociateIamInstanceProfile",
        "fallbackPathToId": "$.responseElements.iamInstanceProfileAssociation.instanceId",
        "isDefault": false,
        "pathToId": "$.responseElements.iamInstanceProfileAssociation.instanceId",
        "source": "ec2.amazonaws.com"
    },
    "DisassociateRouteTable": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "DisassociateRouteTable",
        "fallbackPathToId": "$.requestParameters.associationId",
        "isDefault": false,
        "pathToId": "$.requestParameters.associationId",
        "source": "ec2.amazonaws.com"
    },
    "DisassociateVPCFromHostedZone": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53",
        "event": "DisassociateVPCFromHostedZone",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.hostedZoneId",
        "source": "route53.amazonaws.com"
    },
    "DownloadDBLogFilePortion": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "DownloadDBLogFilePortion",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "EnableEnhancedMonitoring": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "EnableEnhancedMonitoring",
        "fallbackPathToId": "$.requestParameters.streamName",
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "EnableKey": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "EnableKey",
        "fallbackPathToId": "$.requestParameters.keyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "EnableKeyRotation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "KMS",
        "event": "EnableKeyRotation",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "EnableLogging": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "EnableLogging",
        "fallbackPathToId": "$.requestParameters.clusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "EnableMFADevice": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "EnableMFADevice",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "EnableRule": {
        "cspId": 2049,
        "entityType": "CLOUD_WATCH",
        "event": "EnableRule",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "events.amazonaws.com"
    },
    "EnableVgwRoutePropagation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "EnableVgwRoutePropagation",
        "fallbackPathToId": "$.responseElements.routeTableId",
        "isDefault": false,
        "pathToId": "$.responseElements.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "ExecuteChangeSet": {
        "cspId": 2049,
        "entityType": "CLOUDFORMATION",
        "event": "ExecuteChangeSet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "ExportImage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "ExportImage",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.ExportImageRequest.ImageId",
        "source": "ec2.amazonaws.com"
    },
    "ImportApi": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "ImportApi",
        "fallbackPathToId": "$.responseElements.apiId",
        "isDefault": null,
        "pathToId": "$.responseElements.apiId",
        "source": "apigateway.amazonaws.com"
    },
    "IncreaseReplicaCount": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "IncreaseReplicaCount",
        "fallbackPathToId": "$.responseElements.nodeGroups.nodeGroupMembers[*].cacheClusterId",
        "isDefault": null,
        "pathToId": "$.responseElements.nodeGroups.nodeGroupMembers[*].cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "LambdaESMDisabled": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "LambdaESMDisabled",
        "fallbackPathToId": "$.resources[*].ARN",
        "isDefault": null,
        "pathToId": "$.resources[*].ARN",
        "source": "lambda.amazonaws.com"
    },
    "ModifyCacheCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "ModifyCacheCluster",
        "fallbackPathToId": "$.requestParameters.cacheClusterId",
        "isDefault": null,
        "pathToId": "$.requestParameters.cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "ModifyCluster": {
        "cspId": 2049,
        "entityType": "REDSHIFT",
        "event": "ModifyCluster",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "ModifyClusterIamRoles": {
        "cspId": 2049,
        "entityType": "REDSHIFT",
        "event": "ModifyClusterIamRoles",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "ModifyDBCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "ModifyDBCluster",
        "fallbackPathToId": "$.responseElements.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ModifyDBClusterSnapshotAttribute": {
        "cspId": 2049,
        "entityType": "RDS_CLUSTER_SNAPSHOT",
        "event": "ModifyDBClusterSnapshotAttribute",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ModifyDBInstance": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "ModifyDBInstance",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ModifyDBSnapshotAttribute": {
        "cspId": 2049,
        "entityType": "RDS_SNAPSHOT",
        "event": "ModifyDBSnapshotAttribute",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ModifyDBSubnetGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "ModifyDBSubnetGroup",
        "fallbackPathToId": "$.responseElements.vpcId",
        "isDefault": false,
        "pathToId": "$.responseElements.vpcId",
        "source": "elasticache.amazonaws.com"
    },
    "ModifyEbsDefaultKmsKeyId": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "KMS",
        "event": "ModifyEbsDefaultKmsKeyId",
        "fallbackPathToId": "$.responseElements.ModifyEbsDefaultKmsKeyIdResponse.kmsKeyId",
        "isDefault": null,
        "pathToId": "$.responseElements.ModifyEbsDefaultKmsKeyIdResponse.kmsKeyId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyEventSubscription": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "ModifyEventSubscription",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.dBSnapshotIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ModifyImageAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "ModifyImageAttribute",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.imageId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyInstanceAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "ModifyInstanceAttribute",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.instanceId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyInstanceCreditSpecification": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "ModifyInstanceCreditSpecification",
        "fallbackPathToId": "$.requestParameters.ModifyInstanceCreditSpecificationRequest.InstanceCreditSpecification.InstanceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.ModifyInstanceCreditSpecificationRequest.InstanceCreditSpecification.InstanceId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyInstanceFleet": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "ModifyInstanceFleet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "ModifyListener": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "ModifyListener",
        "fallbackPathToId": "$.requestParameters.listenerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.listenerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "ModifyLoadBalancerAttributes": {
        "cspId": 2049,
        "entityType": "ELB",
        "event": "ModifyLoadBalancerAttributes",
        "fallbackPathToId": "$.requestParameters.loadBalancerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "ModifyMountTargetSecurityGroups": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "ModifyMountTargetSecurityGroups",
        "fallbackPathToId": "$.requestParameters.mountTargetId",
        "isDefault": null,
        "pathToId": "$.requestParameters.mountTargetId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "ModifyNetworkInterfaceAttribute": {
        "cspId": 2049,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "ModifyNetworkInterfaceAttribute",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyReplicationGroup": {
        "cspId": 2049,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "ModifyReplicationGroup",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceName",
        "source": "elasticache.amazonaws.com"
    },
    "ModifyReplicationGroupShardConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ELASTI_CACHE",
        "event": "ModifyReplicationGroupShardConfiguration",
        "fallbackPathToId": "$.responseElements.nodeGroups[*].nodeGroupMembers[*].cacheClusterId",
        "isDefault": null,
        "pathToId": "$.responseElements.nodeGroups[*].nodeGroupMembers[*].cacheClusterId",
        "source": "elasticache.amazonaws.com"
    },
    "ModifyRule": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "ModifyRule",
        "fallbackPathToId": "$.requestParameters.ruleArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.ruleArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "ModifySnapshotAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_SNAPSHOT",
        "event": "ModifySnapshotAttribute",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "ModifySubnetAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_SUBNET",
        "event": "ModifySubnetAttribute",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.subnetId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyVolume": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_VOLUME",
        "event": "ModifyVolume",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.ModifyVolumeRequest.VolumeId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyVolumeAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_VOLUME",
        "event": "ModifyVolumeAttribute",
        "fallbackPathToId": "$.requestParameters.volumeId",
        "isDefault": false,
        "pathToId": "$.requestParameters.volumeId",
        "source": "ec2.amazonaws.com"
    },
    "ModifyVpcAttribute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "VPC",
        "event": "ModifyVpcAttribute",
        "fallbackPathToId": "$.requestParameters.vpcId",
        "isDefault": false,
        "pathToId": "$.requestParameters.vpcId",
        "source": "ec2.amazonaws.com"
    },
    "PurgeQueue": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "PurgeQueue",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "PutAutoScalingPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EMR_CLUSTER",
        "event": "PutAutoScalingPolicy",
        "fallbackPathToId": "$.requestParameters.clusterId",
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "PutAutoTerminationPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EMR_CLUSTER",
        "event": "PutAutoTerminationPolicy",
        "fallbackPathToId": "$.requestParameters.clusterId",
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "PutBackupPolicy": {
        "cspId": 2049,
        "entityType": "AWS_EFS",
        "event": "PutBackupPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "PutBucketAcl": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketAcl",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketCors": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketCors",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketEncryption": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketEncryption",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketInventoryConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketInventoryConfiguration",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketLifecycle": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketLifecycle",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketLogging": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketLogging",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketMetricsConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketMetricsConfiguration",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketNotification": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketNotification",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketOwnershipControls": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketOwnershipControls",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketPolicy": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketPublicAccessBlock": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketPublicAccessBlock",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketReplication": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketReplication",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketTagging": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketTagging",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketVersioning": {
        "cspId": 2049,
        "entityType": "S3",
        "event": "PutBucketVersioning",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutBucketWebsite": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutBucketWebsite",
        "fallbackPathToId": "$.requestParameters.bucketName",
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutConfigRule": {
        "cspId": 2049,
        "entityType": "AWS_REGION",
        "event": "PutConfigRule",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.awsRegion",
        "source": "config.amazonaws.com"
    },
    "PutConfigurationRecorder": {
        "cspId": 2049,
        "entityType": "AWS_REGION",
        "event": "PutConfigurationRecorder",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.awsRegion",
        "source": "config.amazonaws.com"
    },
    "PutDeliveryChannel": {
        "cspId": 2049,
        "entityType": "AWS_REGION",
        "event": "PutDeliveryChannel",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.awsRegion",
        "source": "config.amazonaws.com"
    },
    "PutEventSelectors": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "PutEventSelectors",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.trailName",
        "source": "cloudtrail.amazonaws.com"
    },
    "PutFileSystemPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "PutFileSystemPolicy",
        "fallbackPathToId": "$.requestParameters.fileSystemId",
        "isDefault": null,
        "pathToId": "$.requestParameters.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "PutFunctionCodeSigningConfig": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "PutFunctionCodeSigningConfig",
        "fallbackPathToId": "$.requestParameters.functionName",
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "PutGroupPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "IAM_POLICY",
        "event": "PutGroupPolicy",
        "fallbackPathToId": "$.requestParameters.policyName",
        "isDefault": null,
        "pathToId": "$.requestParameters.policyName",
        "source": "iam.amazonaws.com"
    },
    "PutInsightSelectors": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "PutInsightSelectors",
        "fallbackPathToId": "$.requestParameters.trailName",
        "isDefault": false,
        "pathToId": "$.responseElements.trailARN",
        "source": "cloudtrail.amazonaws.com"
    },
    "PutIntegration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "PutIntegration",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "PutLifecycleConfiguration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "PutLifecycleConfiguration",
        "fallbackPathToId": "$.responseElements.fileSystemId",
        "isDefault": null,
        "pathToId": "$.responseElements.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "PutManagedScalingPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EMR_CLUSTER",
        "event": "PutManagedScalingPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "PutMethod": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "PutMethod",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "PutMethodResponse": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "PutMethodResponse",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "PutObjectTagging": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "S3",
        "event": "PutObjectTagging",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.bucketName",
        "source": "s3.amazonaws.com"
    },
    "PutProvisionedConcurrencyConfig": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "PutProvisionedConcurrencyConfig",
        "fallbackPathToId": "$.resources.functionName",
        "isDefault": null,
        "pathToId": "$.resources.functionName",
        "source": "lambda.amazonaws.com"
    },
    "PutRestApi": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "PutRestApi",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "PutRolePermissionsBoundary": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "IAM",
        "event": "PutRolePermissionsBoundary",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "PutRolePolicy": {
        "cspId": 2049,
        "entityType": "IAM_POLICY",
        "event": "PutRolePolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.policy.policyName",
        "source": "iam.amazonaws.com"
    },
    "PutRule": {
        "cspId": 2049,
        "entityType": "CLOUD_WATCH",
        "event": "PutRule",
        "fallbackPathToId": "$.responseElements.ruleArn",
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "events.amazonaws.com"
    },
    "PutTargets": {
        "cspId": 2049,
        "entityType": "CLOUD_WATCH",
        "event": "PutTargets",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.rule",
        "source": "events.amazonaws.com"
    },
    "PutUserPermissionsBoundary": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "PutUserPermissionsBoundary",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "PutUserPolicy": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "PutUserPolicy",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "RebootCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "RebootCluster",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "RebootDBInstance": {
        "cspId": 2049,
        "entityType": "RDS",
        "event": "RebootDBInstance",
        "fallbackPathToId": "$.responseElements.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.responseElements.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "RegisterImage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "RegisterImage",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.imageId",
        "source": "ec2.amazonaws.com"
    },
    "RegisterInstancesWithLoadBalancer": {
        "cspId": 2049,
        "entityType": "ELB",
        "event": "RegisterInstancesWithLoadBalancer",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "ReleaseAddress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_REGION",
        "event": "ReleaseAddress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.allocationId",
        "source": "ec2.amazonaws.com"
    },
    "RemoveListenerCertificates": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "RemoveListenerCertificates",
        "fallbackPathToId": "$.requestParameters.listenerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.listenerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "RemovePermission": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "RemovePermission",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "RemoveRoleFromDBCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS_CLUSTER",
        "event": "RemoveRoleFromDBCluster",
        "fallbackPathToId": "$.requestParameters.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "RemoveRoleFromDBInstance": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS",
        "event": "RemoveRoleFromDBInstance",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "RemoveTags": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "RemoveTags",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.resourceId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "RemoveTagsFromStream": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "RemoveTagsFromStream",
        "fallbackPathToId": "$.requestParameters.streamName",
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "RemoveTargets": {
        "cspId": 2049,
        "entityType": "CLOUD_WATCH",
        "event": "RemoveTargets",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.rule",
        "source": "events.amazonaws.com"
    },
    "ReplaceIamInstanceProfileAssociation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "ReplaceIamInstanceProfileAssociation",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.ReplaceIamInstanceProfileAssociationResponse.iamInstanceProfileAssociation.instanceId",
        "source": "ec2.amazonaws.com"
    },
    "ReplaceNetworkAclAssociation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_NACL",
        "event": "ReplaceNetworkAclAssociation",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkAclId",
        "source": "ec2.amazonaws.com"
    },
    "ReplaceNetworkAclEntry": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_NACL",
        "event": "ReplaceNetworkAclEntry",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.networkAclId",
        "source": "ec2.amazonaws.com"
    },
    "ReplaceRoute": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "ReplaceRoute",
        "fallbackPathToId": "$.requestParameters.routeTableId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "ReplaceRouteTableAssociation": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "ReplaceRouteTableAssociation",
        "fallbackPathToId": "$.requestParameters.routeTableId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeTableId",
        "source": "ec2.amazonaws.com"
    },
    "ReportInstanceStatus": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "ReportInstanceStatus",
        "fallbackPathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "source": "ec2.amazonaws.com"
    },
    "RequestSpotInstances": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AMI",
        "event": "RequestSpotInstances",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.launchSpecification.imageId",
        "source": "ec2.amazonaws.com"
    },
    "ResetServiceSpecificCredential": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "ResetServiceSpecificCredential",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "ResizeCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "ResizeCluster",
        "fallbackPathToId": "$.requestParameters.clusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "RestoreDBClusterFromSnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS",
        "event": "RestoreDBClusterFromSnapshot",
        "fallbackPathToId": "$.requestParameters.dBClusterIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBClusterIdentifier",
        "source": "rds.amazonaws.com"
    },
    "RestoreDBInstanceFromDBSnapshot": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "RDS",
        "event": "RestoreDBInstanceFromDBSnapshot",
        "fallbackPathToId": "$.requestParameters.dBInstanceIdentifier",
        "isDefault": null,
        "pathToId": "$.requestParameters.dBInstanceIdentifier",
        "source": "rds.amazonaws.com"
    },
    "ResumeCluster": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "REDSHIFT",
        "event": "ResumeCluster",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.clusterIdentifier",
        "source": "redshift.amazonaws.com"
    },
    "ResyncMFADevice": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "ResyncMFADevice",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "RetireGrant": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "RetireGrant",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resources[*].ARN",
        "source": "kms.amazonaws.com"
    },
    "RevokeSecurityGroupEgress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SECURITY_GROUP",
        "event": "RevokeSecurityGroupEgress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.groupId",
        "source": "ec2.amazonaws.com"
    },
    "RevokeSecurityGroupIngress": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SECURITY_GROUP",
        "event": "RevokeSecurityGroupIngress",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.groupId",
        "source": "ec2.amazonaws.com"
    },
    "RunInstances": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "RunInstances",
        "fallbackPathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "isDefault": false,
        "pathToId": "$.responseElements.instancesSet.items[*].instanceId",
        "source": "ec2.amazonaws.com"
    },
    "RunJobFlow": {
        "cspId": 2049,
        "entityType": "EMR_CLUSTER",
        "event": "RunJobFlow",
        "fallbackPathToId": "$.responseElements.jobFlowId",
        "isDefault": null,
        "pathToId": "$.requestParameters.jobFlowId",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "ScheduleKeyDeletion": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "ScheduleKeyDeletion",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "SetDefaultPolicyVersion": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "IAM_POLICY",
        "event": "SetDefaultPolicyVersion",
        "fallbackPathToId": "$.requestParameters.policyArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.policyArn",
        "source": "iam.amazonaws.com"
    },
    "SetIpAddressType": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetIpAddressType",
        "fallbackPathToId": "$.requestParameters.loadBalancerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetLoadBalancerListenerSSLCertificate": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetLoadBalancerListenerSSLCertificate",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetLoadBalancerPoliciesForBackendServer": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetLoadBalancerPoliciesForBackendServer",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetLoadBalancerPoliciesOfListener": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetLoadBalancerPoliciesOfListener",
        "fallbackPathToId": "$.requestParameters.loadBalancerName",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerName",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetPlatformApplicationAttributes": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SNS",
        "event": "SetPlatformApplicationAttributes",
        "fallbackPathToId": "$.requestParameters.platformApplicationArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.platformApplicationArn",
        "source": "sns.amazonaws.com"
    },
    "SetQueueAttributes": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "SetQueueAttributes",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "SetSecurityGroups": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetSecurityGroups",
        "fallbackPathToId": "$.requestParameters.loadBalancerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetSubnets": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ELB",
        "event": "SetSubnets",
        "fallbackPathToId": "$.requestParameters.loadBalancerArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.loadBalancerArn",
        "source": "elasticloadbalancing.amazonaws.com"
    },
    "SetSubscriptionAttributes": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "SNS",
        "event": "SetSubscriptionAttributes",
        "fallbackPathToId": "$.requestParameters.subscriptionArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.subscriptionArn",
        "source": "sns.amazonaws.com"
    },
    "SetTerminationProtection": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EMR_CLUSTER",
        "event": "SetTerminationProtection",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.jobFlowIds[*]",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "SetTopicAttributes": {
        "cspId": 2049,
        "entityType": "SNS",
        "event": "SetTopicAttributes",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.topicArn",
        "source": "sns.amazonaws.com"
    },
    "SharedSnapshotCopyInitiated": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_SNAPSHOT",
        "event": "SharedSnapshotCopyInitiated",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.serviceEventDetails.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "SharedSnapshotVolumeCreated": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EBS_SNAPSHOT",
        "event": "SharedSnapshotVolumeCreated",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.serviceEventDetails.snapshotId",
        "source": "ec2.amazonaws.com"
    },
    "StartLogging": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "StartLogging",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "cloudtrail.amazonaws.com"
    },
    "StopLogging": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "StopLogging",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.name",
        "source": "cloudtrail.amazonaws.com"
    },
    "TagPolicy": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "IAM_POLICY",
        "event": "TagPolicy",
        "fallbackPathToId": "$.requestParameters.policyArn",
        "isDefault": null,
        "pathToId": "$.requestParameters.policyArn",
        "source": "iam.amazonaws.com"
    },
    "TagQueue": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "TagQueue",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "TagResource": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "TagResource",
        "fallbackPathToId": "$.requestParameters.keyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "TagUser": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "TagUser",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "TerminateInstances": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "EC2",
        "event": "TerminateInstances",
        "fallbackPathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "source": "ec2.amazonaws.com"
    },
    "TerminateJobFlows": {
        "cspId": 2049,
        "doDelete": true,
        "entityType": "EMR_CLUSTER",
        "event": "TerminateJobFlows",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.jobFlowIds[*]",
        "source": "elasticmapreduce.amazonaws.com"
    },
    "UnassignPrivateIpAddresses": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_NETWORK_INTERFACE",
        "event": "UnassignPrivateIpAddresses",
        "fallbackPathToId": "$.requestParameters.networkInterfaceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.networkInterfaceId",
        "source": "ec2.amazonaws.com"
    },
    "UnmonitorInstances": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "EC2",
        "event": "UnmonitorInstances",
        "fallbackPathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "isDefault": false,
        "pathToId": "$.requestParameters.instancesSet.items[*].instanceId",
        "source": "ec2.amazonaws.com"
    },
    "Unsubscribe": {
        "cspId": 2049,
        "entityType": "SNS",
        "event": "Unsubscribe",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.subscriptionArn",
        "source": "sns.amazonaws.com"
    },
    "UntagQueue": {
        "cspId": 2049,
        "entityType": "SQS",
        "event": "UntagQueue",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.queueUrl",
        "source": "sqs.amazonaws.com"
    },
    "UntagResource": {
        "cspId": 2049,
        "entityType": "KMS",
        "event": "UntagResource",
        "fallbackPathToId": "$.requestParameters.keyId",
        "isDefault": null,
        "pathToId": "$.requestParameters.keyId",
        "source": "kms.amazonaws.com"
    },
    "UpdateAccessKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "UpdateAccessKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "UpdateAccountPasswordPolicy": {
        "cspId": 2049,
        "entityType": "IAM",
        "event": "UpdateAccountPasswordPolicy",
        "fallbackPathToId": "$.recipientAccountId",
        "isDefault": null,
        "pathToId": "$.recipientAccountId",
        "source": "iam.amazonaws.com"
    },
    "UpdateAlias20150331": {
        "cspId": 2049,
        "entityType": "LAMBDA",
        "event": "UpdateAlias20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "UpdateBasePathMapping": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "ROUTE_53_DOMAIN",
        "event": "UpdateBasePathMapping",
        "fallbackPathToId": "$.requestParameters.domainName",
        "isDefault": null,
        "pathToId": "$.requestParameters.domainName",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateCloudFrontOriginAccessIdentity": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "UpdateCloudFrontOriginAccessIdentity",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "UpdateDeployment": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateDeployment",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateDistribution": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "UpdateDistribution",
        "fallbackPathToId": "$.requestParameters.id",
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "UpdateEventSourceMapping20150331": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "LAMBDA",
        "event": "UpdateEventSourceMapping20150331",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.responseElements.functionArn",
        "source": "lambda.amazonaws.com"
    },
    "UpdateFileSystem": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_EFS",
        "event": "UpdateFileSystem",
        "fallbackPathToId": "$.responseElements.fileSystemId",
        "isDefault": null,
        "pathToId": "$.responseElements.fileSystemId",
        "source": "elasticfilesystem.amazonaws.com"
    },
    "UpdateFunctionCode20150331v2": {
        "cspId": 2049,
        "entityType": "LAMBDA",
        "event": "UpdateFunctionCode20150331v2",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "UpdateFunctionConfiguration20150331v2": {
        "cspId": 2049,
        "entityType": "LAMBDA",
        "event": "UpdateFunctionConfiguration20150331v2",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.functionName",
        "source": "lambda.amazonaws.com"
    },
    "UpdateGroup": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "IAM",
        "event": "UpdateGroup",
        "fallbackPathToId": "$.requestParameters.groupName",
        "isDefault": null,
        "pathToId": "$.requestParameters.groupName",
        "source": "iam.amazonaws.com"
    },
    "UpdateIntegration": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateIntegration",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateIntegrationResponse": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateIntegrationResponse",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateLoginProfile": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "USER",
        "event": "UpdateLoginProfile",
        "fallbackPathToId": "$.requestParameters.userName",
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "UpdateMethodResponse": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateMethodResponse",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateRestApi": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateRestApi",
        "fallbackPathToId": "$.responseElements.restapiUpdate.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateRole": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_IAM_ROLE",
        "event": "UpdateRole",
        "fallbackPathToId": "$.requestParameters.roleName",
        "isDefault": null,
        "pathToId": "$.requestParameters.roleName",
        "source": "iam.amazonaws.com"
    },
    "UpdateRoute": {
        "cspId": 2049,
        "entityType": "AWS_ROUTE_TABLE",
        "event": "UpdateRoute",
        "fallbackPathToId": "$.requestParameters.routeId",
        "isDefault": false,
        "pathToId": "$.requestParameters.routeId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateSSHPublicKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "UpdateSSHPublicKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "UpdateShardCount": {
        "cspId": 2049,
        "entityType": "AWS_KINESIS_DATA_STREAMS",
        "event": "UpdateShardCount",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.streamName",
        "source": "kinesis.amazonaws.com"
    },
    "UpdateStack": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "UpdateStack",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "UpdateStackSet": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "CLOUDFORMATION",
        "event": "UpdateStackSet",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.stackName",
        "source": "cloudformation.amazonaws.com"
    },
    "UpdateStage": {
        "cspId": 2049,
        "doDelete": false,
        "entityType": "AWS_API_GATEWAY_ENDPOINT",
        "event": "UpdateStage",
        "fallbackPathToId": "$.requestParameters.restApiId",
        "isDefault": null,
        "pathToId": "$.requestParameters.restApiId",
        "source": "apigateway.amazonaws.com"
    },
    "UpdateStreamingDistribution": {
        "cspId": 2049,
        "entityType": "CLOUD_FRONT",
        "event": "UpdateStreamingDistribution",
        "fallbackPathToId": "$.requestParameters.id",
        "isDefault": false,
        "pathToId": "$.requestParameters.id",
        "source": "cloudfront.amazonaws.com"
    },
    "UpdateTable": {
        "cspId": 2049,
        "entityType": "AWS_DYNAMO_DATABASE",
        "event": "UpdateTable",
        "fallbackPathToId": "$.requestParameters.tableName",
        "isDefault": null,
        "pathToId": "$.requestParameters.tableName",
        "source": "dynamodb.amazonaws.com"
    },
    "UpdateTrail": {
        "cspId": 2049,
        "entityType": "CLOUD_TRAIL",
        "event": "UpdateTrail",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.responseElements.name",
        "source": "cloudtrail.amazonaws.com"
    },
    "UpdateUser": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "UpdateUser",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "UploadSSHPublicKey": {
        "cspId": 2049,
        "entityType": "USER",
        "event": "UploadSSHPublicKey",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.requestParameters.userName",
        "source": "iam.amazonaws.com"
    },
    "UploadServerCertificate": {
        "cspId": 2049,
        "entityType": "AWS_IAM_SERVER_CERTIFICATE",
        "event": "UploadServerCertificate",
        "fallbackPathToId": "$.requestParameters.serverCertificateName",
        "isDefault": null,
        "pathToId": "$.responseElements.serverCertificateMetadata.serverCertificateId",
        "source": "iam.amazonaws.com"
    }
}
`

	GcpEventsMappingJson = `
{
    "CreateCryptoKey": {
        "cspId": 13465,
        "entityType": "GCP_KMS",
        "event": "CreateCryptoKey",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudkms.googleapis.com"
    },
    "CreateProject": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "PROJECT",
        "event": "CreateProject",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.project_id",
        "source": "cloudresourcemanager.googleapis.com"
    },
    "DeleteProject": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "PROJECT",
        "event": "DeleteProject",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.project_id",
        "source": "cloudresourcemanager.googleapis.com"
    },
    "DestroyCryptoKeyVersion": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_KMS",
        "event": "DestroyCryptoKeyVersion",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudkms.googleapis.com"
    },
    "SetIamPolicy": {
        "cspId": 13465,
        "entityType": "GCP_IAM",
        "event": "SetIamPolicy",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudresourcemanager.googleapis.com"
    },
    "UpdateCryptoKey": {
        "cspId": 13465,
        "entityType": "GCP_KMS",
        "event": "UpdateCryptoKey",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudkms.googleapis.com"
    },
    "UpdateCryptoKeyPrimaryVersion": {
        "cspId": 13465,
        "entityType": "GCP_KMS",
        "event": "UpdateCryptoKeyPrimaryVersion",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudkms.googleapis.com"
    },
    "UpdateCryptoKeyVersion": {
        "cspId": 13465,
        "entityType": "GCP_KMS",
        "event": "UpdateCryptoKeyVersion",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudkms.googleapis.com"
    },
    "UpdateProject": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "PROJECT",
        "event": "UpdateProject",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudresourcemanager.googleapis.com"
    },
    "beta.compute.disks.insert": {
        "cspId": 13465,
        "entityType": "VM_DISKS",
        "event": "beta.compute.disks.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "beta.compute.firewalls.insert": {
        "cspId": 13465,
        "entityType": "FIREWALL_RULE",
        "event": "beta.compute.firewalls.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "beta.compute.firewalls.patch": {
        "cspId": 13465,
        "entityType": "FIREWALL_RULE",
        "event": "beta.compute.firewalls.patch",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.firewall_rule_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.instances.insert": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "beta.compute.instances.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.instance_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.instances.setLabels": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "beta.compute.instances.setLabels",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.instance_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.instances.stop": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "beta.compute.instances.stop",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.instance_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.networks.insert": {
        "cspId": 13465,
        "entityType": "PROJECT",
        "event": "beta.compute.networks.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resource.labels.project_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.networks.patch": {
        "cspId": 13465,
        "entityType": "GCP_NETWORK",
        "event": "beta.compute.networks.patch",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.network_id",
        "source": "compute.googleapis.com"
    },
    "beta.compute.networks.switchToCustomMode": {
        "cspId": 13465,
        "entityType": "GCP_NETWORK",
        "event": "beta.compute.networks.switchToCustomMode",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.network_id",
        "source": "compute.googleapis.com"
    },
    "cloudsql.instances.connect": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.instances.connect",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.database_id",
        "source": "cloudsql.googleapis.com"
    },
    "cloudsql.instances.create": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.instances.create",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.request.body.name",
        "source": "cloudsql.googleapis.com"
    },
    "cloudsql.instances.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.instances.delete",
        "fallbackPathToId": "$.protoPayload.request.instance:$.protoPayload.request.instance",
        "isDefault": false,
        "pathToId": "$.protoPayload.request.instance",
        "source": "cloudsql.googleapis.com"
    },
    "cloudsql.instances.update": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.instances.update",
        "fallbackPathToId": "projects:$.resource.labels.project_id:instances:$.protoPayload.request.instance",
        "isDefault": false,
        "pathToId": "$.protoPayload.request.instance",
        "source": "cloudsql.googleapis.com"
    },
    "cloudsql.users.create": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.users.create",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.protoPayload.request.instance",
        "source": "cloudsql.googleapis.com"
    },
    "cloudsql.users.delete": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "SQL_DATABASE",
        "event": "cloudsql.users.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.request.instance",
        "source": "cloudsql.googleapis.com"
    },
    "compute.instances.migrateOnHostMaintenance": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "compute.instances.migrateOnHostMaintenance",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.instance_id",
        "source": "compute.googleapis.com"
    },
    "compute.instances.repair.recreateInstance": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "compute.instances.repair.recreateInstance",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.instance_id",
        "source": "compute.googleapis.com"
    },
    "dns.changes.create": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_DNS_MANAGED_ZONE",
        "event": "dns.changes.create",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.zone_name",
        "source": "dns.googleapis.com"
    },
    "dns.managedZones.create": {
        "cspId": 13465,
        "entityType": "GCP_DNS_MANAGED_ZONE",
        "event": "dns.managedZones.create",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.zone_name",
        "source": "dns.googleapis.com"
    },
    "dns.managedZones.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_DNS_MANAGED_ZONE",
        "event": "dns.managedZones.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.zone_name",
        "source": "dns.googleapis.com"
    },
    "google.cloud.functions.v2.FunctionService.CreateFunction": {
        "cspId": 13465,
        "entityType": "GCP_FUNCTION",
        "event": "google.cloud.functions.v2.FunctionService.CreateFunction",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudfunctions.googleapis.com"
    },
    "google.cloud.functions.v2.FunctionService.DeleteFunction": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_FUNCTION",
        "event": "google.cloud.functions.v2.FunctionService.DeleteFunction",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "cloudfunctions.googleapis.com"
    },
    "google.iam.admin.v1.CreateServiceAccount": {
        "cspId": 13465,
        "entityType": "SERVICE_ACCOUNT",
        "event": "google.iam.admin.v1.CreateServiceAccount",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.email_id",
        "source": "iam.googleapis.com"
    },
    "google.iam.admin.v1.CreateServiceAccountKey": {
        "cspId": 13465,
        "entityType": "SERVICE_ACCOUNT",
        "event": "google.iam.admin.v1.CreateServiceAccountKey",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.email_id",
        "source": "iam.googleapis.com"
    },
    "google.iam.admin.v1.DeleteServiceAccount": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "SERVICE_ACCOUNT",
        "event": "google.iam.admin.v1.DeleteServiceAccount",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.email_id",
        "source": "iam.googleapis.com"
    },
    "google.iam.admin.v1.DisableServiceAccount": {
        "cspId": 13465,
        "entityType": "SERVICE_ACCOUNT",
        "event": "google.iam.admin.v1.DisableServiceAccount",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.email_id",
        "source": "iam.googleapis.com"
    },
    "google.pubsub.v1.Publisher.CreateTopic": {
        "cspId": 13465,
        "entityType": "GCP_PUBSUB_TOPIC",
        "event": "google.pubsub.v1.Publisher.CreateTopic",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "google.pubsub.v1.Publisher.DeleteTopic": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_PUBSUB_TOPIC",
        "event": "google.pubsub.v1.Publisher.DeleteTopic",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "google.pubsub.v1.Subscriber.CreateSnapshot": {
        "cspId": 13465,
        "entityType": "PUBSUB_SNAPSHOT",
        "event": "google.pubsub.v1.Subscriber.CreateSnapshot",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "google.pubsub.v1.Subscriber.CreateSubscription": {
        "cspId": 13465,
        "entityType": "GCP_PUBSUB_SUBSCRIPTION",
        "event": "google.pubsub.v1.Subscriber.CreateSubscription",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "google.pubsub.v1.Subscriber.DeleteSnapshot": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "PUBSUB_SNAPSHOT",
        "event": "google.pubsub.v1.Subscriber.DeleteSnapshot",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "google.pubsub.v1.Subscriber.DeleteSubscription": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_PUBSUB_SUBSCRIPTION",
        "event": "google.pubsub.v1.Subscriber.DeleteSubscription",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "pubsub.googleapis.com"
    },
    "storage.buckets.create": {
        "cspId": 13465,
        "entityType": "CLOUD_STORAGE",
        "event": "storage.buckets.create",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "storage.googleapis.com"
    },
    "storage.buckets.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "CLOUD_STORAGE",
        "event": "storage.buckets.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "storage.googleapis.com"
    },
    "storage.buckets.update": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "CLOUD_STORAGE",
        "event": "storage.buckets.update",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:projects:$.resource.labels.project_id",
        "source": "storage.googleapis.com"
    },
    "storage.setIamPermissions": {
        "cspId": 13465,
        "entityType": "CLOUD_STORAGE",
        "event": "storage.setIamPermissions",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.bucket_name",
        "source": "storage.googleapis.com"
    },
    "v1.compute.disks.addResourcePolicies": {
        "cspId": 13465,
        "entityType": "VM_DISKS",
        "event": "v1.compute.disks.addResourcePolicies",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.disks.createSnapshot": {
        "cspId": 13465,
        "entityType": "GCP_DISK_SNAPSHOT",
        "event": "v1.compute.disks.createSnapshot",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "global:snapshots:$.protoPayload.request.name",
        "source": "compute.googleapis.com"
    },
    "v1.compute.disks.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "VM_DISKS",
        "event": "v1.compute.disks.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.disks.deleteSnapshot": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_DISK_SNAPSHOT",
        "event": "v1.compute.disks.deleteSnapshot",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "global:snapshots:$.protoPayload.request.name",
        "source": "compute.googleapis.com"
    },
    "v1.compute.disks.insert": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "VM_DISKS",
        "event": "v1.compute.disks.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.disks.setLabels": {
        "cspId": 13465,
        "entityType": "VM_DISKS",
        "event": "v1.compute.disks.setLabels",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.firewalls.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "FIREWALL_RULE",
        "event": "v1.compute.firewalls.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.firewalls.insert": {
        "cspId": 13465,
        "entityType": "FIREWALL_RULE",
        "event": "v1.compute.firewalls.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.firewalls.patch": {
        "cspId": 13465,
        "entityType": "FIREWALL_RULE",
        "event": "v1.compute.firewalls.patch",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.images.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_IMAGE",
        "event": "v1.compute.images.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.image_id",
        "source": "compute.googleapis.com"
    },
    "v1.compute.images.insert": {
        "cspId": 13465,
        "entityType": "GCP_IMAGE",
        "event": "v1.compute.images.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.image_id",
        "source": "compute.googleapis.com"
    },
    "v1.compute.images.setIamPolicy": {
        "cspId": 13465,
        "doDelete": false,
        "entityType": "GCP_IMAGE",
        "event": "v1.compute.images.setIamPolicy",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.image_id",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.addAccessConfig": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.addAccessConfig",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.attachDisk": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.attachDisk",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.deleteAccessConfig": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.deleteAccessConfig",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.detachDisk": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.detachDisk",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.insert": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.setDeletionProtection": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.setDeletionProtection",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.setDiskAutoDelete": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.setDiskAutoDelete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.setMetadata": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.setMetadata",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.setTags": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.setTags",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.instances.start": {
        "cspId": 13465,
        "entityType": "VM_INSTANCE",
        "event": "v1.compute.instances.start",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.networks.insert": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_NETWORK",
        "event": "v1.compute.networks.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resource.labels.network_id",
        "source": "compute.googleapis.com"
    },
    "v1.compute.networks.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_NETWORK",
        "event": "v1.compute.networks.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName:$.resource.labels.network_id",
        "source": "compute.googleapis.com"
    },
    "v1.compute.subnetworks.delete": {
        "cspId": 13465,
        "doDelete": true,
        "entityType": "GCP_SUB_NETWORK",
        "event": "v1.compute.subnetworks.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.subnetworks.insert": {
        "cspId": 13465,
        "entityType": "GCP_SUB_NETWORK",
        "event": "v1.compute.subnetworks.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.subnetworks.setPrivateIpGoogleAccess": {
        "cspId": 13465,
        "entityType": "GCP_SUB_NETWORK",
        "event": "v1.compute.subnetworks.setPrivateIpGoogleAccess",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "google.container.v1.ClusterManager.CreateCluster":{
        "cspId": 13465,
        "entityType": "GCP_CLUSTER",
        "event": "google.container.v1.ClusterManager.CreateCluster",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "container.googleapis.com"
    },
    "google.container.v1.ClusterManager.DeleteCluster":{
        "cspId": 13465,
        "entityType": "GCP_CLUSTER",
        "event": "google.container.v1.ClusterManager.DeleteCluster",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "container.googleapis.com"
    },
    "google.container.v1.ClusterManager.UpdateCluster":{
        "cspId": 13465,
        "entityType": "GCP_CLUSTER",
        "event": "google.container.v1.ClusterManager.UpdateCluster",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "container.googleapis.com"
    },
    "v1.compute.regionUrlMaps.insert":{
        "cspId": 13465,
        "entityType": "GCP_LOAD_BALANCER",
        "event": "v1.compute.regionUrlMaps.insert",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    },
    "v1.compute.regionUrlMaps.delete":{
        "cspId": 13465,
        "entityType": "GCP_LOAD_BALANCER",
        "event": "v1.compute.regionUrlMaps.delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.protoPayload.resourceName",
        "source": "compute.googleapis.com"
    }
}
`

	AzureEventMappingJson = `
{
    "Microsoft.Authorization/locks/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_MANAGEMENT_LOCKS",
        "event": "Microsoft.Authorization/locks/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Authorization/locks"
    },
    "Microsoft.Authorization/locks/write": {
        "cspId": 4366,
        "entityType": "AZURE_MANAGEMENT_LOCKS",
        "event": "Microsoft.Authorization/locks/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Authorization/locks"
    },
    "Microsoft.Authorization/roleAssignments/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Authorization/roleAssignments/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts"
    },
    "Microsoft.Cache/Redis/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis"
    },
    "Microsoft.Cache/Redis/linkedServers/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/linkedServers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis/linkedServers"
    },
    "Microsoft.Cache/Redis/patchSchedules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/patchSchedules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis/patchSchedules"
    },
    "Microsoft.Cache/Redis/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis/privateEndpointConnectionProxies"
    },
    "Microsoft.Cache/Redis/privateEndpointConnections/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/privateEndpointConnections/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis/privateEndpointConnections"
    },
    "Microsoft.Cache/Redis/write": {
        "cspId": 4366,
        "entityType": "AZURE_REDIS_CACHE",
        "event": "Microsoft.Cache/Redis/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Cache/Redis"
    },
    "Microsoft.ClassicStorage/storageAccounts/disks/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.ClassicStorage/storageAccounts/disks/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ClassicStorage/storageAccounts/disks"
    },
    "Microsoft.ClassicStorage/storageAccounts/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.ClassicStorage/storageAccounts/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ClassicStorage/storageAccounts"
    },
    "Microsoft.Compute/disks/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "DISK",
        "event": "Microsoft.Compute/disks/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/disks"
    },
    "Microsoft.Compute/disks/write": {
        "cspId": 4366,
        "entityType": "DISK",
        "event": "Microsoft.Compute/disks/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/disks"
    },
    "Microsoft.Compute/virtualMachineScaleSets/deallocate/action": {
        "cspId": 4366,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/deallocate/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachineScaleSets/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachineScaleSets/delete/action": {
        "cspId": 4366,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/delete/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachineScaleSets/extensions/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachineScaleSets/extensions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets/extensions"
    },
    "Microsoft.Compute/virtualMachineScaleSets/restart/action": {
        "cspId": 4366,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/restart/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachineScaleSets/start/action": {
        "cspId": 4366,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/start/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachineScaleSets/virtualmachines/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachineScaleSets/virtualmachines/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets/virtualmachines"
    },
    "Microsoft.Compute/virtualMachineScaleSets/write": {
        "cspId": 4366,
        "entityType": "SCALE_SET",
        "event": "Microsoft.Compute/virtualMachineScaleSets/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachineScaleSets"
    },
    "Microsoft.Compute/virtualMachines/deallocate/action": {
        "cspId": 4366,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachines/deallocate/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachines"
    },
    "Microsoft.Compute/virtualMachines/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachines/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachines"
    },
    "Microsoft.Compute/virtualMachines/extensions/write": {
        "cspId": 4366,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachines/extensions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachines"
    },
    "Microsoft.Compute/virtualMachines/start/action": {
        "cspId": 4366,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachines/start/action",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachines"
    },
    "Microsoft.Compute/virtualMachines/write": {
        "cspId": 4366,
        "entityType": "VIRTUAL_MACHINE",
        "event": "Microsoft.Compute/virtualMachines/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Compute/virtualMachines"
    },
    "Microsoft.DBforMariaDB/servers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_MARIADB_DATABASE",
        "event": "Microsoft.DBforMariaDB/servers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMariaDB/servers"
    },
    "Microsoft.DBforMariaDB/servers/securityAlertPolicies/write": {
        "cspId": 4366,
        "entityType": "AZURE_MARIADB_DATABASE",
        "event": "Microsoft.DBforMariaDB/servers/securityAlertPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMariaDB/servers/securityAlertPolicies"
    },
    "Microsoft.DBforMariaDB/servers/write": {
        "cspId": 4366,
        "entityType": "AZURE_MARIADB_DATABASE",
        "event": "Microsoft.DBforMariaDB/servers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMariaDB/servers"
    },
    "Microsoft.DBforMySQL/servers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_MYSQL_DATABASE",
        "event": "Microsoft.DBforMySQL/servers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMySQL/servers"
    },
    "Microsoft.DBforMySQL/servers/firewallRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_MYSQL_DATABASE",
        "event": "Microsoft.DBforMySQL/servers/firewallRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMySQL/servers/firewallRules"
    },
    "Microsoft.DBforMySQL/servers/securityAlertPolicies/write": {
        "cspId": 4366,
        "entityType": "AZURE_MYSQL_DATABASE",
        "event": "Microsoft.DBforMySQL/servers/securityAlertPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMySQL/servers/securityAlertPolicies"
    },
    "Microsoft.DBforMySQL/servers/virtualNetworkRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_MYSQL_DATABASE",
        "event": "Microsoft.DBforMySQL/servers/virtualNetworkRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMySQL/servers/virtualNetworkRules"
    },
    "Microsoft.DBforMySQL/servers/write": {
        "cspId": 4366,
        "entityType": "AZURE_MYSQL_DATABASE",
        "event": "Microsoft.DBforMySQL/servers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforMySQL/servers"
    },
    "Microsoft.DBforPostgreSQL/servers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers"
    },
    "Microsoft.DBforPostgreSQL/servers/firewallRules/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/firewallRules/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers/firewallRules"
    },
    "Microsoft.DBforPostgreSQL/servers/firewallRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/firewallRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers/firewallRules"
    },
    "Microsoft.DBforPostgreSQL/servers/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers/privateEndpointConnectionProxies"
    },
    "Microsoft.DBforPostgreSQL/servers/securityAlertPolicies/write": {
        "cspId": 4366,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/securityAlertPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers/securityAlertPolicies"
    },
    "Microsoft.DBforPostgreSQL/servers/virtualNetworkRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/virtualNetworkRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers/virtualNetworkRules"
    },
    "Microsoft.DBforPostgreSQL/servers/write": {
        "cspId": 4366,
        "entityType": "AZURE_POSTGRESQL_DATABASE",
        "event": "Microsoft.DBforPostgreSQL/servers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DBforPostgreSQL/servers"
    },
    "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections"
    },
    "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/throughputSettings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/throughputSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/throughputSettings"
    },
    "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/collections"
    },
    "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/throughputSettings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/throughputSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/throughputSettings"
    },
    "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/mongodbDatabases"
    },
    "Microsoft.DocumentDB/databaseAccounts/notebookWorkspaces/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/notebookWorkspaces/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/notebookWorkspaces"
    },
    "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers"
    },
    "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers"
    },
    "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/throughputSettings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/throughputSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/throughputSettings"
    },
    "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases"
    },
    "Microsoft.DocumentDb/databaseAccounts/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDb/databaseAccounts/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDb/databaseAccounts"
    },
    "Microsoft.DocumentDb/databaseAccounts/write": {
        "cspId": 4366,
        "entityType": "AZURE_COSMOSDB",
        "event": "Microsoft.DocumentDb/databaseAccounts/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.DocumentDb/databaseAccounts"
    },
    "Microsoft.EventHub/namespaces/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces"
    },
    "Microsoft.EventHub/namespaces/eventhubs/AuthorizationRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/eventhubs/AuthorizationRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces/eventhubs/AuthorizationRules"
    },
    "Microsoft.EventHub/namespaces/eventhubs/consumergroups/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/eventhubs/consumergroups/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces/eventhubs/consumergroups"
    },
    "Microsoft.EventHub/namespaces/eventhubs/consumergroups/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/eventhubs/consumergroups/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces/eventhubs/consumergroups"
    },
    "Microsoft.EventHub/namespaces/eventhubs/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/eventhubs/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces/eventhubs"
    },
    "Microsoft.EventHub/namespaces/eventhubs/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/eventhubs/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces/eventhubs"
    },
    "Microsoft.EventHub/namespaces/write": {
        "cspId": 4366,
        "entityType": "EVENT_HUB",
        "event": "Microsoft.EventHub/namespaces/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.EventHub/namespaces"
    },
    "Microsoft.KeyVault/vaults/accessPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/accessPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults/accessPolicies"
    },
    "Microsoft.KeyVault/vaults/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults"
    },
    "Microsoft.KeyVault/vaults/eventGridFilters/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/eventGridFilters/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults/eventGridFilters"
    },
    "Microsoft.KeyVault/vaults/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults/privateEndpointConnectionProxies"
    },
    "Microsoft.KeyVault/vaults/privateEndpointConnections/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/privateEndpointConnections/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults/privateEndpointConnections"
    },
    "Microsoft.KeyVault/vaults/secrets/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/secrets/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults/secrets"
    },
    "Microsoft.KeyVault/vaults/write": {
        "cspId": 4366,
        "entityType": "AZURE_KEY_VAULT",
        "event": "Microsoft.KeyVault/vaults/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.KeyVault/vaults"
    },
    "Microsoft.Network/VirtualNetworks/write": {
        "cspId": 4366,
        "entityType": "VIRTUAL_NETWORK",
        "event": "Microsoft.Network/VirtualNetworks/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/VirtualNetworks"
    },
    "Microsoft.Network/applicationGateways/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_APPLICATION_GATEWAY",
        "event": "Microsoft.Network/applicationGateways/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/applicationGateways"
    },
    "Microsoft.Network/applicationGateways/write": {
        "cspId": 4366,
        "entityType": "AZURE_APPLICATION_GATEWAY",
        "event": "Microsoft.Network/applicationGateways/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/applicationGateways"
    },
    "Microsoft.Network/loadBalancers/backendAddressPools/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/loadBalancers/backendAddressPools/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/loadBalancers/backendAddressPools"
    },
    "Microsoft.Network/loadBalancers/backendAddressPools/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/loadBalancers/backendAddressPools/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/loadBalancers/backendAddressPools"
    },
    "Microsoft.Network/loadBalancers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_LOAD_BALANCER",
        "event": "Microsoft.Network/loadBalancers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/loadBalancers"
    },
    "Microsoft.Network/loadBalancers/write": {
        "cspId": 4366,
        "entityType": "AZURE_LOAD_BALANCER",
        "event": "Microsoft.Network/loadBalancers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/loadBalancers"
    },
    "Microsoft.Network/natGateways/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_NAT_GATEWAY",
        "event": "Microsoft.Network/natGateways/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/natGateways"
    },
    "Microsoft.Network/natGateways/write": {
        "cspId": 4366,
        "entityType": "AZURE_NAT_GATEWAY",
        "event": "Microsoft.Network/natGateways/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/natGateways"
    },
    "Microsoft.Network/networkInterfaces/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/networkInterfaces/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkInterfaces"
    },
    "Microsoft.Network/networkInterfaces/write": {
        "cspId": 4366,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/networkInterfaces/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkInterfaces"
    },
    "Microsoft.Network/networkSecurityGroups/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkSecurityGroups/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkSecurityGroups"
    },
    "Microsoft.Network/networkSecurityGroups/securityRules/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkSecurityGroups/securityRules/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkSecurityGroups/securityRules"
    },
    "Microsoft.Network/networkSecurityGroups/securityRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkSecurityGroups/securityRules/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkSecurityGroups/securityRules"
    },
    "Microsoft.Network/networkSecurityGroups/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkSecurityGroups/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkSecurityGroups"
    },
    "Microsoft.Network/networkWatchers/flowLogs/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkWatchers/flowLogs/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkWatchers"
    },
    "Microsoft.Network/networkWatchers/flowLogs/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "NETWORK_SECURITY_GROUP",
        "event": "Microsoft.Network/networkWatchers/flowLogs/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/networkWatchers"
    },
    "Microsoft.Network/publicIPAddresses/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_PUBLIC_IP_ADDRESS",
        "event": "Microsoft.Network/publicIPAddresses/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/publicIPAddresses"
    },
    "Microsoft.Network/publicIPAddresses/write": {
        "cspId": 4366,
        "entityType": "AZURE_PUBLIC_IP_ADDRESS",
        "event": "Microsoft.Network/publicIPAddresses/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/publicIPAddresses"
    },
    "Microsoft.Network/routeTables/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_ROUTE_TABLE",
        "event": "Microsoft.Network/routeTables/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/routeTables"
    },
    "Microsoft.Network/routeTables/routes/delete": {
        "cspId": 4366,
        "entityType": "AZURE_ROUTE_TABLE",
        "event": "Microsoft.Network/routeTables/routes/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/routeTables/routes"
    },
    "Microsoft.Network/routeTables/routes/write": {
        "cspId": 4366,
        "entityType": "AZURE_ROUTE_TABLE",
        "event": "Microsoft.Network/routeTables/routes/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/routeTables/routes"
    },
    "Microsoft.Network/routeTables/write": {
        "cspId": 4366,
        "entityType": "AZURE_ROUTE_TABLE",
        "event": "Microsoft.Network/routeTables/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/routeTables"
    },
    "Microsoft.Network/virtualNetworks/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "VIRTUAL_NETWORK",
        "event": "Microsoft.Network/virtualNetworks/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks"
    },
    "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies"
    },
    "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/remoteVirtualNetworkPeeringProxies"
    },
    "Microsoft.Network/virtualNetworks/subnets/ServiceAssociationLinks/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/subnets/ServiceAssociationLinks/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/subnets/ServiceAssociationLinks"
    },
    "Microsoft.Network/virtualNetworks/subnets/contextualServiceEndpointPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/subnets/contextualServiceEndpointPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/subnets/contextualServiceEndpointPolicies"
    },
    "Microsoft.Network/virtualNetworks/subnets/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "VIRTUAL_NETWORK",
        "event": "Microsoft.Network/virtualNetworks/subnets/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/subnets"
    },
    "Microsoft.Network/virtualNetworks/subnets/write": {
        "cspId": 4366,
        "entityType": "VIRTUAL_NETWORK",
        "event": "Microsoft.Network/virtualNetworks/subnets/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/subnets"
    },
    "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings"
    },
    "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "VIRTUAL_NETWORK",
        "event": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/virtualNetworks/virtualNetworkPeerings"
    },
    "Microsoft.Network/virtualNetworks/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "Microsoft.Network/virtualNetworks/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Network/VirtualNetworks"
    },
    "Microsoft.OperationalInsights/workspaces/configurationScopes/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/configurationScopes/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces/configurationScopes"
    },
    "Microsoft.OperationalInsights/workspaces/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces"
    },
    "Microsoft.OperationalInsights/workspaces/linkedServices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/linkedServices/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces/linkedServices"
    },
    "Microsoft.OperationalInsights/workspaces/linkedStorageAccounts/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/linkedStorageAccounts/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces/linkedStorageAccounts"
    },
    "Microsoft.OperationalInsights/workspaces/savedSearches/schedules/actions/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/savedSearches/schedules/actions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces/savedSearches/schedules/actions"
    },
    "Microsoft.OperationalInsights/workspaces/savedSearches/schedules/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/savedSearches/schedules/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces/savedSearches/schedules"
    },
    "Microsoft.OperationalInsights/workspaces/write": {
        "cspId": 4366,
        "entityType": "AZURE_WORKSPACE",
        "event": "Microsoft.OperationalInsights/workspaces/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces"
    },
    "Microsoft.Resources/deployments/write": {
        "cspId": 4366,
        "entityType": "RESOURCE_GROUP",
        "event": "Microsoft.Resources/deployments/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Resources/deployments"
    },
    "Microsoft.Resources/subscriptions/resourceGroups/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "RESOURCE_GROUP",
        "event": "Microsoft.Resources/subscriptions/resourceGroups/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Resources/subscriptions/resourceGroups"
    },
    "Microsoft.Resources/subscriptions/resourceGroups/write": {
        "cspId": 4366,
        "entityType": "RESOURCE_GROUP",
        "event": "Microsoft.Resources/subscriptions/resourceGroups/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Resources/subscriptions/resourceGroups"
    },
    "Microsoft.Security/securityContacts/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SECURITY_CENTER",
        "event": "Microsoft.Security/securityContacts/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Security/securityContacts"
    },
    "Microsoft.ServiceBus/namespaces/authorizationRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/authorizationRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/authorizationRules"
    },
    "Microsoft.ServiceBus/namespaces/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces"
    },
    "Microsoft.ServiceBus/namespaces/queues/delete": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/queues/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/queues"
    },
    "Microsoft.ServiceBus/namespaces/queues/write": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/queues/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/queues"
    },
    "Microsoft.ServiceBus/namespaces/topics/AuthorizationRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/AuthorizationRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics/AuthorizationRules"
    },
    "Microsoft.ServiceBus/namespaces/topics/delete": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics"
    },
    "Microsoft.ServiceBus/namespaces/topics/subscriptions/delete": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/subscriptions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics/subscriptions"
    },
    "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics/subscriptions/rules"
    },
    "Microsoft.ServiceBus/namespaces/topics/subscriptions/write": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/subscriptions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics/subscriptions"
    },
    "Microsoft.ServiceBus/namespaces/topics/write": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/topics/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces/topics"
    },
    "Microsoft.ServiceBus/namespaces/write": {
        "cspId": 4366,
        "entityType": "AZURE_SERVICE_BUS",
        "event": "Microsoft.ServiceBus/namespaces/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.ServiceBus/namespaces"
    },
    "Microsoft.Sql/servers/administrators/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/administrators/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/administrators"
    },
    "Microsoft.Sql/servers/auditingSettings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/auditingSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/auditingSettings"
    },
    "Microsoft.Sql/servers/connectionPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/connectionPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/connectionPolicies"
    },
    "Microsoft.Sql/servers/databases/advisors/recommendedActions/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/advisors/recommendedActions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/advisors/recommendedActions"
    },
    "Microsoft.Sql/servers/databases/advisors/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/advisors/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/advisors"
    },
    "Microsoft.Sql/servers/databases/auditingSettings/write": {
        "cspId": 4366,
        "entityType": "SQL_DATABASES",
        "event": "Microsoft.Sql/servers/databases/auditingSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/auditingSettings"
    },
    "Microsoft.Sql/servers/databases/automaticTuning/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/automaticTuning/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/automaticTuning"
    },
    "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/backupShortTermRetentionPolicies"
    },
    "Microsoft.Sql/servers/databases/currentSensitivityLabels/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/currentSensitivityLabels/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/currentSensitivityLabels"
    },
    "Microsoft.Sql/servers/databases/dataMaskingPolicies/rules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/dataMaskingPolicies/rules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/dataMaskingPolicies/rules"
    },
    "Microsoft.Sql/servers/databases/dataMaskingPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/dataMaskingPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/dataMaskingPolicies"
    },
    "Microsoft.Sql/servers/databases/delete": {
        "cspId": 4366,
        "entityType": "SQL_DATABASES",
        "event": "Microsoft.Sql/servers/databases/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases"
    },
    "Microsoft.Sql/servers/databases/recommendedSensitivityLabels/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/recommendedSensitivityLabels/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/recommendedSensitivityLabels"
    },
    "Microsoft.Sql/servers/databases/securityAlertPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/securityAlertPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/securityAlertPolicies"
    },
    "Microsoft.Sql/servers/databases/syncGroups/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/syncGroups/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/syncGroups"
    },
    "Microsoft.Sql/servers/databases/transparentDataEncryption/write": {
        "cspId": 4366,
        "entityType": "SQL_DATABASES",
        "event": "Microsoft.Sql/servers/databases/transparentDataEncryption/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/transparentDataEncryption"
    },
    "Microsoft.Sql/servers/databases/vulnerabilityAssessments/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/vulnerabilityAssessments"
    },
    "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines"
    },
    "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/vulnerabilityAssessments/rules/baselines"
    },
    "Microsoft.Sql/servers/databases/workloadGroups/workloadClassifiers/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/workloadGroups/workloadClassifiers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/workloadGroups/workloadClassifiers"
    },
    "Microsoft.Sql/servers/databases/workloadGroups/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/workloadGroups/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/databases/workloadGroups"
    },
    "Microsoft.Sql/servers/databases/write": {
        "cspId": 4366,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/databases/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers"
    },
    "Microsoft.Sql/servers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers"
    },
    "Microsoft.Sql/servers/elasticPools/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/elasticPools/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/elasticPools"
    },
    "Microsoft.Sql/servers/encryptionProtector/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/encryptionProtector/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/encryptionProtector"
    },
    "Microsoft.Sql/servers/failoverGroups/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/failoverGroups/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/failoverGroups"
    },
    "Microsoft.Sql/servers/firewallRules/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/firewallRules/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/firewallRules"
    },
    "Microsoft.Sql/servers/firewallRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/firewallRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/firewallRules"
    },
    "Microsoft.Sql/servers/keys/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/keys/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/keys"
    },
    "Microsoft.Sql/servers/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/privateEndpointConnectionProxies"
    },
    "Microsoft.Sql/servers/securityAlertPolicies/write": {
        "cspId": 4366,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/securityAlertPolicies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/securityAlertPolicies"
    },
    "Microsoft.Sql/servers/virtualNetworkRules/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/virtualNetworkRules/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/virtualNetworkRules"
    },
    "Microsoft.Sql/servers/virtualNetworkRules/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/virtualNetworkRules/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/virtualNetworkRules"
    },
    "Microsoft.Sql/servers/vulnerabilityAssessments/delete": {
        "cspId": 4366,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/vulnerabilityAssessments/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/vulnerabilityAssessments"
    },
    "Microsoft.Sql/servers/vulnerabilityAssessments/write": {
        "cspId": 4366,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/vulnerabilityAssessments/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers/vulnerabilityAssessments"
    },
    "Microsoft.Sql/servers/write": {
        "cspId": 4366,
        "entityType": "SQL_SERVER",
        "event": "Microsoft.Sql/servers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Sql/servers"
    },
    "Microsoft.Storage/storageAccounts/blobServices/containers/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/blobServices/containers/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/blobServices/containers"
    },
    "Microsoft.Storage/storageAccounts/blobServices/containers/immutabilityPolicies/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/blobServices/containers/immutabilityPolicies/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/blobServices/containers/immutabilityPolicies"
    },
    "Microsoft.Storage/storageAccounts/blobServices/containers/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/blobServices/containers/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/blobServices/containers"
    },
    "Microsoft.Storage/storageAccounts/blobServices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/blobServices/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/blobServices"
    },
    "Microsoft.Storage/storageAccounts/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts"
    },
    "Microsoft.Storage/storageAccounts/encryptionScopes/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/encryptionScopes/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/encryptionScopes"
    },
    "Microsoft.Storage/storageAccounts/fileservices/shares/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/fileservices/shares/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/fileservices/shares"
    },
    "Microsoft.Storage/storageAccounts/managementPolicies/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/managementPolicies/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/managementPolicies"
    },
    "Microsoft.Storage/storageAccounts/managementPolicies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/managementPolicies/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/managementPolicies"
    },
    "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies/delete",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies"
    },
    "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/privateEndpointConnectionProxies"
    },
    "Microsoft.Storage/storageAccounts/privateEndpointConnections/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/privateEndpointConnections/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/privateEndpointConnections"
    },
    "Microsoft.Storage/storageAccounts/queueServices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/queueServices/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/queueServices"
    },
    "Microsoft.Storage/storageAccounts/tableServices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/tableServices/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts/tableServices"
    },
    "Microsoft.Storage/storageAccounts/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "Microsoft.Storage/storageAccounts/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Storage/storageAccounts"
    },
    "Microsoft.Web/sites/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites"
    },
    "Microsoft.Web/sites/functions/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_FUNCTION",
        "event": "Microsoft.Web/sites/functions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/functions"
    },
    "Microsoft.Web/sites/functions/properties/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_FUNCTION",
        "event": "Microsoft.Web/sites/functions/properties/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/functions/properties"
    },
    "Microsoft.Web/sites/functions/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_FUNCTION",
        "event": "Microsoft.Web/sites/functions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/functions"
    },
    "Microsoft.Web/sites/host/functionkeys/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/host/functionkeys/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/host/functionkeys"
    },
    "Microsoft.Web/sites/hostNameBindings/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/hostNameBindings/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/hostNameBindings"
    },
    "Microsoft.Web/sites/hostNameBindings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/hostNameBindings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/hostNameBindings"
    },
    "Microsoft.Web/sites/hostruntime/vfs/index.js/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/hostruntime/vfs/index.js/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/hostruntime/vfs/index.js"
    },
    "Microsoft.Web/sites/hostruntime/vfs/run.ps1/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/hostruntime/vfs/run.ps1/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/hostruntime/vfs/run.ps1"
    },
    "Microsoft.Web/sites/hostruntime/vfs/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/hostruntime/vfs/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/hostruntime/vfs"
    },
    "Microsoft.Web/sites/instances/processes/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/instances/processes/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/instances/processes"
    },
    "Microsoft.Web/sites/networkconfig/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/networkconfig/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/networkconfig"
    },
    "Microsoft.Web/sites/privateEndpointConnectionProxies/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/privateEndpointConnectionProxies/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/privateEndpointConnectionProxies"
    },
    "Microsoft.Web/sites/privateEndpointConnectionProxies/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/privateEndpointConnectionProxies/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/privateEndpointConnectionProxies"
    },
    "Microsoft.Web/sites/slots/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/slots/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots"
    },
    "Microsoft.Web/sites/slots/functions/properties/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_FUNCTION",
        "event": "Microsoft.Web/sites/slots/functions/properties/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots/functions/properties"
    },
    "Microsoft.Web/sites/slots/hostNameBindings/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/slots/hostNameBindings/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots/hostNameBindings"
    },
    "Microsoft.Web/sites/slots/hostNameBindings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/slots/hostNameBindings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots/hostNameBindings"
    },
    "Microsoft.Web/sites/slots/siteextensions/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/slots/siteextensions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots/siteextensions"
    },
    "Microsoft.Web/sites/slots/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/slots/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots"
    },
    "Microsoft.Web/sites/sourcecontrols/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/sourcecontrols/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/sourcecontrols"
    },
    "Microsoft.Web/sites/sourcecontrols/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/sourcecontrols/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/sourcecontrols"
    },
    "Microsoft.Web/sites/triggeredwebjobs/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/triggeredwebjobs/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/triggeredwebjobs"
    },
    "Microsoft.Web/sites/virtualNetworkConnections/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/virtualNetworkConnections/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/virtualNetworkConnections"
    },
    "Microsoft.Web/sites/virtualNetworkConnections/gateways/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/virtualNetworkConnections/gateways/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/virtualNetworkConnections/gateways"
    },
    "Microsoft.Web/sites/virtualNetworkConnections/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/virtualNetworkConnections/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/virtualNetworkConnections"
    },
    "Microsoft.Web/sites/write": {
        "cspId": 4366,
        "entityType": "AZURE_APPLICATION",
        "event": "Microsoft.Web/sites/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites"
    },
    "SwapWebSite": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "SwapWebSite",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.Web/sites/slots"
    },
    "microsoft.insights/activityLogAlerts/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "AZURE_ACTIVITY_LOG_ALERT",
        "event": "microsoft.insights/activityLogAlerts/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.insights/activityLogAlerts"
    },
    "microsoft.insights/activityLogAlerts/write": {
        "cspId": 4366,
        "entityType": "AZURE_ACTIVITY_LOG_ALERT",
        "event": "microsoft.insights/activityLogAlerts/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.insights/activityLogAlerts"
    },
    "microsoft.insights/diagnosticSettings/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "microsoft.insights/diagnosticSettings/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.insights/diagnosticSettings"
    },
    "microsoft.keyvault/vaults/eventGridFilters/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_KEY_VAULT",
        "event": "microsoft.keyvault/vaults/eventGridFilters/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.keyvault/vaults/eventGridFilters"
    },
    "microsoft.network/networkWatchers/flowLogs/delete": {
        "cspId": 4366,
        "doDelete": true,
        "entityType": "NETWORK_WATCHER_FLOWLOG",
        "event": "microsoft.network/networkWatchers/flowLogs/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.network/networkwatchers/flowlogs"
    },
    "microsoft.network/networkWatchers/flowLogs/write": {
        "cspId": 4366,
        "entityType": "NETWORK_WATCHER_FLOWLOG",
        "event": "microsoft.network/networkWatchers/flowLogs/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.network/networkwatchers/flowlogs"
    },
    "microsoft.network/virtualNetworks/subnets/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "microsoft.network/virtualNetworks/subnets/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.network/virtualNetworks/subnets"
    },
    "microsoft.network/virtualnetworks/taggedTrafficConsumers/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_NETWORK_INTERFACE",
        "event": "microsoft.network/virtualnetworks/taggedTrafficConsumers/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.network/virtualnetworks/taggedTrafficConsumers"
    },
    "microsoft.operationalinsights/workspaces/customfields/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/customfields/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/customfields"
    },
    "microsoft.operationalinsights/workspaces/customfields/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/customfields/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/customfields"
    },
    "microsoft.operationalinsights/workspaces/datasources/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/datasources/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/datasources"
    },
    "microsoft.operationalinsights/workspaces/datasources/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/datasources/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/datasources"
    },
    "microsoft.operationalinsights/workspaces/linkedServices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/linkedServices/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "Microsoft.OperationalInsights/workspaces"
    },
    "microsoft.operationalinsights/workspaces/logsettings/definitions/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/logsettings/definitions/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/logsettings/definitions"
    },
    "microsoft.operationalinsights/workspaces/savedSearches/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/savedSearches/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/savedSearches"
    },
    "microsoft.operationalinsights/workspaces/savedSearches/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/savedSearches/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/savedSearches"
    },
    "microsoft.operationalinsights/workspaces/storageInsightConfigs/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_WORKSPACE",
        "event": "microsoft.operationalinsights/workspaces/storageInsightConfigs/write",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.operationalinsights/workspaces/storageInsightConfigs"
    },
    "microsoft.storage/storageaccounts/fileservices/write": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "STORAGE_ACCOUNTS",
        "event": "microsoft.storage/storageaccounts/fileservices/write",
        "fallbackPathToId": null,
        "isDefault": false,
        "pathToId": "$.resourceId",
        "source": "microsoft.storage/storageaccounts/fileservices"
    },
    "microsoft.web/sites/siteextensions/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "microsoft.web/sites/siteextensions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.web/sites/siteextensions"
    },
    "microsoft.web/sites/slots/siteextensions/delete": {
        "cspId": 4366,
        "doDelete": false,
        "entityType": "AZURE_APPLICATION",
        "event": "microsoft.web/sites/slots/siteextensions/delete",
        "fallbackPathToId": null,
        "isDefault": null,
        "pathToId": "$.resourceId",
        "source": "microsoft.web/sites/slots/siteextensions"
    }
}
`
)
