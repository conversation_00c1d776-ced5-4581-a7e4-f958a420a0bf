package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"os"
	"strings"
	"time"

	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// SDC -> Single Data Collector

type SDCRequest struct {
	EntityType string   `json:"entityType"`
	EntityIds  []string `json:"entityIds"`
}

type ResourceEventCopy struct {
	ResourceMap     map[string]any
	ResourceType    string
	ResourceName    string
	ServiceCode     string
	EventName       string
	EventTime       time.Time
	ResourceGroup   string
	Region          string
	EventResourceID string
	AccountID       string
}

type SDCResponse struct {
	transport.ServerResponseInfo
	Data []ResourceResponse `json:"data"`
}

type ResourceResponse struct {
	ResourceID         string         `json:"entityId"`
	ResourceProperties map[string]any `json:"cloudResource"`
}

var (
	sdcErrorTracker = make(map[string]map[string]any)
)

func MapEventToTfResources(resourceIdToEventDocMap map[string]ResourceEventCopy, resourceTypeToIDsMap map[string][]string, csp string, tenantID string, accountID string, eventStartTime time.Time) (time.Time, error) {
	lastEventTime := eventStartTime
	batchSize := 3

	for key, value := range resourceTypeToIDsMap {

		for i := 0; i < len(value); i += batchSize {
			end := math.Min(float64(i+batchSize), float64(len(value)))
			batch := value[i:int(end)]

			var resourcesProperties []ResourceResponse
			resourcesProperties, err := FetchResourceDetails(SDCRequest{
				EntityType: key,
				EntityIds:  batch,
			}, csp, tenantID, resourceIdToEventDocMap)
			if err != nil {
				return time.Time{}, err
			}

			mappedResources := make(map[string]struct{})

			for _, resourceProperty := range resourcesProperties {

				if resourcePropertyStr, ok := resourceProperty.ResourceProperties["entityJson"].(string); ok {

					resourcePropertyJSON := make(map[string]any)
					err := json.Unmarshal([]byte(resourcePropertyStr), &resourcePropertyJSON)
					if err != nil {
						logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, resourcePropertyStr}, err)
						break
					}
					var resourceDetail ResourceEventCopy
					if resourceDetail, ok = resourceIdToEventDocMap[resourceProperty.ResourceID]; !ok {
						continue
					}

					resourceLastModifiedTime, err := fetchResourceLastModifiedTime(resourceDetail.ResourceType, resourceDetail.EventResourceID, tenantID)
					if err != nil {
						logger.Print(logger.ERROR, "Error fetch resource last modified time")
						return time.Time{}, err
					}

					var (
						docIDs                    map[string]struct{}
						duration                  = int(math.Ceil(time.Now().UTC().Sub(resourceLastModifiedTime).Hours() / 24))
						backoffDays               = []int{7, 14, 28, 56, 90}
						maxBackoffIndex           int
						commitsFetchEndTime       = resourceDetail.EventTime
						hasCommitsForResourceType = false
					)

					for i, days := range backoffDays {
						if duration < days {
							backoffDays = append(backoffDays[:i], duration)
							break
						}
					}
					maxBackoffIndex = len(backoffDays) - 1

					for backoffIndex := 0; backoffIndex <= maxBackoffIndex; backoffIndex++ {

						resourceLastModifiedTime = commitsFetchEndTime.AddDate(0, 0, -backoffDays[backoffIndex])
						if backoffIndex > 0 {
							commitsFetchEndTime = commitsFetchEndTime.AddDate(0, 0, -backoffDays[backoffIndex-1])
						}

						docIDs, err = fetchCommitIDsForModifiedResources(resourceDetail.ResourceType, resourceLastModifiedTime, commitsFetchEndTime, tenantID, resourceDetail.EventTime, resourcePropertyJSON, &hasCommitsForResourceType)
						if err != nil {
							logger.Print(logger.ERROR, "Error fetching docIds from tf_commits")
							return time.Time{}, err
						}

						if len(docIDs) > 0 {
							if _, ok := mappedResources[resourceDetail.ResourceType+resourceDetail.ResourceName]; ok {
								continue
							}
							mappedResources[resourceDetail.ResourceType+resourceDetail.ResourceName] = struct{}{}
							logger.Print(logger.INFO, "Match Found for ", []string{tenantID}, resourceDetail.ResourceType, resourceDetail.ResourceName, resourceDetail.EventName, resourceDetail.EventName, resourceDetail.EventTime, docIDs)
							break
						}

						// Log for those resource type which has commits only and since there is no filter for Terraform events from Azure avoid logging those.
						if backoffIndex == maxBackoffIndex && hasCommitsForResourceType && csp == AZURE_SERVICE_CODE {
							logger.Print(logger.INFO, "No Match Found for ", []string{tenantID}, resourceDetail.ResourceType, resourceDetail.ResourceName, resourceDetail.EventName, resourceDetail.EventName, resourceDetail.EventTime)
							break
						}
					}

					var gitUsers []GitUser

					if len(docIDs) > 0 {
						var terraformResourceDoc TerraformResourceDoc

						if len(accountID) == 0 {
							terraformResourceDoc.Account = resourceDetail.AccountID
						} else {
							terraformResourceDoc.Account = accountID
						}

						terraformResourceDoc.CSP = csp

						for docID := range docIDs {
							gitCommitSearchQuery := `{
											"query": {
												"bool": {
													"filter": [
													{"match": {"tenantId.keyword": "` + tenantID + `"}},
													{"match": {"_id": "` + docID + `"}}
													]
												}
												}
											}`

							commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.IAC_GIT_COMMITS_INDEX}, gitCommitSearchQuery)
							if err != nil {
								return time.Time{}, err
							}

							if len(commitDocs) <= 0 {
								continue
							}

							if commitID, ok := commitDocs[0]["commitId"].(string); ok {
								terraformResourceDoc.CommitID = commitID
							}

							if repoName, ok := commitDocs[0]["repoName"].(string); ok {
								terraformResourceDoc.Repository = repoName
							}

							if fileName, ok := commitDocs[0]["filename"].(string); ok {
								terraformResourceDoc.Filename = fileName
							}

							terraformResourceDoc.ResourceType = resourceDetail.ResourceType

							terraformResourceDoc.Region = resourceDetail.Region

							terraformResourceDoc.ResourceID, terraformResourceDoc.ResourceName = resourceDetail.EventResourceID, resourceDetail.EventResourceID

							terraformResourceDoc.Approach = "tfAlternate"

							terraformResourceDoc.EventTime = elastic.DateTime(resourceDetail.EventTime)

							eventTime, err := time.Parse(elastic.DATE_FORMAT, terraformResourceDoc.EventTime)
							if err != nil {
								logger.Print(logger.ERROR, "Got error parsing event time", []string{tenantID}, err)
								return time.Time{}, err
							}

							if eventTime.After(lastEventTime) {
								lastEventTime = eventTime
							}

							terraformResourceDoc.TenantID = tenantID

							if csp == AZURE_SERVICE_CODE {
								terraformResourceDoc.ResourceGroup = resourceDetail.ResourceGroup
							}

							if _, err = elastic.InsertDocument(tenantID, elastic.TERRAFORM_RESOURCES_INDEX, terraformResourceDoc); err != nil {
								continue
							}

							gitUsers := append(gitUsers, fetchGitUsersFromCommitDocs(commitDocs, tenantID)...)

							for _, gitUser := range gitUsers {

								gitCommitSearchQuery := `{
									"query": {
										"bool": {
											"filter": [
											{"match": {"commitDocId": "` + gitUser.DocID + `"}}
											]
										}
										}
									}`

								commitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_COMMITS_INDEX}, gitCommitSearchQuery)
								if err != nil {
									continue
								}

								resourceEventPriorityConfig := make(map[string]any)
								resourceEventPriorityConfigJsonDataStr := ""

								if len(commitDocs) > 0 {
									if priorityConfigsJsonStr, ok := commitDocs[0]["priorityConfigs"].(string); ok {
										var priorityConfigsMap map[string]any

										if priorityConfigsJsonStr != "" {
											err := json.Unmarshal([]byte(priorityConfigsJsonStr), &priorityConfigsMap)
											if err != nil {
												logger.Print(logger.ERROR, "Error Unmarshaling priority configs json", err)
												continue
											}

											if len(priorityConfigsMap) != 0 {
												for _, val := range priorityConfigsMap {
													if valMap, ok := val.(map[string]any); ok {
														resourceEventPriorityConfig = valMap
														break
													}
												}
											}
										}

									}
								}

								if len(resourceEventPriorityConfig) != 0 {
									resourceEventPriorityConfigJsonData, err := json.Marshal(resourceEventPriorityConfig)
									if err != nil {
										logger.Print(logger.ERROR, "Got error marshalling static properties", err)
										continue
									}
									resourceEventPriorityConfigJsonDataStr = string(resourceEventPriorityConfigJsonData)
								}

								resourceEvent := ResourceEvent{
									ResourceID:   terraformResourceDoc.ResourceID,
									ResourceName: terraformResourceDoc.ResourceID,
									Region:       terraformResourceDoc.Region,
									Account:      terraformResourceDoc.Account,
									ResourceType: terraformResourceDoc.ResourceType,
								}

								if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, ResourceUserEventDoc{
									ResourceEvent:   resourceEvent,
									Action:          gitUser.Action,
									TenantID:        tenantID,
									User:            gitUser.Name,
									UserType:        gitUser.Client,
									EventTime:       gitUser.CommitTime,
									DocID:           gitUser.DocID,
									DerivedFrom:     "tfAlternate",
									PriorityConfigs: resourceEventPriorityConfigJsonDataStr,
								}); err != nil {
									continue
								}

							}
						}
					}
				}
			}
		}
	}

	return lastEventTime, nil
}

func fetchResourceLastModifiedTime(resourceType string, resourceName string, tenantID string) (time.Time, error) {
	lastModifiedTime := time.Time{}

	resourceLastUserEventQuery := `{
				"_source": ["eventTime"],
				"sort": [{"eventTime": {"order": "desc"}}],
				"query": {
				  "bool": {
					"filter": [
					  {"match": {"tenantId.keyword": "` + tenantID + `"}},
					  {"prefix": {"resourceName.keyword": "` + resourceName + `"}},
					  {"match": {"resourceType.keyword": "` + resourceType + `"}},
					  {"terms": {"userType.keyword": ["gitlab","github","bitbucket"]}},
					  {"match": {"derivedFrom": "tfAlternate"}}
					]
				  }
				},
				"size": 1
			  }`

	resourceLastUserEventDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.RESOURCE_USER_EVENTS_INDEX}, resourceLastUserEventQuery)
	if err != nil {
		return time.Time{}, err
	}

	for _, resourceLastUserEventDoc := range resourceLastUserEventDocs {

		if eventTime, ok := resourceLastUserEventDoc["eventTime"].(string); ok {

			lastModifiedTime, err = elastic.ParseDateTime(eventTime)
			if err != nil {
				logger.Print(logger.ERROR, "Error parse date time", eventTime)
				return time.Time{}, err
			}

		}
	}

	if lastModifiedTime.IsZero() {
		resourceLastUserEventQuery := `{
			"_source": ["eventTime"],
			"sort": [{"eventTime": {"order": "desc"}}],
			"query": {
			  "bool": {
				"filter": [
				  {"match": {"tenantId.keyword": "` + tenantID + `"}},
				  {"match": {"resourceType.keyword": "` + resourceType + `"}},
				  {"terms": {"userType.keyword": ["gitlab","github","bitbucket"]}},
				  {"match": {"derivedFrom": "tfAlternate"}}
				]
			  }
			},
			"size": 1
		  }`

		resourceLastUserEventDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.RESOURCE_USER_EVENTS_INDEX}, resourceLastUserEventQuery)
		if err != nil {
			return time.Time{}, err
		}

		for _, resourceLastUserEventDoc := range resourceLastUserEventDocs {

			if eventTime, ok := resourceLastUserEventDoc["eventTime"].(string); ok {

				lastModifiedTime, err = elastic.ParseDateTime(eventTime)
				if err != nil {
					logger.Print(logger.ERROR, "Error parse date time", eventTime)
					return time.Time{}, err
				}

			}
		}
	}

	return lastModifiedTime, nil
}

func fetchCommitIDsForModifiedResources(resourceType string, resourceLastModifiedTime, eventsTo time.Time, tenantID string, eventTime time.Time, resourceProperty map[string]any, hasCommitsForResourceType *bool) (map[string]struct{}, error) {

	resourcePropertiesMap := make(map[string]any)

	if len(resourceProperty) > 0 {
		flattenJSON(resourceProperty, resourcePropertiesMap)
	} else {
		return nil, nil
	}

	tfCommitQuery := `{
		"query": {
			"bool": {
				"filter": [
					{"match": {"tenantId.keyword": "` + tenantID + `"}},
					{"match": {"resourceType.keyword": "` + resourceType + `"}},
					{"range": {"commitTime": {"gte": "` + elastic.DateTime(resourceLastModifiedTime) + `", "lte": "` + elastic.DateTime(eventsTo) + `"}}}
				]
			}
		}
	}`
	tfCommitDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_COMMITS_INDEX}, tfCommitQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching records from tf_variables index")
		return nil, err
	}

	commitIDs := make(map[string]struct{})

	for _, tfCommitDoc := range tfCommitDocs {

		matchingCount := 0
		totalCount := 0
		*hasCommitsForResourceType = true

		if commitID, ok := tfCommitDoc["commitDocId"].(string); ok {

			if resourceLabelName, ok := tfCommitDoc["resourceLabelName"].(string); ok {

				if resourceFilePath, ok := tfCommitDoc["filePath"].(string); ok {

					completeMatch := false

					err := mapStaticVariables(tfCommitDoc, resourcePropertiesMap, &matchingCount, &totalCount, &completeMatch, resourceProperty)
					if err != nil {
						logger.Print(logger.ERROR, "Error Mapping Static Variables")
						return nil, err
					}

					// False Static Match
					if matchingCount == -1 {
						continue
					}

					if completeMatch {
						ids, err := fetchCommitIDsForMatchedResources(tfCommitDoc, tenantID, resourceLabelName, resourceFilePath, resourceType, commitID, resourceLastModifiedTime, eventTime)
						if err != nil {
							return nil, err
						}
						for key := range ids {
							commitIDs[key] = struct{}{}
						}
						continue
					}

					completeMatch = false

					err = mapDynamicVariables(tfCommitDoc, resourcePropertiesMap, &matchingCount, &totalCount, commitIDs, &completeMatch, resourceProperty)
					if err != nil {
						logger.Print(logger.ERROR, "Error Mapping Dynamic Variables")
						return nil, err
					}

					// False Dynamic Match
					if matchingCount == -1 {
						continue
					}

					if completeMatch {
						ids, err := fetchCommitIDsForMatchedResources(tfCommitDoc, tenantID, resourceLabelName, resourceFilePath, resourceType, commitID, resourceLastModifiedTime, eventTime)
						if err != nil {
							return nil, err
						}
						for key := range ids {
							commitIDs[key] = struct{}{}
						}
						continue
					}

					percentageMatch := 0.0
					if totalCount > 0 {
						percentageMatch = float64(matchingCount) / float64(totalCount) * 100.0
					}
					if percentageMatch >= 50.0 {
						ids, err := fetchCommitIDsForMatchedResources(tfCommitDoc, tenantID, resourceLabelName, resourceFilePath, resourceType, commitID, resourceLastModifiedTime, eventTime)
						if err != nil {
							return nil, err
						}
						for key := range ids {
							commitIDs[key] = struct{}{}
						}
					}
				}
			}
		}
	}

	return commitIDs, nil
}

func mapStaticVariables(tfCommitDoc map[string]any, resourceMap map[string]any, matchingCount *int, totalCount *int, completeMatch *bool, resourceProperty map[string]any) error {
	if staticResource, ok := tfCommitDoc["staticResourceProperties"].(string); ok {
		var staticResourcePropertiesMap map[string]any

		if staticResource == "" {
			return nil
		}

		err := json.Unmarshal([]byte(staticResource), &staticResourcePropertiesMap)
		if err != nil {
			logger.Print(logger.ERROR, "Error Unmarshaling static properties json", err)
			return err
		}

		if len(staticResourcePropertiesMap) == 0 {
			return nil
		}

		for key, val := range staticResourcePropertiesMap {
			key = strings.ToLower(key)
			if key == "key" || key == "value" {
				if key == "value" {
					continue
				}

				if tfKeys, ok := val.([]any); ok {
					if tfValues, ok := staticResourcePropertiesMap["value"].([]any); ok {
						if resourceTags, ok := resourceProperty["tags"].([]any); ok {
							tfTag := make(map[any]any)
							for i := 0; i < len(tfKeys) && i < len(tfValues); i++ {
								tfTag[tfKeys[i]] = tfValues[i]
							}

							for _, item := range resourceTags {

								if itemMap, ok := item.(map[string]any); ok {
									key := itemMap["key"].(string)
									value := itemMap["value"]

									switch v1 := value.(type) {
									case string:
										value = strings.ToLower(strings.ReplaceAll(v1, " ", ""))
									}

									*totalCount++
									if tfTagValue, ok := tfTag[key]; ok {
										if valArr, ok := value.([]any); ok {
											if tfTagValueArr, ok := tfTagValue.([]any); ok {
												if isMatched := arrContainsPartial(tfTagValueArr, valArr, false, matchingCount, nil, nil, nil, true, nil); isMatched {
													*matchingCount++
													if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
														*completeMatch = true
														return nil
													}
													continue
												} else {
													if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
														*matchingCount = -1
														return nil
													}
												}
											}
										} else {
											if equalContains(value, tfTagValue, true) {
												*matchingCount++
												if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
													*completeMatch = true
													return nil
												}
												continue
											} else {
												if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
													*matchingCount = -1
													return nil
												}
											}
										}

									} else {
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*matchingCount = -1
											return nil
										}
									}
								}
							}

						}
					}
				}
			} else {
				if cloudPropertyValue, ok := resourceMap[strings.ToLower(key)]; ok {
					*totalCount++
					if valArr, ok := val.([]any); ok {
						if cloudPropertyValue == nil {
							*totalCount--
							continue
						}
						if cloudPropertyValueArr, ok := cloudPropertyValue.([]any); ok {
							for _, val := range valArr {
								if innerValArr, ok := val.([]any); ok {
									//2D Array
									if isMatched := arrContainsPartial(cloudPropertyValueArr, innerValArr, false, matchingCount, nil, nil, nil, true, nil); isMatched {
										*matchingCount++
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*completeMatch = true
											return nil
										}
										continue
									} else {
										// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*matchingCount = -1
											return nil
										}
									}
								} else {
									//1D Array
									if isMatched := arrContainsPartial(cloudPropertyValueArr, valArr, false, matchingCount, nil, nil, nil, true, nil); isMatched {
										*matchingCount++
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*completeMatch = true
											return nil
										}
										continue
									} else {
										// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*matchingCount = -1
											return nil
										}
									}
									break
								}
							}
						} else {
							for _, v := range valArr {
								if innerValArr, ok := v.([]any); ok {
									//2D Array
									for _, innerArrVal := range innerValArr {
										if equalContains(innerArrVal, cloudPropertyValue, true) {
											*matchingCount++
											// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
											if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
												if equalVal(v, cloudPropertyValue) {
													*completeMatch = true
													return nil
												}
											}
											break
										} else {
											// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
											if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
												*matchingCount = -1
												return nil
											}
										}
									}
								} else {
									//1D Array
									if equalContains(v, cloudPropertyValue, true) {
										*matchingCount++
										// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											if equalVal(v, cloudPropertyValue) {
												*completeMatch = true
												return nil
											}
										}
										break
									} else {
										// TODO: Have a map which contains unique identifiers for an resource instead of using if sts
										if strings.Contains(strings.ToLower(key), "name") || strings.Contains(strings.ToLower(key), "id") || strings.ToLower(key) == "bucket" {
											*matchingCount = -1
											return nil
										}
									}
								}
							}
						}
					}
				}
			}

		}
	}
	return nil
}

func mapDynamicVariables(tfCommitDoc map[string]any, resourceMap map[string]any, matchingCount *int, totalCount *int, commitIDs map[string]struct{}, completeMatch *bool, resourceProperty map[string]any) error {
	if dynamicResource, ok := tfCommitDoc["dynamicResourceProperties"].(string); ok {
		var dynamicResourceMap map[string]any

		if dynamicResource == "" {
			return nil
		}

		err := json.Unmarshal([]byte(dynamicResource), &dynamicResourceMap)
		if err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling dynamic variables", err)
			return err
		}

		if len(dynamicResourceMap) == 0 {
			return nil
		}

		tfVariablesMapping := make(map[string]any)
		variableCommitIDMap := make(map[any][]string)

		err = parseDynamicVariables(tfCommitDoc, tfVariablesMapping, variableCommitIDMap)
		if err != nil {
			logger.Print(logger.ERROR, "Error parsing variables", err)
			return err
		}

		for dynamicResourceMapKey, dynamicResourceMapVal := range dynamicResourceMap {
			dynamicResourceMapKey = strings.ToLower(dynamicResourceMapKey)
			if dynamicResourceMapKey == "key" || dynamicResourceMapKey == "value" {
				if dynamicResourceMapKey == "value" {
					continue
				}

				if tfKeys, ok := dynamicResourceMapVal.([]any); ok {
					if tfValues, ok := dynamicResourceMap["value"].([]any); ok {
						if resourceTags, ok := resourceProperty["tags"].([]any); ok {

							tfTag := make(map[any]any)
							for i := 0; i < len(tfKeys) && i < len(tfValues); i++ {
								tfTag[tfKeys[i]] = tfValues[i]
							}

							for _, item := range resourceTags {
								if itemMap, ok := item.(map[string]any); ok {
									key := itemMap["key"].(string)
									value := itemMap["value"]

									switch v1 := value.(type) {
									case string:
										value = strings.ToLower(strings.ReplaceAll(v1, " ", ""))
									}

									*totalCount++
									if tfTagValue, ok := tfTag[key]; ok {
										if tfTagValueArr, ok := tfTagValue.([]any); ok {
											if valArr, ok := value.([]any); ok {
												lessValueMatch := false
												if isMatched := arrContainsPartial(tfTagValueArr, valArr, false, matchingCount, nil, nil, nil, false, &lessValueMatch); isMatched {
													*matchingCount++
													if !lessValueMatch {
														if strings.Contains(strings.ToLower(key), "name") || strings.ToLower(key) == "bucket" {
															*completeMatch = true
															return nil
														}
													}
													continue
												} else {
													if !lessValueMatch {
														if strings.Contains(strings.ToLower(key), "name") || strings.ToLower(key) == "bucket" {
															*matchingCount = -1
															return nil
														}
													}
												}
											}
										} else {
											cloudPropertyValue := fmt.Sprintf("%v", value)
											if valStr, ok := tfTagValue.(string); ok {

												modifiedVals := FindStringCombinationsForVars(valStr, tfVariablesMapping, variableCommitIDMap)
												if len(modifiedVals) == 0 {
													modifiedVals[valStr] = []string{}
												}
												matchPossible := false
												isMatchFound := false
												for modifiedValskey, modifiedValsVal := range modifiedVals {
													if equalContains(modifiedValskey, cloudPropertyValue, false) {
														for _, id := range modifiedValsVal {
															commitIDs[id] = struct{}{}
														}
														isMatchFound = true
														*matchingCount++
														break
													} else {
														matchPossible = true
													}
													modifiedVal := RemoveVarsFromString(modifiedValskey)
													for _, v := range modifiedVal {
														if equalContains(v, cloudPropertyValue, false) {
															for _, id := range modifiedValsVal {
																commitIDs[id] = struct{}{}
															}
															isMatchFound = true
															*matchingCount++
															break
														} else {
															matchPossible = true
														}
													}
												}
												if !isMatchFound {
													modifiedVal := RemoveVarsFromString(valStr)
													for _, val := range modifiedVal {
														if equalContains(val, cloudPropertyValue, false) {
															isMatchFound = true
															*matchingCount++
															break
														} else {
															matchPossible = true
														}
													}
												}

												if !isMatchFound && matchPossible {

													if strings.Contains(strings.ToLower(key), "name") || strings.ToLower(key) == "bucket" {
														*matchingCount = -1
														return nil
													}
												} else {
													if strings.Contains(strings.ToLower(key), "name") || strings.ToLower(key) == "bucket" {
														*completeMatch = true
														return nil
													}
												}
											}
										}

									} else {
										if strings.Contains(strings.ToLower(key), "name") || strings.ToLower(key) == "bucket" {
											*matchingCount = -1
											return nil
										}
									}
								}
							}

						}
					}
				}

			} else {
				if cloudPropertyValue, ok := resourceMap[strings.ToLower(dynamicResourceMapKey)]; ok {
					*totalCount++
					if valArr, ok := dynamicResourceMapVal.([]any); ok {
						if cloudPropertyValueArr, ok := cloudPropertyValue.([]any); ok {
							for _, val := range valArr {
								lessValueMatch := false
								if val == "" {
									*totalCount--
									break
								}
								if innerValArr, ok := val.([]any); ok {
									//2D Array
									if isMatched := arrContainsPartial(cloudPropertyValueArr, innerValArr, false, matchingCount, tfVariablesMapping, variableCommitIDMap, commitIDs, false, &lessValueMatch); isMatched {
										*matchingCount++
										continue
									}
									isMatched := arrContainsPartial(cloudPropertyValueArr, innerValArr, true, matchingCount, tfVariablesMapping, variableCommitIDMap, commitIDs, false, &lessValueMatch)
									if *completeMatch {
										return nil
									} else if isMatched {
										*matchingCount++
										if !lessValueMatch {
											if strings.Contains(strings.ToLower(dynamicResourceMapKey), "name") || strings.ToLower(dynamicResourceMapKey) == "bucket" {
												*completeMatch = true
												return nil
											}
										}
										continue
									} else if *matchingCount == -1 {
										return nil
									}
								} else {
									//1D Array

									lessValueMatch := false
									if isMatched := arrContainsPartial(cloudPropertyValueArr, valArr, false, matchingCount, tfVariablesMapping, variableCommitIDMap, commitIDs, false, &lessValueMatch); isMatched {
										*matchingCount++
										continue
									}
									isMatched := arrContainsPartial(cloudPropertyValueArr, valArr, true, matchingCount, tfVariablesMapping, variableCommitIDMap, commitIDs, false, &lessValueMatch)
									if *completeMatch {
										return nil
									} else if isMatched {
										*matchingCount++
										if !lessValueMatch {
											if strings.Contains(strings.ToLower(dynamicResourceMapKey), "name") || strings.ToLower(dynamicResourceMapKey) == "bucket" {
												*completeMatch = true
												return nil
											}
										}
										continue
									} else if *matchingCount == -1 {
										return nil
									}
									break
								}
							}
						} else {
							isMatchFound := false
							matchPossible := false
							for _, v := range valArr {
								if v == "" {
									*totalCount--
									break
								}
								if innerValArr, ok := v.([]any); ok {
									//2D Array
									for _, v := range innerValArr {
										if valStr, ok := v.(string); ok {
											cloudPropertyValue := fmt.Sprintf("%v", cloudPropertyValue)

											modifiedVals := FindStringCombinationsForVars(valStr, tfVariablesMapping, variableCommitIDMap)
											if len(modifiedVals) == 0 {
												modifiedVals[valStr] = []string{}
											}
											for modifiedValskey, modifiedValsVal := range modifiedVals {
												if equalContains(cloudPropertyValue, modifiedValskey, false) {
													for _, id := range modifiedValsVal {
														commitIDs[id] = struct{}{}
													}
													isMatchFound = true
													*matchingCount++
													break
												} else {
													matchPossible = true
												}
												modifiedVal := RemoveVarsFromString(modifiedValskey)
												if len(modifiedVal) == 0 {
													matchPossible = false
												}
												for _, v := range modifiedVal {
													if equalContains(cloudPropertyValue, v, false) {
														for _, id := range modifiedValsVal {
															commitIDs[id] = struct{}{}
														}
														isMatchFound = true
														*matchingCount++
														break
													} else {
														matchPossible = true
													}
												}
											}
											if !isMatchFound {
												modifiedVal := RemoveVarsFromString(valStr)
												if len(modifiedVal) == 0 {
													matchPossible = false
												}
												for _, val := range modifiedVal {
													if equalContains(cloudPropertyValue, val, false) {
														isMatchFound = true
														*matchingCount++
														break
													} else {
														matchPossible = true
													}
												}
											}
										}
									}
								} else {
									//1D Array
									if valStr, ok := v.(string); ok {
										cloudPropertyValue := fmt.Sprintf("%v", cloudPropertyValue)

										modifiedVals := FindStringCombinationsForVars(valStr, tfVariablesMapping, variableCommitIDMap)
										if len(modifiedVals) == 0 {
											modifiedVals[valStr] = []string{}
										}
										for modifiedValskey, modifiedValsVal := range modifiedVals {
											if equalContains(cloudPropertyValue, modifiedValskey, false) {
												for _, id := range modifiedValsVal {
													commitIDs[id] = struct{}{}
												}
												isMatchFound = true
												*matchingCount++
												break
											} else {
												matchPossible = true
											}
											modifiedVal := RemoveVarsFromString(modifiedValskey)
											if len(modifiedVal) == 0 {
												matchPossible = false
											}
											for _, v := range modifiedVal {
												if equalContains(cloudPropertyValue, v, false) {
													for _, id := range modifiedValsVal {
														commitIDs[id] = struct{}{}
													}
													isMatchFound = true
													*matchingCount++
													break
												} else {
													matchPossible = true
												}
											}
										}
										if !isMatchFound {
											modifiedVal := RemoveVarsFromString(valStr)
											if len(modifiedVal) == 0 {
												matchPossible = false
											}
											for _, val := range modifiedVal {
												if equalContains(cloudPropertyValue, val, false) {
													isMatchFound = true
													*matchingCount++
													break
												} else {
													matchPossible = true
												}
											}
										}
									}
								}
							}
							if !isMatchFound && matchPossible {
								if strings.Contains(strings.ToLower(dynamicResourceMapKey), "name") || strings.ToLower(dynamicResourceMapKey) == "bucket" {
									*matchingCount = -1
									return nil
								}
							} else if !isMatchFound && !matchPossible {
								*totalCount--
								continue
							} else {
								if strings.Contains(strings.ToLower(dynamicResourceMapKey), "name") || strings.ToLower(dynamicResourceMapKey) == "bucket" {
									*completeMatch = true
									return nil
								}
							}
						}
					}
				}
			}

		}
	}
	return nil
}

// Not being Used
// func mapsEqual(map1, map2 map[string]any) bool {
// 	for key, value1 := range map1 {
// 		if value2, ok := map2[key]; ok {
// 			if value1Arr, isValue1Arr := value1.([]any); isValue1Arr {
// 				if subMap1, isMap1 := value1Arr[0].(map[string]any); isMap1 {
// 					subMap2, isMap2 := value2.(map[string]any)
// 					if !isMap2 || !mapsEqual(subMap1, subMap2) {
// 						return false
// 					}
// 				} else {
// 					if !reflect.DeepEqual(value1Arr[0], value2) {
// 						return false
// 					}
// 				}
// 			}
// 		} else {
// 			return false
// 		}
// 	}
// 	return true
// }

// Not Being Used
// func mapsEqualPercentage(map1, map2 map[string]any, matchingCount *int, totalCount *int) {

// 	//map1 -> tf_commits
// 	//map2 -> resourceMap

// 	for key, value1 := range map1 {
// 		if value2, ok := map2[key]; ok {
// 			if subMap1, isMap1 := value1.(map[string]any); isMap1 {
// 				subMap2, isMap2 := value2.(map[string]any)
// 				if isMap2 {
// 					mapsEqualPercentage(subMap1, subMap2, matchingCount, totalCount)
// 				}
// 			} else if array1, isArray1 := value1.([]any); isArray1 {
// 				*totalCount++
// 				for _, elem1 := range array1 {

// 					if reflect.DeepEqual(elem1, value2) {
// 						*matchingCount++
// 						break
// 					} else if elem1Arr, isElemArr := elem1.([]any); isElemArr {
// 						allValuesMatch := true
// 						if value2Arr, isValue2Arr := value2.([]any); isValue2Arr {
// 							for i, elemVal := range elem1Arr {
// 								if !equalVal(elemVal, value2Arr[i]) {
// 									allValuesMatch = false
// 									break
// 								}
// 							}
// 							if allValuesMatch {
// 								*matchingCount++
// 								break
// 							}
// 						}
// 					} else if value2Obj, isValue2Obj := value2.(map[string]any); isValue2Obj {
// 						if value1Obj, isValue1Obj := array1[0].(map[string]any); isValue1Obj {
// 							allValuesMatch := true
// 							for k, v := range value2Obj {
// 								if val1, ok := value1Obj[k]; ok {

// 									if value1ObjArr, isValue1ObjArr := val1.([]any); isValue1ObjArr {
// 										matchKey := false
// 										for _, e := range value1ObjArr {
// 											if equalVal(v, e) {
// 												matchKey = true
// 												break
// 											}
// 										}
// 										if !matchKey {
// 											allValuesMatch = false
// 											break
// 										}
// 									} else {
// 										if !equalVal(v, val1) {
// 											allValuesMatch = false
// 											break
// 										}
// 									}
// 								} else {
// 									break
// 								}
// 							}
// 							if allValuesMatch {
// 								*matchingCount++
// 								break
// 							}
// 						}
// 					}
// 				}
// 			} else {
// 				*totalCount++
// 				if reflect.DeepEqual(value1, value2) {
// 					*matchingCount++
// 				}
// 			}
// 		}
// 	}
// }

func ExtractValueFromNestedPath(event map[string]any, pathID string) string {
	paths := strings.Split(pathID, ":")
	resultData := ""
	eventCopy := event
	for i, path := range paths {
		eventData := ""
		if strings.Contains(path, "$") {
			properties := strings.Split(path, ".")
			properties = properties[1:]

			for _, property := range properties {
				property = strings.Replace(property, "[*]", "", -1)
				if val, ok := event[property]; ok {
					switch v := val.(type) {
					case string:
						eventData = v
					case []any:
						if mp, ok := v[0].(map[string]any); ok {
							event = mp
						}
					case map[string]any:
						event = v
					}
				} else {
					logger.Print(logger.INFO, "Event value not found for pathID", pathID, "\n", event)
					return ""
				}
			}
		} else {
			eventData = path
		}

		if eventData != "" {
			resultData += eventData
		}
		if i < len(paths)-1 && eventData != "" {
			resultData += "/"
		}
		event = eventCopy
	}
	return resultData
}

func flattenJSON(jsonData map[string]any, flattened map[string]any) {
	for key, value := range jsonData {
		key = strings.ToLower(key)
		switch value := value.(type) {
		case map[string]any:
			flattenJSON(value, flattened)
		case []any:
			for _, v := range value {
				switch v := v.(type) {
				case map[string]any:
					flattenJSON(v, flattened)
				default:
					switch v1 := v.(type) {
					case string:
						v = strings.ToLower(strings.ReplaceAll(v1, " ", ""))
					}
					if existingValue, ok := flattened[key]; ok {
						switch existingValue := existingValue.(type) {
						case []any:
							if !contains(existingValue, v) {
								flattened[strings.ToLower(key)] = append(existingValue, v)
							}
						default:
							if !contains([]any{existingValue}, v) {
								flattened[strings.ToLower(key)] = []any{existingValue, v}
							}
						}
					} else {
						flattened[strings.ToLower(key)] = v
					}
				}
			}
		default:
			switch v1 := value.(type) {
			case string:
				value = strings.ToLower(strings.ReplaceAll(v1, " ", ""))
			}
			if existingValue, ok := flattened[key]; ok {
				switch existingValue := existingValue.(type) {
				case []any:
					if !contains(existingValue, value) {
						flattened[strings.ToLower(key)] = append(existingValue, value)
					}
				default:
					if !contains([]any{existingValue}, value) {
						flattened[strings.ToLower(key)] = []any{existingValue, value}
					}
				}
			} else {
				flattened[strings.ToLower(key)] = value
			}
		}
	}
}

func FetchResourceDetails(sdcRequest SDCRequest, csp string, tenantID string, resourceIdToEventDocMap map[string]ResourceEventCopy) ([]ResourceResponse, error) {

	time.Sleep(10 * time.Millisecond)
	jsonBody, err := json.Marshal(sdcRequest)
	if err != nil {
		logger.Print(logger.ERROR, "Error Marshaling JSON", err)
		return nil, err
	}
	var sdcResponse SDCResponse
	resp, err := transport.SendRequestToServer("POST", "/precize/private/entities/"+csp+"/"+tenantID, nil, bytes.NewBuffer(jsonBody))
	if err != nil {
		logger.Print(logger.INFO, "SDC Response for error", []string{tenantID}, sdcRequest)
		return nil, nil
	}

	if err = json.Unmarshal(resp, &sdcResponse); err != nil {
		logger.Print(logger.ERROR, "Got error calling unmarshal", sdcResponse, err)
		return nil, err
	}

	if sdcResponse.ErrorCode == 16101 {
		val := sdcErrorTracker[tenantID]
		if len(val) > 0 {
			if valMap, ok := val["EntityType"].(map[string]struct{}); ok {
				if _, ok := valMap[sdcRequest.EntityType]; ok {
					return nil, nil
				}
			}
		}

		logger.Print(logger.INFO, "SDC Error", []string{tenantID}, sdcRequest, sdcResponse.Message)

		eventMap := make(map[string]struct{})
		entityIDMap := make(map[string]struct{})
		entityTypeMap := make(map[string]struct{})

		if len(val) > 0 {
			eventMap, _ = val["Events"].(map[string]struct{})
			entityIDMap, _ = val["EntityIDs"].(map[string]struct{})
			entityTypeMap, _ = val["EntityType"].(map[string]struct{})
		}

		if resourceIdToEventDocMap != nil {
			for _, id := range sdcRequest.EntityIds {
				if val, ok := resourceIdToEventDocMap[id]; ok {
					eventMap[val.EventName] = struct{}{}
					entityIDMap[id] = struct{}{}
				}
			}
			entityTypeMap[sdcRequest.EntityType] = struct{}{}
		}

		val = make(map[string]any)
		val["EntityType"] = entityTypeMap
		val["EntityIDs"] = entityIDMap
		val["Events"] = eventMap
		val["Csp"] = csp

		sdcErrorTracker[tenantID] = val

		jsonData, err := json.Marshal(sdcErrorTracker)
		if err != nil {
			return nil, err
		}

		fileName := "sdcError.json"
		if _, err := os.Stat(fileName); os.IsNotExist(err) {
			if _, err := os.Create(fileName); err != nil {
				return nil, err
			}
		}

		err = ioutil.WriteFile(fileName, jsonData, 0644)
		if err != nil {
			return nil, err
		}
		return nil, nil
	}

	return sdcResponse.Data, nil
}

func fetchCommitIDsForMatchedResources(tfCommitDoc map[string]any, tenantID, resourceLabelName, resourceFilePath, resourceType, commitID string, resourceLastModifiedTime, eventTime time.Time) (map[string]struct{}, error) {
	var commitIDs = make(map[string]struct{})
	if variableModifiedDocIds, ok := tfCommitDoc["variableModifiedCommitDocIds"].(string); ok {
		if len(variableModifiedDocIds) != 0 {
			docIDs := make(map[string]any)

			err := json.Unmarshal([]byte(variableModifiedDocIds), &docIDs)
			if err != nil {
				logger.Print(logger.ERROR, "Error Unmarshaling static properties json", err)
				return nil, err
			}

			if len(docIDs) != 0 {
				for key := range docIDs {
					if _, ok := commitIDs[key]; !ok {
						commitIDs[key] = struct{}{}
					}
				}
			}
		}
	}
	if _, ok := commitIDs[commitID]; !ok {
		commitIDs[commitID] = struct{}{}
	}

	tfCommitQuery := `{
				"query": {
					"bool": {
						"filter": [
							{"match": {"tenantId.keyword": "` + tenantID + `"}},
							{"match": {"resourceType.keyword": "` + resourceType + `"}},
							{"match": {"resourceLabelName.keyword": "` + resourceLabelName + `"}},
							{"match": {"filePath.keyword": "` + resourceFilePath + `"}},
							{"range": {"commitTime": {"gt": "` + elastic.DateTime(resourceLastModifiedTime) + `", "lte": "` + elastic.DateTime(eventTime) + `"}}}
						]
					}
				}
			}`
	tfCommitDocsRev, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.TF_COMMITS_INDEX}, tfCommitQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching records from tf_commits index")
		return nil, err
	}

	for _, tfCommitDocRev := range tfCommitDocsRev {
		if commitID, ok := tfCommitDocRev["commitDocId"].(string); ok {
			if _, ok := commitIDs[commitID]; !ok {
				commitIDs[commitID] = struct{}{}
			}
		}
	}
	return commitIDs, nil
}

func parseDynamicVariables(tfCommitDocmap map[string]any, tfVariablesMapping map[string]any, variableCommitIDMap map[any][]string) error {

	if gitClient, ok := tfCommitDocmap["gitClient"].(string); ok {
		if tenantID, ok := tfCommitDocmap["tenantId"].(string); ok {
			if filePath, ok := tfCommitDocmap["filePath"].(string); ok {
				if repoName, ok := tfCommitDocmap["repoName"].(string); ok {
					recentCodeEventsQuery := `{
					"_source": ["filePath", "variables", "fileName"],
						"query": {
							"bool": {
								"must": [
									{"match": {"gitClient": "` + gitClient + `"}},
									{"match": {"tenantId.keyword": "` + tenantID + `"}},
									{"match": {"repoName.keyword": "` + repoName + `"}}  
								]
							}
						}
					}`

					tfVarDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_VARIABLES_INDEX}, recentCodeEventsQuery)
					if err != nil {
						logger.Print(logger.ERROR, "Got error while fetching existing module var record", filePath, tenantID)
						return err
					}

					if len(tfVarDocs) > 0 {
						for _, tfVarDoc := range tfVarDocs {
							if variableName, ok := tfVarDoc["varName"].(string); ok {

								if variablesJSONStr, ok := tfVarDoc["variables"].(string); ok {
									variableMap := make(map[string]any)
									err := json.Unmarshal([]byte(variablesJSONStr), &variableMap)
									if err != nil {
										logger.Print(logger.ERROR, "Got error while unmarshaling variables json")
										continue
									}

									for commitID, value := range variableMap {
										if valueMap, isValueMap := value.([]any); isValueMap {
											for _, valueMapValues := range valueMap {
												if valueMapValuesArr, isValueMapValuesArr := valueMapValues.([]any); isValueMapValuesArr {
													for _, v := range valueMapValuesArr {
														if val, ok := tfVariablesMapping[variableName]; ok {
															if valArr, isValArr := val.([]any); isValArr {
																if !contains(valArr, v) {
																	valArr = append(valArr, v)
																	tfVariablesMapping[variableName] = valArr
																}
															}
														} else {
															valArr := make([]any, 0)
															valArr = append(valArr, v)
															tfVariablesMapping[variableName] = valArr
														}
														if commitID != "unknown" {
															if _, ok := v.([]any); ok {
																//TODO: handle case when variable value is array. Array cannot be key in map
																continue
															}
															if valArr, ok := variableCommitIDMap[v]; ok {
																valArr = append(valArr, commitID)
																variableCommitIDMap[v] = valArr
															} else {
																commitArr := make([]string, 0)
																commitArr = append(commitArr, commitID)
																variableCommitIDMap[v] = commitArr
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	return nil
}
