package common

import (
	"context"

	"github.com/google/go-github/v48/github"
	"github.com/xanzy/go-gitlab"
)

type ResourceEvent struct {
	ResourceID    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceType  string `json:"resourceType"`
	Account       string `json:"account,omitempty"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
	Region        string `json:"region,omitempty"`
}

type ResourceUserEventDoc struct {
	ResourceEvent
	ID              string `json:"id"`
	Action          string `json:"action"`
	TenantID        string `json:"tenantId"`
	User            string `json:"user"`
	UserType        string `json:"userType"`
	EventTime       string `json:"eventTime"`
	DocID           string `json:"commitDocId"`
	DerivedFrom     string `json:"derivedFrom"`
	PriorityConfigs string `json:"priorityConfigs"`
}

type IACGitCommitDoc struct {
	CommitID         string `json:"commitId"`
	CommitSummary    string `json:"commitSummary"`
	Author           string `json:"author"`
	AuthorEmail      string `json:"authorEmail"`
	Filename         string `json:"filename"`
	GitFileHash      string `json:"gitFileHash"`
	MinifiedJSONHash string `json:"minifiedJSONHash"`
	CommitTime       string `json:"commitTime"`
	FileStatus       string `json:"fileStatus"`
	RepoName         string `json:"repoName"`
	Branch           string `json:"branch"`
	IACType          string `json:"iacType"`
	GitClient        string `json:"gitClient"`
	TenantID         string `json:"tenantId"`
	FileContent      string `json:"fileContent"`
	PriorityConfigs  string `json:"priorityConfigs"`
}

type TerraformResourceDoc struct {
	ResourceID    string `json:"resourceId"`
	ResourceName  string `json:"resourceName"`
	ResourceType  string `json:"resourceType"`
	EventTime     string `json:"eventTime"`
	Repository    string `json:"repository"`
	CommitID      string `json:"commitId"`
	Filename      string `json:"filename"`
	ResourceGroup string `json:"resourceGroup"`
	Account       string `json:"account"`
	Region        string `json:"region"`
	TenantID      string `json:"tenantId"`
	CSP           string `json:"csp"`
	Approach      string `json:"approach"`
}

type GitUser struct {
	Name       string
	Client     string
	Action     string
	CommitTime string
	DocID      string
}

type TerraformStateFile struct {
	Resources []TerraformStateResource `json:"resources"`
}

type TerraformStateResource struct {
	Mode      string                           `json:"mode"`
	Type      string                           `json:"type"`
	Provider  string                           `json:"provider"`
	Instances []TerraformStateResourceInstance `json:"instances"`
}

type TerraformStateResourceInstance struct {
	Attributes InstanceAttributes `json:"attributes"`
}

type InstanceAttributes struct {
	Arn           string `json:"arn"`
	ID            string `json:"id"`
	Name          string `json:"name"`
	Location      string `json:"location"`
	ResourceGroup string `json:"resource_group_name"`
}

type TFCommitDoc struct {
	DocID                        string   `json:"commitDocId"`
	ResourceLabelName            string   `json:"resourceLabelName"`
	ResourceType                 string   `json:"resourceType"`
	StaticResourceProperties     string   `json:"staticResourceProperties"`
	DynamicResourceProperties    string   `json:"dynamicResourceProperties"`
	Variables                    string   `json:"variables"`
	ServiceCode                  string   `json:"serviceCode"`
	TenantID                     string   `json:"tenantId"`
	GitClient                    string   `json:"gitClient"`
	CommitTime                   string   `json:"commitTime"`
	VariableModifiedCommitDocIds string   `json:"variableModifiedCommitDocIds"`
	FilePath                     string   `json:"filePath"`
	UnprocessedResourceType      string   `json:"unprocessedResourceType"`
	ResourceTemplate             string   `json:"resourceTemplate"`
	RepoName                     string   `json:"repoName"`
	PriorityConfigs              string   `json:"priorityConfigs"`
	ExternalFiles                []string `json:"externalFiles"`
}

type GithubApiClient struct {
	GithubClient *github.Client
	Context      context.Context
	RepoOwner    string
	RepoName     string
	CommitSHA    string
	FilePath     string
	TenantID     string
	FileStatus   string
	Branch       string
}

type GitlabApiClient struct {
	GitlabClient *gitlab.Client
	CommitID     string
	ProjectID    int
	Branch       string
	FilePath     string
	TenantID     string
	RepoName     string
	FileStatus   string
	AccessToken  string
}

type BitbucketApiClient struct {
	Token         string
	Domain        string
	WorkspaceSlug string
	RepoSlug      string
	CommitHash    string
	FilePath      string
	TenantID      string
	RepoName      string
	FileStatus    string
	Branch        string
}

type BitBucketDirectoryContents struct {
	Values []DirectoryValue `json:"values"`
}

type DirectoryValue struct {
	Path string `json:"path"`
	Type string `json:"type"`
}

type TFVarDoc struct {
	TenantID     string `json:"tenantId"`
	Variables    string `json:"variables"`
	VariableName string `json:"varName"`
	RepoName     string `json:"repoName"`
	GitClient    string `json:"gitClient"`
}

type EnvironmentDoc struct {
	TenantID     string   `json:"tenantId"`
	ServiceID    int      `json:"serviceId"`
	Name         string   `json:"name"`
	Repositories []string `json:"repositories"`
}

type DeplopymentInfo struct {
	JobID       string
	Name        string
	Email       string
	StartTime   string
	EndTime     string
	Duration    float64
	GitClient   string
	ProjectID   string
	ProjectName string
}
