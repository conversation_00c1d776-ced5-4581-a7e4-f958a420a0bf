package common

import "github.com/precize/transport"

type ComplianceFunction struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type Compliance struct {
	Name          string               `json:"name"`
	FunctionsList []ComplianceFunction `json:"functionsList"`
}

type HeroStat struct {
	DbKey             string       `json:"dbKey"`
	DisplayText       string       `json:"displayText"`
	Description       string       `json:"description"`
	AdditionalDetails string       `json:"additionalDetails"`
	ResourceCount     int          `json:"resourceCount"`
	ResourceTotal     int          `json:"resourceTotal"`
	Severity          string       `json:"severity"`
	AccountIds        []string     `json:"accountIds"`
	Position          int          `json:"position"`
	EntityIds         []string     `json:"entityIds"`
	Target            string       `json:"target"`
	Compliance        []Compliance `json:"compliance"`
	StatRisk          []int        `json:"statRisk"`
}

type HeroStatsAPIReq struct {
	AccountIds []string `json:"accountIds"`
}

type HeroStatsAPIResponse struct {
	transport.ServerResponseInfo
	Data []HeroStat `json:"data"`
}
