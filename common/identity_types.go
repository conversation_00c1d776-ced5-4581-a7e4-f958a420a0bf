package common

type IdentitiesDoc struct {
	ID                string            `json:"id,omitempty"`
	TenantID          string            `json:"tenantId,omitempty"`
	ServiceCode       string            `json:"serviceCode,omitempty"`
	AccountID         string            `json:"accountId,omitempty"`
	IdentityID        string            `json:"identityId,omitempty"`
	Name              string            `json:"name,omitempty"`
	Type              string            `json:"type,omitempty"`
	LastReadTime      int64             `json:"lastReadTime,omitempty"`
	LastReadActivity  *Event            `json:"lastReadActivity,omitempty"`
	LastWriteTime     int64             `json:"lastWriteTime,omitempty"`
	LastWriteActivity *Event            `json:"lastWriteActivity,omitempty"`
	Deleted           bool              `json:"deleted"`
	AdditionalInfo    string            `json:"additionalInfo,omitempty"`
	AccountType       int               `json:"accountType"`
	Permission        int               `json:"permission"`
	Category          int               `json:"category,omitempty"`
	IsCrossAccount    bool              `json:"isCrossAccount"`
	ExUser            bool              `json:"exUser"`
	CreatedDate       string            `json:"createdDate,omitempty"`
	UpdatedDate       string            `json:"updatedDate,omitempty"`
	AdditionalDetails map[string]string `json:"additionalDetails,omitempty"`
	PrimaryEmail      string            `json:"primaryEmail,omitempty"`
	IdentityStatus    int               `json:"identityStatus,omitempty"`
	UIDeleted         bool              `json:"uiDeleted,omitempty"`
}

type Event struct {
	Name   string `json:"name"`
	Region string `json:"region"`
	Time   string `json:"time"`
}

type IdentityApplicationAccess struct {
	Name     string `json:"name"`
	Label    string `json:"label"`
	Username string `json:"username"`
}
