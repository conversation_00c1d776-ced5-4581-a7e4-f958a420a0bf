package common

const (
	USER_JIRADATA_TYPE       = "user"
	PROJECT_JIRADATA_TYPE    = "project"
	ISSUETYPE_JIRADATA_TYPE  = "issueType"
	ISSUEFIELD_JIRADATA_TYPE = "field"
	FIELDVALUE_JIRADATA_TYPE = "fieldValue"
)

type Comment struct {
	Author string `json:"author"`
	Body   string `json:"body"`
}

type JiraIssuesDoc struct {
	ID           string    `json:"id"`
	IssueID      string    `json:"issueId"`
	Summary      string    `json:"summary"`
	Assignee     string    `json:"assignee"`
	Description  string    `json:"description"`
	Reporter     string    `json:"reporter"`
	CreatedTime  string    `json:"createdTime"`
	ModifiedTime string    `json:"modifiedTime"`
	Status       string    `json:"status"`
	ProjectName  string    `json:"projectName"`
	ProjectID    string    `json:"projectId"`
	TenantID     string    `json:"tenantId"`
	AccountID    string    `json:"accountId"`
	ResourceType string    `json:"resourceType"`
	InsertTime   string    `json:"insertTime"`
	Comments     []Comment `json:"comments,omitempty"`
	Active       bool      `json:"active"`
}

type JiraCloudChangelog struct {
	NextPage   string           `json:"nextPage"`
	MaxResults int              `json:"maxResults"`
	StartAt    int              `json:"startAt"`
	Total      int              `json:"total"`
	IsLast     bool             `json:"isLast"`
	Values     []ChangeLogValue `json:"values"`
}

type JiraDCChangelog struct {
	MaxResults int              `json:"maxResults"`
	StartAt    int              `json:"startAt"`
	Total      int              `json:"total"`
	Values     []ChangeLogValue `json:"histories"`
}

type ChangeLogValue struct {
	ID      string `json:"id"`
	Author  Author `json:"author"`
	Created string `json:"created"`
	Items   []Item `json:"items"`
}

type Author struct {
	AccountID   string `json:"accountId"`
	DisplayName string `json:"displayName"`
	Active      bool   `json:"active"`
}

type Item struct {
	Field      string `json:"field"`
	Fieldtype  string `json:"fieldtype"`
	FieldID    string `json:"fieldId"`
	From       string `json:"from"`
	FromString string `json:"fromString"`
	To         string `json:"to"`
	ToString   string `json:"toString"`
}

type JiraDataDoc struct {
	ID                string `json:"id"`
	JiraID            string `json:"jiraId"`
	Name              string `json:"name"`
	Type              string `json:"type"`
	ParentID          string `json:"parentId"`
	AccountID         string `json:"accountId"`
	TenantID          string `json:"tenantId"`
	AdditionalDetails string `json:"additionalDetails"`
	InsertTime        string `json:"insertTime"`
	Deleted           bool   `json:"deleted"`
}

type JiraUser struct {
	AccountID    string `json:"accountId"`
	Key          string `json:"key"`
	AccountType  string `json:"accountType"`
	DisplayName  string `json:"displayName"`
	Name         string `json:"name"`
	EmailAddress string `json:"emailAddress"`
	Active       bool   `json:"active"`
}

type JiraProject struct {
	ID         string          `json:"id"`
	Name       string          `json:"name"`
	Key        string          `json:"key"`
	IssueTypes []JiraIssueType `json:"issueTypes"`
}

type JiraIssueType struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type JiraCloudIssue struct {
	ID     string               `json:"id"`
	Key    string               `json:"key"`
	Fields JiraCloudIssueFields `json:"fields"`
}

type JiraComment struct {
	ID     string           `json:"id"`
	Author JiraUser         `json:"author"`
	Body   IssueDescription `json:"body"`
}

type JiraComments struct {
	Comment []JiraComment `json:"comments"`
}

type JiraCustomField struct {
	ID    any    `json:"id"`
	Value string `json:"value"`
}

type JiraCloudIssueFields struct {
	IssueType   JiraIssueType    `json:"issuetype"`
	Project     JiraProject      `json:"project"`
	Created     string           `json:"created"`
	Assignee    JiraUser         `json:"assignee"`
	Updated     string           `json:"updated"`
	Description IssueDescription `json:"description"`
	Summary     string           `json:"summary"`
	Reporter    JiraUser         `json:"reporter"`
	Status      struct {
		ID             string `json:"id"`
		Name           string `json:"name"`
		StatusCategory struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
			Key  string `json:"key"`
		} `json:"statusCategory"`
	} `json:"status"`
	Comment JiraComments `json:"comment"`
}

type JiraDCIssue struct {
	ID        string            `json:"id"`
	Key       string            `json:"key"`
	Fields    JiraDCIssueFields `json:"fields"`
	Changelog JiraDCChangelog   `json:"changelog"`
}

type JiraDCIssueFields struct {
	IssueType   JiraIssueType `json:"issuetype"`
	Project     JiraProject   `json:"project"`
	Created     string        `json:"created"`
	Assignee    JiraUser      `json:"assignee"`
	Updated     string        `json:"updated"`
	Description string        `json:"description,omitempty"`
	Summary     string        `json:"summary"`
	Reporter    JiraUser      `json:"reporter"`
	Status      struct {
		ID             string `json:"id"`
		Name           string `json:"name"`
		StatusCategory struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
			Key  string `json:"key"`
		} `json:"statusCategory"`
	} `json:"status"`
}

type IssueDescription struct {
	PlainText *string `json:"-"`
	Content   []ContentBlock
}
type TagMention struct {
	ID   string `json:"id"`
	Text string `json:"text"`
}

type ContentBlock struct {
	Type    string         `json:"type"`
	Content []ContentBlock `json:"content,omitempty"`
	Text    string         `json:"text,omitempty"`
	Attrs   TagMention     `json:"attrs,omitempty"`
}

type JiraIssueTypeField struct {
	Name     string `json:"name"`
	Key      string `json:"key"`
	Required bool   `json:"required"`
	Schema   struct {
		Type   string `json:"type"`
		Items  string `json:"items"`
		System string `json:"system"`
		Custom string `json:"custom"`
	} `json:"schema"`
	HasDefaultValue bool             `json:"hasDefaultValue"`
	Operations      []string         `json:"operations"`
	AllowedValues   []map[string]any `json:"allowedValues,omitempty"`
	DefaultValue    any              `json:"defaultValue,omitempty"`
	FieldID         string           `json:"fieldId"`
	AutoCompleteUrl string           `json:"autoCompleteUrl"`
}

type jiraBaseResponse struct {
	Total      int `json:"total,omitempty"`
	StartAt    int `json:"startAt,omitempty"`
	MaxResults int `json:"maxResults,omitempty"`
}

type JiraCreateMetaResponse struct {
	jiraBaseResponse
	Values []JiraIssueTypeField `json:"values"`
}

type JiraDataCenterIssueSearchResponse struct {
	jiraBaseResponse
	Issues []JiraDCIssue `json:"issues"`
}

type AssigneeChangeLog struct {
	FromAssignee string `json:"fromAssignee"`
	ToAssignee   string `json:"toAssignee"`
	CreatedTime  string `json:"createdTime"`
	Author       string `json:"author"`
}

type JiraDCComment struct {
	ID     string   `json:"id"`
	Author JiraUser `json:"author"`
	Body   string   `json:"body"`
}

type JiraDataCenterCommentResponse struct {
	jiraBaseResponse
	Comments []JiraDCComment `json:"comments"`
}

var AWSKeywordToResourceTypeMap = map[string]string{
	`(?i)iam[-_\s]*(user|users)`:                                   AWS_IAM_USER_RESOURCE_TYPE,
	`(?i)iam[-_\s]*(role|roles)`:                                   AWS_IAM_ROLE_RESOURCE_TYPE,
	`(?i)(security[-_\s]*group|sg)s?`:                              AWS_SG_RESOURCE_TYPE,
	`(?i)(ec2|instance|ec2[-_\s]*instance)s?`:                      AWS_EC2_RESOURCE_TYPE,
	`(?i)subnet(s)?`:                                               AWS_SUBNET_RESOURCE_TYPE,
	`(?i)vpc(s)?`:                                                  AWS_VPC_RESOURCE_TYPE,
	`(?i)(ebs[-_\s]*volume|volume)s?`:                              AWS_EBSVOLUME_RESOURCE_TYPE,
	`(?i)ebs[-_\s]*snapshot(s)?`:                                   AWS_EBSSNAPSHOT_RESOURCE_TYPE,
	`(?i)(s3|simple[-_\s]*storage)`:                                AWS_S3_RESOURCE_TYPE,
	`(?i)bucket(s)?`:                                               AWS_S3_RESOURCE_TYPE,
	`(?i)(ami|amazon[-_\s]*machine[-_\s]*image)s?`:                 AWS_AMI_RESOURCE_TYPE,
	`(?i)(rds|relational[-_\s]*database)`:                          AWS_RDS_RESOURCE_TYPE,
	`(?i)rds[-_\s]*snapshot(s)?`:                                   AWS_RDSSNAPSHOT_RESOURCE_TYPE,
	`(?i)rds[-_\s]*cluster[-_\s]*snapshot(s)?`:                     AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE,
	`(?i)rds[-_\s]*cluster(s)?`:                                    AWS_RDSCLUSTER_RESOURCE_TYPE,
	`(?i)(dynamo[-_\s]*db|ddb)`:                                    AWS_DYNAMODB_RESOURCE_TYPE,
	`(?i)(elasticache|cache)[-_\s]*cluster(s)?`:                    AWS_ELASTICACHE_RESOURCE_TYPE,
	`(?i)route[-_\s]*table(s)?`:                                    AWS_ROUTETABLE_RESOURCE_TYPE,
	`(?i)(network[-_\s]*interface|eni)s?`:                          NETWORKINTERFACE_RESOURCE_TYPE,
	`(?i)root[-_\s]*user(s)?`:                                      AWS_ROOTUSER_RESOURCE_TYPE,
	`(?i)(sso|single[-_\s]*sign[-_\s]*on)[-_\s]*user(s)?`:          AWS_SSOUSER_RESOURCE_TYPE,
	`(?i)(elastic[-_\s]*ip|eip)s?`:                                 AWS_ELASTICIP_RESOURCE_TYPE,
	`(?i)(elastic[-_\s]*search|es)`:                                AWS_ELASTICSEARCH_RESOURCE_TYPE,
	`(?i)(sqs|simple[-_\s]*queue)`:                                 AWS_SQS_RESOURCE_TYPE,
	`(?i)(sns|simple[-_\s]*notification)`:                          AWS_SNS_RESOURCE_TYPE,
	`(?i)iam`:                                                      AWS_IAM_RESOURCE_TYPE,
	`(?i)(kms|key[-_\s]*management)`:                               AWS_KMS_RESOURCE_TYPE,
	`(?i)lambda(s)?`:                                               AWS_LAMBDA_RESOURCE_TYPE,
	`(?i)(cloudfront|cf)`:                                          AWS_CLOUDFRONT_RESOURCE_TYPE,
	`(?i)(elb|elastic[-_\s]*load[-_\s]*balancer)s?`:                AWS_ELB_RESOURCE_TYPE,
	`(?i)route[-_\s]*53`:                                           AWS_ROUTE53_RESOURCE_TYPE,
	`(?i)route[-_\s]*53[-_\s]*domain(s)?`:                          AWS_ROUTE53DOMAIN_RESOURCE_TYPE,
	`(?i)(cloud[-_\s]*formation|cfn)`:                              AWS_CLOUDFORMATION_RESOURCE_TYPE,
	`(?i)trusted[-_\s]*advisor(y)?`:                                AWS_TRUSTEDADVISORY_RESOURCE_TYPE,
	`(?i)iam[-_\s]*policy`:                                         AWS_IAMPOLICY_RESOURCE_TYPE,
	`(?i)(redis[-_\s]*memory[-_\s]*db|memorydb)[-_\s]*cluster(s)?`: AWS_REDISMEMORYDBCLUSTER_RESOURCE_TYPE,
	`(?i)iam[-_\s]*server[-_\s]*cert(ificate)?s?`:                  AWS_IAMSERVERCERT_RESOURCE_TYPE,
	`(?i)neptune[-_\s]*cluster(s)?`:                                AWS_NEPTUNECLUSTER_RESOURCE_TYPE,
	`(?i)neptune[-_\s]*instance(s)?`:                               AWS_NEPTUNEINSTANCE_RESOURCE_TYPE,
	`(?i)redshift`:                                                 AWS_REDSHIFT_RESOURCE_TYPE,
	`(?i)timestream[-_\s]*db(s)?`:                                  AWS_TIMESTREAMDB_RESOURCE_TYPE,
	`(?i)timestream[-_\s]*table(s)?`:                               AWS_TIMESTREAMTABLE_RESOURCE_TYPE,
	`(?i)(container[-_\s]*registry|ecr)`:                           AWS_CONTAINERREGISTRY_RESOURCE_TYPE,
	`(?i)container[-_\s]*repository`:                               AWS_CONTAINERREPOSITORY_RESOURCE_TYPE,
	`(?i)container[-_\s]*image(s)?`:                                AWS_CONTAINERIMAGE_RESOURCE_TYPE,
	`(?i)(ecs[-_\s]*fargate|fargate)`:                              AWS_ECSFARGATE_RESOURCE_TYPE,
	`(?i)(eks[-_\s]*cluster|kubernetes[-_\s]*cluster)s?`:           AWS_EKSCLUSTER_RESOURCE_TYPE,
	`(?i)sagemaker[-_\s]*artifact(s)?`:                             AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE,
	`(?i)sagemaker[-_\s]*trial[-_\s]*component(s)?`:                AWS_SAGEMAKERTRIALCOMPONENT_RESOURCE_TYPE,
}

var AzureKeywordToResourceTypeMap = map[string]string{
	`(?i)(resource[-_\s]*group|rg)s?`:                                            AZURE_RG_RESOURCE_TYPE,
	`(?i)(application|app)s?`:                                                    AZURE_GRAPHAPP_RESOURCE_TYPE,
	`(?i)(ad[-_\s]*user|azure[-_\s]*ad[-_\s]*user)s?`:                            AZURE_ADUSER_RESOURCE_TYPE,
	`(?i)(network[-_\s]*security[-_\s]*group|nsg)s?`:                             AZURE_NSG_RESOURCE_TYPE,
	`(?i)(virtual[-_\s]*machine|vm)s?`:                                           AZURE_VM_RESOURCE_TYPE,
	`(?i)(virtual[-_\s]*network[-_\s]*subnet|vnet[-_\s]*subnet|subnet)s?`:        AZURE_SUBNET_RESOURCE_TYPE,
	`(?i)(network[-_\s]*interface|nic)s?`:                                        NETWORKINTERFACE_RESOURCE_TYPE,
	`(?i)(public[-_\s]*ip|pip|public[-_\s]*ip[-_\s]*address)es?`:                 AZURE_PUBLICIP_RESOURCE_TYPE,
	`(?i)(managed[-_\s]*disk|disk)s?`:                                            AZURE_VMDISK_RESOURCE_TYPE,
	`(?i)snapshot(s)?`:                                                           AZURE_SNAPSHOT_RESOURCE_TYPE,
	`(?i)(storage[-_\s]*account|sa)s?`:                                           AZURE_STORAGEACCOUNT_RESOURCE_TYPE,
	`(?i)(mysql|my[-_\s]*sql)[-_\s]*(server|database)?s?`:                        AZURE_MYSQL_RESOURCE_TYPE,
	`(?i)(sql[-_\s]*db|sql[-_\s]*database)s?`:                                    AZURE_SQLDB_RESOURCE_TYPE,
	`(?i)sql[-_\s]*server(s)?`:                                                   AZURE_SQLSERVER_RESOURCE_TYPE,
	`(?i)(cosmos[-_\s]*db|cosmosdb)s?`:                                           AZURE_COSMOSDB_RESOURCE_TYPE,
	`(?i)(databrick|databricks)[-_\s]*(workspace)?s?`:                            AZURE_DATABRICKWORKSPACE_RESOURCE_TYPE,
	`(?i)(virtual[-_\s]*network|vnet)s?`:                                         AZURE_VIRTUALNETWORK_RESOURCE_TYPE,
	`(?i)(security[-_\s]*center|defender)`:                                       AZURE_SECURITYCENTER_RESOURCE_TYPE,
	`(?i)(key[-_\s]*vault|kv)s?`:                                                 AZURE_KEYVAULT_RESOURCE_TYPE,
	`(?i)(load[-_\s]*balancer|lb)s?`:                                             LOADBALANCER_RESOURCE_TYPE,
	`(?i)maria[-_\s]*db[-_\s]*(server|database)?s?`:                              AZURE_MARIADB_RESOURCE_TYPE,
	`(?i)(postgres|postgresql)[-_\s]*(server|database)?s?`:                       AZURE_POSTGRES_RESOURCE_TYPE,
	`(?i)(redis[-_\s]*cache|redis)s?`:                                            AZURE_REDISCACHE_RESOURCE_TYPE,
	`(?i)deployment(s)?`:                                                         AZURE_DEPLOYMENT_RESOURCE_TYPE,
	`(?i)route[-_\s]*table(s)?`:                                                  AZURE_ROUTETABLES_RESOURCE_TYPE,
	`(?i)network[-_\s]*watcher(s)?`:                                              AZURE_NETWORKWATCHER_RESOURCE_TYPE,
	`(?i)(group|groups)`:                                                         AZURE_GROUPS_RESOURCE_TYPE,
	`(?i)(policy|policies)`:                                                      AZURE_POLICY_RESOURCE_TYPE,
	`(?i)device(s)?`:                                                             AZURE_DEVICE_RESOURCE_TYPE,
	`(?i)ssh[-_\s]*public[-_\s]*key(s)?`:                                         AZURE_SSHPUBLICKEYS_RESOURCE_TYPE,
	`(?i)(container[-_\s]*registry|acr)s?`:                                       AZURE_CONTAINERREGISTRY_RESOURCE_TYPE,
	`(?i)(aks[-_\s]*cluster|kubernetes[-_\s]*cluster)s?`:                         AZURE_AKSCLUSTER_RESOURCE_TYPE,
	`(?i)\bopenai\b`:                                                             AZURE_OPENAI_RESOURCE_TYPE,
	`(?i)openai[-_\s]*model(s)?`:                                                 AZURE_OPENAIMODEL_RESOURCE_TYPE,
	`(?i)openai[-_\s]*deployment(s)?`:                                            AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE,
	`(?i)(ai[-_\s]*multi[-_\s]*service[-_\s]*account|cognitive[-_\s]*service)s?`: AZURE_AIMULTISERVICEACCOUNT_RESOURCE_TYPE,
	`(?i)(ml[-_\s]*workspace|machine[-_\s]*learning[-_\s]*workspace)s?`:          AZURE_MLWORKSPACE_RESOURCE_TYPE,
	`(?i)video[-_\s]*indexer(s)?`:                                                AZURE_VIDEOINDEXER_RESOURCE_TYPE,
	`(?i)speech[-_\s]*service(s)?`:                                               AZURE_SPEECHSERVICE_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*endpoint(s)?`:                                  AZURE_MLWORKSPACEENDPOINTS_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*compute[-_\s]*machine(s)?`:                     AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*deployment(s)?`:                                AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*model(s)?`:                                     AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*job(s)?`:                                       AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE,
	`(?i)ml[-_\s]*workspace[-_\s]*datastore(s)?`:                                 AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE,
	`(?i)ml[-_\s]*feature[-_\s]*store(s)?`:                                       AZURE_MLFEATURESTORE_RESOURCE_TYPE,
	`(?i)ml[-_\s]*project[-_\s]*index(es)?`:                                      AZURE_MLPROJECTINDEX_RESOURCE_TYPE,
	`(?i)ml[-_\s]*hub[-_\s]*connection(s)?`:                                      AZURE_MLHUBCONNECTION_RESOURCE_TYPE,
}

var GCPKeywordToResourceTypeMap = map[string]string{
	`(?i)(gcp[-_\s]*user|iam[-_\s]*user)s?`:                                          GCP_IAM_RESOURCE_TYPE,
	`(?i)service[-_\s]*account(s)?`:                                                  GCP_SERVICEACCOUNT_RESOURCE_TYPE,
	`(?i)firewall[-_\s]*rule(s)?`:                                                    GCP_FIREWALL_RESOURCE_TYPE,
	`(?i)(vm[-_\s]*instance|compute[-_\s]*instance|instance)s?`:                      GCP_INSTANCE_RESOURCE_TYPE,
	`(?i)(gke[-_\s]*cluster|kubernetes[-_\s]*cluster)s?`:                             GCP_GKECLUSTER_RESOURCE_TYPE,
	`(?i)(node|gke[-_\s]*node)s?`:                                                    GCP_GKENODE_RESOURCE_TYPE,
	`(?i)(vm[-_\s]*disk|compute[-_\s]*disk|disk)s?`:                                  GCP_VMDISK_RESOURCE_TYPE,
	`(?i)(network|vpc)s?`:                                                            GCP_NETWORK_RESOURCE_TYPE,
	`(?i)(sub[-_\s]*network|subnet)s?`:                                               GCP_SUBNETWORK_RESOURCE_TYPE,
	`(?i)(disk[-_\s]*snapshot|snapshot)s?`:                                           GCP_DISK_SNAPSHOT_RESOURCE_TYPE,
	`(?i)(cloud[-_\s]*storage|gcs)`:                                                  GCP_CLOUDSTORAGE_RESOURCE_TYPE,
	`(?i)bucket(s)?`:                                                                 GCP_CLOUDSTORAGE_RESOURCE_TYPE,
	`(?i)(sql[-_\s]*database|cloud[-_\s]*sql)s?`:                                     GCP_SQLDB_RESOURCE_TYPE,
	`(?i)bigquery[-_\s]*table(s)?`:                                                   GCP_BIGQUERYTABLE_RESOURCE_TYPE,
	`(?i)bigquery[-_\s]*dataset(s)?`:                                                 GCP_BIGQUERYDATASET_RESOURCE_TYPE,
	`(?i)(big[-_\s]*table|bigtable)s?`:                                               GCP_BIGTABLE_RESOURCE_TYPE,
	`(?i)(big[-_\s]*table[-_\s]*cluster|bigtable[-_\s]*cluster)s?`:                   GCP_BIGTABLECLUSTER_RESOURCE_TYPE,
	`(?i)spanner[-_\s]*database(s)?`:                                                 GCP_SPANNERDB_RESOURCE_TYPE,
	`(?i)spanner[-_\s]*instance(s)?`:                                                 GCP_SPANNERDBINSTANCE_RESOURCE_TYPE,
	`(?i)dataproc[-_\s]*cluster(s)?`:                                                 GCP_DATAPROCCLUSTER_RESOURCE_TYPE,
	`(?i)dataflow[-_\s]*job(s)?`:                                                     GCP_DATAFLOWJOB_RESOURCE_TYPE,
	`(?i)auto[-_\s]*scaler(s)?`:                                                      GCP_AUTOSCALER_RESOURCE_TYPE,
	`(?i)instance[-_\s]*group[-_\s]*manager(s)?`:                                     GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE,
	`(?i)instance[-_\s]*group(s)?`:                                                   GCP_INSTANCEGROUP_RESOURCE_TYPE,
	`(?i)(load[-_\s]*balancer|lb)s?`:                                                 LOADBALANCER_RESOURCE_TYPE,
	`(?i)backend[-_\s]*service(s)?`:                                                  GCP_BACKENDSERVICE_RESOURCE_TYPE,
	`(?i)dns[-_\s]*managed[-_\s]*zone(s)?`:                                           GCP_DNSMANAGEDZONE_RESOURCE_TYPE,
	`(?i)(function|cloud[-_\s]*function)s?`:                                          GCP_FUNCTION_RESOURCE_TYPE,
	`(?i)(gcp[-_\s]*iam|iam)`:                                                        GCP_GCPIAM_RESOURCE_TYPE,
	`(?i)(image|compute[-_\s]*image)s?`:                                              GCP_IMAGE_RESOURCE_TYPE,
	`(?i)iam[-_\s]*policy`:                                                           GCP_IAMPOLICY_RESOURCE_TYPE,
	`(?i)ssl[-_\s]*policy`:                                                           GCP_SSLPOLICY_RESOURCE_TYPE,
	`(?i)(lb[-_\s]*target[-_\s]*https[-_\s]*proxy|target[-_\s]*https[-_\s]*proxy)s?`: GCP_LBTARGETHTTPSPROXY_RESOURCE_TYPE,
	`(?i)pubsub[-_\s]*snapshot(s)?`:                                                  GCP_PUBSUBSNAPSHOT_RESOURCE_TYPE,
	`(?i)pubsub[-_\s]*subscription(s)?`:                                              GCP_PUBSUBSUBSCRIPTION_RESOURCE_TYPE,
	`(?i)pubsub[-_\s]*topic(s)?`:                                                     GCP_PUBSUBTOPIC_RESOURCE_TYPE,
	`(?i)(kms|key[-_\s]*management[-_\s]*service)`:                                   GCP_KMS_RESOURCE_TYPE,
	`(?i)artifact[-_\s]*repository`:                                                  GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE,
	`(?i)docker[-_\s]*image(s)?`:                                                     GCP_DOCKERIMAGE_RESOURCE_TYPE,
	`(?i)container[-_\s]*repository`:                                                 GCP_CONTAINERREPOSITORY_RESOURCE_TYPE,
	`(?i)container[-_\s]*image(s)?`:                                                  GCP_CONTAINERIMAGE_RESOURCE_TYPE,
}
