package common

import (
	"encoding/json"
	"regexp"
	"strings"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func ProcessTraceFile(traceContent, tenantID string, deployInfo DeplopymentInfo, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) {

	lowerContent := strings.ToLower(traceContent)

	if strings.Contains(lowerContent, "no changes. infrastructure is up-to-date") ||
		strings.Contains(lowerContent, "no changes. your infrastructure matches the configuration") ||
		strings.Contains(lowerContent, "apply complete! resources: 0 added, 0 changed, 0 destroyed") || strings.Contains(lowerContent, "no objects need to be destroyed") {

		return
	}

	lines := strings.Split(lowerContent, "\n")

	resourcePatterns := []*regexp.Regexp{
		// Creation
		regexp.MustCompile(`: creation complete after .* \[id=([^\]]+)\]`),

		// Destruction
		regexp.MustCompile(`: destroying\.\.\. \[id=([^\]]+)\]`),

		// Modification
		regexp.MustCompile(`: modifying\.\.\. \[id=([^\]]+)\]`),
	}

	resourceTypeRegex := regexp.MustCompile(`[^\w.]*\.(\w+)\.[\w_]+(?:\[[^\]]*\])?[^\w]*`)

	matchFound := false
	for i := len(lines) - 1; i >= 0; i-- {
		line := lines[i]

		for _, pattern := range resourcePatterns {
			matches := pattern.FindStringSubmatch(line)
			if len(matches) > 0 {

				resourceID := ""
				resourceType := ""

				if len(matches) >= 2 {
					matchFound = true
					resourceID = matches[1]
				}

				if len(resourceID) > 0 {
					if !strings.Contains(resourceID, "organization") && !strings.Contains(resourceID, "folder") && !strings.Contains(resourceID, "project") {
						typeMatch := resourceTypeRegex.FindStringSubmatch(line)
						if len(typeMatch) > 1 {
							tfResourceType := typeMatch[1]
							resourceID, resourceType = normalizeTFResource(tfResourceType, resourceID, parentRscNameToID, missingNormalizeRsc, deployInfo)
						}
					}
				}

				if len(resourceID) <= 0 {
					continue
				}

				processResource(resourceID, tenantID, resourceType, deployInfo)
				break
			}
		}
	}

	if !matchFound {
		logger.Print(logger.INFO, "No resource found in trace file", []string{tenantID}, strings.Join(lines, "\n"))
	}

}

func processResource(resourceID, tenantID, resourceType string, deployInfo DeplopymentInfo) {

	if len(resourceID) <= 0 {
		return
	}

	priorityConfig := make(map[string]any)
	priorityConfig["jobId"] = deployInfo.JobID
	priorityConfig["projectId"] = deployInfo.ProjectID
	priorityConfig["projectName"] = deployInfo.ProjectName

	priorityConfigJsonData, err := json.Marshal(priorityConfig)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling priority config", err)
		return
	}
	priorityConfigJsonDataStr := string(priorityConfigJsonData)

	resourceEvent := ResourceEvent{
		ResourceID:   resourceID,
		ResourceName: resourceID,
		ResourceType: resourceType,
	}

	resourceUserEventDoc := ResourceUserEventDoc{
		ResourceEvent:   resourceEvent,
		Action:          "deployer",
		TenantID:        tenantID,
		User:            deployInfo.Name,
		UserType:        deployInfo.GitClient,
		EventTime:       deployInfo.EndTime,
		DerivedFrom:     DERIVEDFROM_DEPLOYMENT_TYPE,
		PriorityConfigs: priorityConfigJsonDataStr,
	}

	resourceUserEventDoc.ID = GenerateCombinedHashID(resourceUserEventDoc.TenantID, deployInfo.GitClient, resourceUserEventDoc.Action, resourceID, deployInfo.Name)

	if _, err := elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, resourceUserEventDoc, resourceUserEventDoc.ID); err != nil {
		return
	}

}
