package common

import (
	"fmt"
	"math/big"
	"strconv"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetServiceID(tenantID, lastCollectedAt string) (serviceID string) {

	serviceIDQuery := `{"_source":["serviceId"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"collectedAt":` + lastCollectedAt + `}}]}},"size":1}`

	serviceIDDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, serviceIDQuery)
	if err != nil {
		return
	}

	for _, serviceIDDoc := range serviceIDDocs {

		if serviceIDFloat, ok := serviceIDDoc["serviceId"].(float64); ok {
			serviceID = strconv.FormatFloat(serviceIDFloat, 'f', 0, 64)
			return
		}
	}

	return
}

func GetLastCollectedAt(tenantID, serviceID string) (lastCollectedAt string) {

	lastCollectedAtQuery := `{"_source":["collectedAt"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"serviceId":` + serviceID + `}}]}},"size":1,"sort":[{"collectedAt":{"order":"desc"}}]}`

	lastCollectedAtDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, lastCollectedAtQuery)
	if err != nil {
		return
	}

	for _, lastCollectedAtDoc := range lastCollectedAtDocs {

		if lastCollected, ok := lastCollectedAtDoc["collectedAt"]; ok {

			flt, _, err := big.ParseFloat(fmt.Sprint(lastCollected), 10, 0, big.ToNearestEven)
			if err != nil {
				logger.Print(logger.ERROR, "Got error parsing big float", []string{tenantID}, fmt.Sprint(lastCollected))
				return
			}

			var i = new(big.Int)
			i, acc := flt.Int(i)
			if acc == big.Exact {
				lastCollectedAt = i.String()
			}
		}
	}

	return
}
