package common

import "github.com/precize/transport"

type AccountResponse struct {
	transport.ServerResponseInfo
	Data AccountData `json:"data"`
}
type AccountData struct {
	Accounts   []Account `json:"accounts"`
	AccountIDs []string  `json:"accountIds"`
}

// Account represents an AWS account entity with possible child accounts
type Account struct {
	EntityID    string    `json:"entityId"`
	Name        string    `json:"name"`
	AccountType string    `json:"accountType"`
	RiskStatus  string    `json:"riskStatus"`
	Childs      []Account `json:"childs,omitempty"` // Using omitempty to handle null case
}
