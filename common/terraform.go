package common

import (
	"strings"

	"github.com/precize/logger"
)

const (
	RESOURCE_MODE_MANAGED = "managed"
)

var (
	AzureTfExcludedAppIDs       = []string{"a57aca87-cbc0-4f3c-8b9e-dc095fdc8978", "3090ab82-f1c1-4cdf-af2c-5d7a6f3e2cc7", "00000009-0000-0000-c000-000000000000", "cf36b471-5b44-428c-9ce7-313bf84528de", "00b41c95-dab0-4487-9791-b9d2c32c80f2", "d176f6e7-38e5-40c9-8a78-3998aab820e7", "35d54a08-36c9-4847-9018-93934c62740c", "00000004-0000-0ff1-ce00-000000000000", "a3b79187-70b2-4139-83f9-6016c58cd27b", "94c63fef-13a3-47bc-8074-75af8c65887a", "e1ef36fd-b883-4dbf-97f0-9ece4b576fc6", "e64aa8bc-8eb4-40e2-898b-cf261a25954f", "0000000c-0000-0000-c000-000000000000", "62256cef-54c0-4cb4-bcac-4c67989bdc40", "2d4d3d8e-2be3-4bef-9f87-7875a61c29de", "ae8e128e-080f-4086-b0e3-4c19301ada69", "2abdc806-e091-4495-9b10-b04d93c3f040", "b677c290-cf4b-4a8e-a60e-91ba650a4abe", "6253bca8-faf2-4587-8f2f-b056d80998a7", "4345a7b9-9a63-4910-a426-35363201d503", "835b2a73-6e10-4aa5-a979-21dfda45231c", "1786c5ed-9644-47b2-8aa0-7201292175b6", "00000002-0000-0ff1-ce00-000000000000", "797f4846-ba00-4fd7-ba43-dac1f8f63013", "7b7531ad-5926-4f2d-8a1d-38495ad33e17", "d73f4b35-55c9-48c7-8b10-651f6f2acb2e", "61109738-7d2b-4a0b-9fe3-660b1ff83505", "13937bba-652e-4c46-b222-3003f4d1ff97", "23523755-3a2b-41ca-9315-f81f3f566a95", "b669c6ea-1adf-453f-b8bc-6d526592b419", "d3590ed6-52b3-4102-aeff-aad2292ab01c", "28b567f6-162c-4f54-99a0-6887f387bbcc", "a9b49b65-0a12-430b-9540-c80b3332c127", "89bee1f7-5e6e-4d8a-9f3d-ecd601259da7", "8edd93e1-2103-40b4-bd70-6e34e586362d", "00000007-0000-0000-c000-000000000000", "b6e69c34-5f1f-4c34-8cdf-7fea120b8670", "7ab7862c-4c57-491e-8a45-d52a7e023983", "cf53fce8-def6-4aeb-8d30-b158e7b1cf83", "dfe74da8-9279-44ec-8fb2-2aed9e1c73d0", "ffcb16e8-f789-467c-8ce9-f826a080d987", "c1c74fed-04c9-4704-80dc-9f79a2e515cb", "18fbca16-2224-45f6-85b0-f7bf2b39b3f3", "fdf9885b-dd37-42bf-82e5-c3129ef5a302", "9ea1ad79-fdb6-4f9a-8bc3-2b70f96e34c7", "497effe9-df71-4043-a8bb-14cf78c4b63b", "00000005-0000-0ff1-ce00-000000000000", "51be292c-a17e-4f17-9a7e-4b661fb16dd2", "4765445b-32c6-49b0-83e6-1d93765276ca", "268761a2-03f3-40df-8a8b-c3db24145b6b", "e9f49c6b-5ce5-44c8-925d-015017e9f7ad", "944f0bd1-117b-4b1c-af26-804ed95e767e", "1fec8e78-bce4-4aaf-ab1b-5451cc387264", "66a88757-258c-4c72-893c-3e8bed4d6899", "74658136-14ec-4630-ad9b-26e160ff0fc6", "60c8bde5-3167-4f92-8fdb-059f6176dc0f", "29d9ed98-a469-4536-ade2-f981bc1d605e", "871c010f-5e61-4fb1-83ac-98610a7e9110", "91ca2ca5-3b3e-41dd-ab65-809fa3dffffa", "00000002-0000-0000-c000-000000000000", "1950a258-227b-4e31-a9cf-717495945fc2", "fb78d390-0c51-40cd-8e17-fdbfab77341b", "c9a559d2-7aab-4f13-a6ed-e7e9c52aec87", "27922004-5251-4030-b22d-91ecd9a37ea4", "26a7ee05-5602-4d76-a7ba-eae8b7b67941", "45a330b1-b1ec-4cc1-9161-9f03992aa49f", "d9b8ec3a-1e4e-4e08-b3c2-5baf00c0fcb0", "2d7f3606-b07d-41d1-b9d2-0d0c9296a6e8", "00000003-0000-0000-c000-000000000000", "74bcdadc-2fdc-4bb3-8459-76d06952a0e9", "cc15fd57-2c6c-4117-a88c-83b1d56b4bbe", "bdd48c81-3a58-4ea9-849c-ebea7f6b6360", "a970bac6-63fe-4ec5-8884-8536862c42d4", "0cb7b9ec-5336-483b-bc31-b15b5788de71", "80ccca67-54bd-44ab-8625-4b79c4dc7775", "04b07795-8ddb-461a-bbee-02f9e1bf7b46", "0000001a-0000-0000-c000-000000000000", "0f698dd4-f011-4d23-a33e-b36416dcb1e6", "905fcf26-4eb7-48a0-9ff0-8dcc7194b5ba", "20a11fe0-faa8-4df5-baf2-f965f8f9972e", "fc0f3af4-6835-4174-b806-f7db311fd2f3", "b4bddae8-ab25-483e-8670-df09b9f1d0ea", "ab9b8c07-8f02-4f72-87fa-80105867a763", "08e18876-6177-487e-b8b5-cf950c1e598c", "65d91a3d-ab74-42e6-8a2f-0add61688c74", "38049638-cc2c-4cde-abe4-4479d721ed44", "00000015-0000-0000-c000-000000000000", "16aeb910-ce68-41d1-9ac3-9e1673ac9575", "4d5c2d63-cf83-4365-853c-925fd1a64357", "3c896ded-22c5-450f-91f6-3d1ef0848f6e", "0cd196ee-71bf-4fd6-a57c-b491ffd4fb1e", "95de633a-083e-42f5-b444-a4295d8e9314", "00000003-0000-0ff1-ce00-000000000000", "17d5e35f-655b-4fb0-8ae6-86356e9a49f5", "243c63a3-247d-41c5-9d83-7788c43f1c43", "98db8bd6-0cc0-4e67-9de5-f187f1cd1b41", "00000007-0000-0ff1-ce00-000000000000", "93625bc8-bfe2-437a-97e0-3d0060024faa", "1b3c667f-cde3-4090-b60b-3d2abd0117f0", "69893ee3-dd10-4b1c-832d-4870354be3d8", "c44b4083-3bb0-49c1-b47d-974e53cbdf3c", "bb2a2e3a-c5e7-4f0a-88e0-8e01fd3fc1f4", "f5eaa862-7f08-448c-9c4e-f4047d4d4521", "ee272b19-4411-433f-8f28-5c13cb6fd407", "67e3df25-268a-4324-a550-0de1c7f97287", "b23dd4db-9142-4734-867f-3577f640ad0c", "4b233688-031c-404b-9a80-a4f3f2351f90", "37182072-3c9c-4f6a-a4b3-b3f91cacffce", "00000006-0000-0ff1-ce00-000000000000", "a3475900-ccec-4a69-98f5-a65cd5dc5306", "97cb1f73-50df-47d1-8fb0-0271f2728514", "c35cb2ba-f88b-4d15-aa9d-37bd443522e1", "99b904fd-a1fe-455c-b86c-2f9fb1da7687", "5e3ce6c0-2b1f-4285-8d4b-75ee78787346", "93d53678-613d-4013-afc1-62e9e444a0a5", "26abc9a8-24f0-4b11-8234-e86ede698878"}
	terraformResourceTypesToAWS = map[string]string{
		"aws_ebs_default_kms_key":                              "EBSVolume",
		"aws_ebs_encryption_by_default":                        "EBSVolume",
		"aws_ebs_fast_snapshot_restore":                        "EBSSnapshot",
		"aws_ebs_snapshot":                                     "EBSSnapshot",
		"aws_ebs_snapshot_copy":                                "EBSSnapshot",
		"aws_ebs_snapshot_import":                              "EBSSnapshot",
		"aws_ebs_volume":                                       "EBSVolume",
		"aws_snapshot_create_volume_permission":                "EBSSnapshot",
		"aws_volume_attachment":                                "EBSVolume",
		"aws_ami":                                              "EC2",
		"aws_ami_copy":                                         "EC2",
		"aws_ami_from_instance":                                "EC2",
		"aws_ami_launch_permission":                            "EC2",
		"aws_ec2_capacity_reservation":                         "AWS::EC2::CapacityReservation",
		"aws_spot_datafeed_subscription":                       "S3",
		"aws_ec2_availability_zone_group":                      "Vpc",
		"aws_ec2_fleet":                                        "AWS::EC2::EC2Fleet",
		"aws_ec2_host":                                         "EC2",
		"aws_ec2_instance_connect_endpoint":                    "AWS::EC2::InstanceConnectEndpoint",
		"aws_ec2_instance_state":                               "EC2",
		"aws_eip":                                              "ElasticIp",
		"aws_eip_association":                                  "AWS::EC2::EIPAssociation",
		"aws_instance":                                         "EC2",
		"aws_key_pair":                                         "AWS::EC2::KeyPair",
		"aws_launch_template":                                  "AWS::EC2::LaunchTemplate",
		"aws_spot_fleet_request":                               "AWS::EC2::SpotFleet",
		"aws_spot_instance_request":                            "AWS::EC2::SpotInstanceRequest",
		"aws_ecr_pull_through_cache_rule":                      "AWS::ECR::PullThroughCacheRule",
		"aws_ecr_lifecycle_policy":                             "ContainerRepository",
		"aws_ecr_registry_policy":                              "AWS::ECR::RegistryPolicy",
		"aws_ecr_registry_scanning_configuration":              "ContainerRepository",
		"aws_ecr_replication_configuration":                    "AWS::ECR::ReplicationConfiguration",
		"aws_ecr_repository":                                   "ContainerRepository",
		"aws_ecr_repository_policy":                            "ContainerRepository",
		"aws_ecs_capacity_provider":                            "AWS::ECS::CapacityProvider",
		"aws_ecs_cluster":                                      "ECSFargate",
		"aws_ecs_cluster_capacity_providers":                   "ECSFargate",
		"aws_ecs_service":                                      "ECSFargate",
		"aws_ecs_tag":                                          "ECSFargate",
		"aws_ecs_task_definition":                              "ECSFargate",
		"aws_ecs_task_set":                                     "AWS::ECS::TaskSet",
		"aws_lb":                                               "ELB",
		"aws_lb_listener":                                      "ELB",
		"aws_lb_listener_certificate":                          "AWS::ElasticLoadBalancingV2::ListenerCertificate",
		"aws_lb_listener_rule":                                 "AWS::ElasticLoadBalancingV2::ListenerRule",
		"aws_lb_target_group":                                  "AWS::ElasticLoadBalancingV2::TargetGroup",
		"aws_lb_target_group_attachment":                       "AWS::ElasticLoadBalancingV2::TargetGroup",
		"aws_lb_trust_store":                                   "AWS::ElasticLoadBalancingV2::TrustStore",
		"aws_lb_trust_store_revocation":                        "AWS::ElasticLoadBalancingV2::TrustStoreRevocation",
		"aws_db_cluster_snapshot":                              "RDSCluster",
		"aws_db_event_subscription":                            "RDS",
		"aws_db_instance":                                      "RDS",
		"aws_db_instance_automated_backups_replication":        "RDS",
		"aws_db_instance_role_association":                     "RDS",
		"aws_db_option_group":                                  "AWS::RDS::OptionGroup",
		"aws_db_parameter_group":                               "AWS::RDS::DBParameterGroup",
		"aws_db_proxy":                                         "AWS::RDS::DBProxy",
		"aws_db_proxy_default_target_group":                    "AWS::RDS::DBProxy",
		"aws_db_proxy_endpoint":                                "AWS::RDS::DBProxy",
		"aws_db_proxy_target":                                  "AWS::RDS::DBProxy",
		"aws_db_snapshot":                                      "RDSSnapshot",
		"aws_db_snapshot_copy":                                 "RDSSnapshot",
		"aws_db_subnet_group":                                  "AWS::RDS::DBSubnetGroup",
		"aws_rds_cluster":                                      "RDSCluster",
		"aws_rds_cluster_activity_stream":                      "RDSCluster",
		"aws_rds_cluster_endpoint":                             "RDSCluster",
		"aws_rds_cluster_instance":                             "RDSCluster",
		"aws_rds_cluster_parameter_group":                      "RDSCluster",
		"aws_rds_cluster_role_association":                     "RDSCluster",
		"aws_rds_custom_db_engine_version":                     "RDSCluster",
		"aws_rds_export_task":                                  "RDSCluster",
		"aws_rds_global_cluster":                               "RDSCluster",
		"aws_rds_reserved_instance":                            "RDS",
		"aws_appmesh_gateway_route":                            "AWS::AppMesh::GatewayRoute",
		"aws_appmesh_mesh":                                     "AWS::AppMesh::Mesh",
		"aws_appmesh_route":                                    "AWS::AppMesh::Route",
		"aws_appmesh_virtual_gateway":                          "AWS::AppMesh::VirtualGateway",
		"aws_appmesh_virtual_node":                             "AWS::AppMesh::VirtualNode",
		"aws_appmesh_virtual_router":                           "AWS::AppMesh::VirtualRouter",
		"aws_appmesh_virtual_service":                          "AWS::AppMesh::VirtualService",
		"aws_apprunner_service":                                "AWS::AppRunner::Service",
		"aws_appstream_directory_config":                       "AWS::AppStream::DirectoryConfig",
		"aws_appstream_fleet":                                  "AWS::AppStream::Fleet",
		"aws_appstream_image_builder":                          "AWS::AppStream::ImageBuilder",
		"aws_appstream_stack":                                  "AWS::AppStream::Stack",
		"aws_appstream_user":                                   "AWS::AppStream::User",
		"aws_appsync_api_cache":                                "AWS::AppSync::ApiCache",
		"aws_appsync_api_key":                                  "AWS::AppSync::ApiKey",
		"aws_appsync_datasource":                               "AWS::AppSync::DataSource",
		"aws_appsync_domain_name":                              "AWS::AppSync::DomainName",
		"aws_appsync_domain_name_api_association":              "AWS::AppSync::DomainNameApiAssociation",
		"aws_appsync_graphql_api":                              "AWS::AppSync::GraphQLApi",
		"aws_appsync_resolver":                                 "AWS::AppSync::Resolver",
		"aws_athena_named_query":                               "AWS::Athena::NamedQuery",
		"aws_athena_workgroup":                                 "AWS::Athena::WorkGroup",
		"aws_batch_compute_environment":                        "AWS::Batch::ComputeEnvironment",
		"aws_batch_job_definition":                             "AWS::Batch::JobDefinition",
		"aws_batch_job_queue":                                  "AWS::Batch::JobQueue",
		"aws_batch_scheduling_policy":                          "AWS::Batch::SchedulingPolicy",
		"aws_budgets_budget":                                   "AWS::Budgets::BudgetsAction",
		"aws_cloud9_environment_ec2":                           "AWS::Cloud9::EnvironmentEC2",
		"aws_cloudformation_stack":                             "CloudFormation",
		"aws_cloudformation_stack_set":                         "AWS::CloudFormation::StackSet",
		"aws_cloudfront_cache_policy":                          "CloudFront",
		"aws_cloudfront_distribution":                          "CloudFront",
		"aws_cloudfront_function":                              "CloudFront",
		"aws_cloudfront_key_group":                             "CloudFront",
		"aws_cloudfront_origin_request_policy":                 "CloudFront",
		"aws_cloudfront_public_key":                            "CloudFront",
		"aws_cloudfront_realtime_log_config":                   "CloudFront",
		"aws_cloudfront_response_headers_policy":               "CloudFront",
		"aws_cloudwatch_composite_alarm":                       "AWS::CloudWatch::Alarm",
		"aws_cloudwatch_dashboard":                             "logs.amazonaws.com",
		"aws_cloudwatch_event_api_destination":                 "events.amazonaws.com",
		"aws_cloudwatch_event_archive":                         "events.amazonaws.com",
		"aws_cloudwatch_event_connection":                      "events.amazonaws.com",
		"aws_cloudwatch_event_rule":                            "events.amazonaws.com",
		"aws_cloudwatch_log_destination":                       "logs.amazonaws.com",
		"aws_cloudwatch_log_group":                             "logs.amazonaws.com",
		"aws_cloudwatch_log_metric_filter":                     "logs.amazonaws.com",
		"aws_cloudwatch_log_resource_policy":                   "logs.amazonaws.com",
		"aws_cloudwatch_log_stream":                            "logs.amazonaws.com",
		"aws_cloudwatch_log_subscription_filter":               "logs.amazonaws.com",
		"aws_cloudwatch_metric_alarm":                          "AWS::CloudWatch::Alarm",
		"aws_cloudwatch_metric_stream":                         "AWS::CloudWatch::MetricStream",
		"aws_codeartifact_domain":                              "AWS::CodeArtifact::Domain",
		"aws_codeartifact_repository":                          "AWS::CodeArtifact::Repository",
		"aws_codebuild_project":                                "AWS::CodeBuild::Project",
		"aws_codebuild_report_group":                           "AWS::CodeBuild::ReportGroup",
		"aws_codebuild_source_credential":                      "AWS::CodeBuild::SourceCredential",
		"aws_codecommit_repository":                            "AWS::CodeCommit::Repository",
		"aws_codedeploy_deployment_config":                     "AWS::CodeDeploy::DeploymentConfig",
		"aws_codedeploy_deployment_group":                      "AWS::CodeDeploy::DeploymentGroup",
		"aws_codepipeline_webhook":                             "AWS::CodePipeline::Pipeline",
		"aws_codestarconnections_connection":                   "AWS::CodeStarConnections::Connection",
		"aws_codestarnotifications_notification_rule":          "AWS::CodeStarNotifications::NotificationRule",
		"aws_cognito_identity_pool":                            "AWS::Cognito::IdentityPool",
		"aws_cognito_identity_pool_roles_attachment":           "AWS::Cognito::IdentityPoolRoleAttachment",
		"aws_cognito_user_group":                               "AWS::Cognito::UserPoolGroup",
		"aws_cognito_user_pool":                                "AWS::Cognito::UserPool",
		"aws_cognito_user_pool_client":                         "AWS::Cognito::UserPoolClient",
		"aws_cognito_user_pool_domain":                         "AWS::Cognito::UserPoolDomain",
		"aws_config_config_rule":                               "AWS::Config::ConfigRule",
		"aws_config_configuration_aggregator":                  "AWS::Config::ConfigurationAggregator",
		"aws_config_configuration_recorder":                    "AWS::Config::ConfigurationRecorder",
		"aws_config_conformance_pack":                          "AWS::Config::ConformancePackCompliance",
		"aws_config_delivery_channel":                          "AWS::Config::DeliveryChannel",
		"aws_config_organization_conformance_pack":             "AWS::Config::ConformancePackCompliance",
		"aws_config_remediation_configuration":                 "AWS::Config::RemediationConfiguration",
		"aws_connect_contact_flow":                             "AWS::Connect::ContactFlow",
		"aws_connect_contact_flow_module":                      "AWS::Connect::ContactFlowModule",
		"aws_connect_hours_of_operation":                       "AWS::Connect::HoursOfOperation",
		"aws_connect_quick_connect":                            "AWS::Connect::QuickConnect",
		"aws_cur_report_definition":                            "AWS::CUR::ReportDefinition",
		"aws_datapipeline_pipeline":                            "AWS::DataPipeline::Pipeline",
		"aws_datasync_agent":                                   "AWS::DataSync::Agent",
		"aws_datasync_location_efs":                            "AWS::DataSync::LocationEFS",
		"aws_datasync_location_hdfs":                           "AWS::DataSync::LocationHDFS",
		"aws_datasync_location_nfs":                            "AWS::DataSync::LocationNFS",
		"aws_datasync_location_s3":                             "AWS::DataSync::LocationS3",
		"aws_datasync_location_smb":                            "AWS::DataSync::LocationSMB",
		"aws_datasync_task":                                    "AWS::DataSync::Task",
		"aws_dax_cluster":                                      "AWS::DAX::Cluster",
		"aws_dax_parameter_group":                              "AWS::DAX::ParameterGroup",
		"aws_dax_subnet_group":                                 "AWS::DAX::SubnetGroup",
		"aws_detective_graph":                                  "AWS::Detective::Graph",
		"aws_devicefarm_device_pool":                           "AWS::DeviceFarm::DevicePool",
		"aws_devicefarm_instance_profile":                      "AWS::DeviceFarm::InstanceProfile",
		"aws_devicefarm_network_profile":                       "AWS::DeviceFarm::NetworkProfile",
		"aws_devicefarm_project":                               "AWS::DeviceFarm::Project",
		"aws_dlm_lifecycle_policy":                             "AWS::DLM::LifecyclePolicy",
		"aws_dms_certificate":                                  "AWS::DMS::Certificate",
		"aws_dms_endpoint":                                     "AWS::DMS::Endpoint",
		"aws_dms_event_subscription":                           "AWS::DMS::EventSubscription",
		"aws_dms_replication_instance":                         "AWS::DMS::ReplicationInstance",
		"aws_dms_replication_subnet_group":                     "AWS::DMS::ReplicationSubnetGroup",
		"aws_dms_replication_task":                             "AWS::DMS::ReplicationTask",
		"aws_dynamodb_global_table":                            "DynamoDB",
		"aws_dynamodb_table":                                   "DynamoDB",
		"aws_efs_access_point":                                 "AWS::EFS::AccessPoint",
		"aws_efs_file_system":                                  "AWS::EFS::FileSystem",
		"aws_efs_mount_target":                                 "AWS::EFS::MountTarget",
		"aws_eks_addon":                                        "AWS::EKS::Addon",
		"aws_eks_cluster":                                      "EKSCluster",
		"aws_eks_fargate_profile":                              "AWS::EKS::FargateProfile",
		"aws_eks_node_group":                                   "eks.amazonaws.com",
		"aws_elastic_beanstalk_application":                    "AWS::ElasticBeanstalk::Application",
		"aws_elastic_beanstalk_application_version":            "AWS::ElasticBeanstalk::ApplicationVersion",
		"aws_elastic_beanstalk_configuration_template":         "AWS::ElasticBeanstalk::ConfigurationTemplate",
		"aws_elastic_beanstalk_environment":                    "AWS::ElasticBeanstalk::Environment",
		"aws_elasticache_global_replication_group":             "AWS::ElastiCache::GlobalReplicationGroup",
		"aws_elasticache_parameter_group":                      "AWS::ElastiCache::ParameterGroup",
		"aws_elasticache_replication_group":                    "AWS::ElastiCache::ReplicationGroup",
		"aws_elasticache_security_group":                       "AWS::ElastiCache::SecurityGroup",
		"aws_elasticache_subnet_group":                         "AWS::ElastiCache::SubnetGroup",
		"aws_elasticache_user":                                 "AWS::ElastiCache::User",
		"aws_elasticache_user_group":                           "AWS::ElastiCache::UserGroup",
		"aws_elasticsearch_domain":                             "ElasticSearch",
		"aws_elb":                                              "ELB",
		"aws_emr_cluster":                                      "AWS::EMR::Cluster",
		"aws_emr_security_configuration":                       "AWS::EMR::SecurityConfiguration",
		"aws_emr_studio":                                       "AWS::EMR::Studio",
		"aws_emr_studio_session_mapping":                       "AWS::EMR::StudioSessionMapping",
		"aws_flow_log":                                         "AWS::EC2::FlowLog",
		"aws_fms_policy":                                       "AWS::FMS::Policy",
		"aws_gamelift_alias":                                   "AWS::GameLift::Alias",
		"aws_gamelift_build":                                   "AWS::GameLift::Build",
		"aws_gamelift_fleet":                                   "AWS::GameLift::Fleet",
		"aws_gamelift_game_session_queue":                      "AWS::GameLift::GameSessionQueue",
		"aws_globalaccelerator_accelerator":                    "AWS::GlobalAccelerator::Accelerator",
		"aws_globalaccelerator_endpoint_group":                 "AWS::GlobalAccelerator::EndpointGroup",
		"aws_globalaccelerator_listener":                       "AWS::GlobalAccelerator::Listener",
		"aws_glue_classifier":                                  "AWS::Glue::Classifier",
		"aws_glue_connection":                                  "AWS::Glue::Connection",
		"aws_glue_crawler":                                     "AWS::Glue::Crawler",
		"aws_glue_data_catalog_encryption_settings":            "AWS::Glue::DataCatalogEncryptionSettings",
		"aws_glue_dev_endpoint":                                "AWS::Glue::DevEndpoint",
		"aws_glue_job":                                         "AWS::Glue::Job",
		"aws_glue_ml_transform":                                "AWS::Glue::MLTransform",
		"aws_glue_partition":                                   "AWS::Glue::Partition",
		"aws_glue_registry":                                    "AWS::Glue::Registry",
		"aws_glue_schema":                                      "AWS::Glue::Schema",
		"aws_glue_security_configuration":                      "AWS::Glue::SecurityConfiguration",
		"aws_glue_trigger":                                     "AWS::Glue::Trigger",
		"aws_glue_workflow":                                    "AWS::Glue::Workflow",
		"aws_guardduty_detector":                               "AWS::GuardDuty::Detector",
		"aws_guardduty_filter":                                 "AWS::GuardDuty::Filter",
		"aws_guardduty_ipset":                                  "AWS::GuardDuty::IPSet",
		"aws_guardduty_member":                                 "AWS::GuardDuty::Member",
		"aws_guardduty_threatintelset":                         "AWS::GuardDuty::ThreatIntelSet",
		"aws_iam_access_key":                                   "User",
		"aws_iam_group":                                        "IAM",
		"aws_iam_instance_profile":                             "AWS::IAM::InstanceProfile",
		"aws_iam_policy":                                       "IAMPolicy",
		"aws_iam_role":                                         "IAMRole",
		"aws_iam_saml_provider":                                "AWS::IAM::SAMLProvider",
		"aws_iam_server_certificate":                           "AWS::IAM::ServerCertificate",
		"aws_iam_service_linked_role":                          "AWS::IAM::ServiceLinkedRole",
		"aws_iam_user":                                         "User",
		"aws_imagebuilder_component":                           "AWS::ImageBuilder::Component",
		"aws_imagebuilder_distribution_configuration":          "AWS::ImageBuilder::DistributionConfiguration",
		"aws_imagebuilder_image":                               "AWS::ImageBuilder::Image",
		"aws_imagebuilder_image_pipeline":                      "AWS::ImageBuilder::ImagePipeline",
		"aws_imagebuilder_image_recipe":                        "AWS::ImageBuilder::ImageRecipe",
		"aws_imagebuilder_infrastructure_configuration":        "AWS::ImageBuilder::InfrastructureConfiguration",
		"aws_inspector_assessment_target":                      "AWS::Inspector::AssessmentTarget",
		"aws_inspector_assessment_template":                    "AWS::Inspector::AssessmentTemplate",
		"aws_inspector_resource_group":                         "AWS::Inspector::ResourceGroup",
		"aws_internet_gateway":                                 "AWS::EC2::InternetGateway",
		"aws_iot_authorizer":                                   "AWS::IoT::Authorizer",
		"aws_iot_certificate":                                  "AWS::IoT::Certificate",
		"aws_iot_policy":                                       "AWS::IoT::Policy",
		"aws_iot_thing":                                        "AWS::IoT::Thing",
		"aws_iot_thing_principal_attachment":                   "AWS::IoT::ThingPrincipalAttachment",
		"aws_iot_topic_rule":                                   "AWS::IoT::TopicRule",
		"aws_kinesis_analytics_application":                    "AWS::KinesisAnalytics::Application",
		"aws_kinesis_firehose_delivery_stream":                 "AWS::KinesisFirehose::DeliveryStream",
		"aws_kinesis_stream":                                   "AWS::Kinesis::Stream",
		"aws_kinesis_stream_consumer":                          "AWS::Kinesis::StreamConsumer",
		"aws_kinesis_video_stream":                             "AWS::KinesisVideo::Stream",
		"aws_kinesisanalyticsv2_application":                   "AWS::KinesisAnalyticsV2::Application",
		"aws_kms_alias":                                        "AWS::KMS::Alias",
		"aws_kms_key":                                          "KMS",
		"aws_kms_replica_key":                                  "AWS::KMS::ReplicaKey",
		"aws_lakeformation_data_lake_settings":                 "AWS::LakeFormation::DataLakeSettings",
		"aws_lakeformation_permissions":                        "AWS::LakeFormation::Permissions",
		"aws_lakeformation_resource":                           "AWS::LakeFormation::Resource",
		"aws_lambda_alias":                                     "AWS::Lambda::Alias",
		"aws_lambda_code_signing_config":                       "AWS::Lambda::CodeSigningConfig",
		"aws_lambda_event_source_mapping":                      "AWS::Lambda::EventSourceMapping",
		"aws_lambda_function":                                  "Lambda",
		"aws_lambda_layer_version":                             "AWS::Lambda::LayerVersion",
		"aws_lambda_layer_version_permission":                  "AWS::Lambda::LayerVersionPermission",
		"aws_lambda_permission":                                "AWS::Lambda::Permission",
		"aws_launch_configuration":                             "AWS::AutoScaling::LaunchConfiguration",
		"aws_lex_bot":                                          "AWS::Lex::Bot",
		"aws_lex_bot_alias":                                    "AWS::Lex::BotAlias",
		"aws_lightsail_instance":                               "AWS::Lightsail::Instance",
		"aws_lightsail_static_ip":                              "AWS::Lightsail::StaticIp",
		"aws_media_convert_queue":                              "AWS::MediaConvert::Queue",
		"aws_media_package_channel":                            "AWS::MediaPackage::Channel",
		"aws_media_store_container":                            "AWS::MediaStore::Container",
		"aws_memorydb_acl":                                     "AWS::MemoryDB::ACL",
		"aws_memorydb_cluster":                                 "AWS::MemoryDB::Cluster",
		"aws_memorydb_parameter_group":                         "AWS::MemoryDB::ParameterGroup",
		"aws_memorydb_subnet_group":                            "AWS::MemoryDB::SubnetGroup",
		"aws_memorydb_user":                                    "AWS::MemoryDB::User",
		"aws_msk_cluster":                                      "AWS::MSK::Cluster",
		"aws_mwaa_environment":                                 "AWS::MWAA::Environment",
		"aws_nat_gateway":                                      "AWS::EC2::NatGateway",
		"aws_neptune_cluster_parameter_group":                  "AWS::Neptune::DBClusterParameterGroup",
		"aws_network_acl":                                      "AWS::EC2::NetworkAcl",
		"aws_networkfirewall_firewall":                         "AWS::NetworkFirewall::Firewall",
		"aws_networkfirewall_firewall_policy":                  "AWS::NetworkFirewall::FirewallPolicy",
		"aws_networkfirewall_logging_configuration":            "AWS::NetworkFirewall::LoggingConfiguration",
		"aws_networkfirewall_rule_group":                       "AWS::NetworkFirewall::RuleGroup",
		"aws_opsworks_instance":                                "AWS::OpsWorks::Instance",
		"aws_opsworks_stack":                                   "AWS::OpsWorks::Stack",
		"aws_opsworks_user_profile":                            "AWS::OpsWorks::UserProfile",
		"aws_pinpoint_adm_channel":                             "AWS::Pinpoint::ADMChannel",
		"aws_pinpoint_apns_channel":                            "AWS::Pinpoint::APNSChannel",
		"aws_pinpoint_apns_sandbox_channel":                    "AWS::Pinpoint::APNSSandboxChannel",
		"aws_pinpoint_apns_voip_channel":                       "AWS::Pinpoint::APNSVoipChannel",
		"aws_pinpoint_apns_voip_sandbox_channel":               "AWS::Pinpoint::APNSVoipSandboxChannel",
		"aws_pinpoint_app":                                     "AWS::Pinpoint::App",
		"aws_pinpoint_baidu_channel":                           "AWS::Pinpoint::BaiduChannel",
		"aws_pinpoint_email_channel":                           "AWS::Pinpoint::EmailChannel",
		"aws_pinpoint_event_stream":                            "AWS::Pinpoint::EventStream",
		"aws_pinpoint_gcm_channel":                             "AWS::Pinpoint::GCMChannel",
		"aws_pinpoint_sms_channel":                             "AWS::Pinpoint::SMSChannel",
		"aws_qldb_ledger":                                      "AWS::QLDB::Ledger",
		"aws_quicksight_data_source":                           "AWS::QuickSight::DataSource",
		"aws_ram_resource_share":                               "AWS::RAM::ResourceShare",
		"aws_redshift_cluster":                                 "RedShift",
		"aws_redshift_parameter_group":                         "AWS::Redshift::ClusterParameterGroup",
		"aws_redshift_security_group":                          "SecurityGroup",
		"aws_redshift_subnet_group":                            "Subnet",
		"aws_resourcegroups_group":                             "AWS::ResourceGroups::Group",
		"aws_route":                                            "AWS::EC2::Route",
		"aws_route_table":                                      "RouteTable",
		"aws_route_table_association":                          "RouteTable",
		"aws_route53_health_check":                             "AWS::Route53::HealthCheck",
		"aws_route53_key_signing_key":                          "AWS::Route53::KeySigningKey",
		"aws_route53_record":                                   "AWS::Route53::RecordSet",
		"aws_route53_resolver_firewall_domain_list":            "AWS::Route53Resolver::FirewallDomainList",
		"aws_route53_resolver_firewall_rule_group":             "AWS::Route53Resolver::FirewallRuleGroup",
		"aws_route53_resolver_firewall_rule_group_association": "AWS::Route53Resolver::FirewallRuleGroupAssociation",
		"aws_route53_zone":                                     "Route53",
		"aws_route53recoveryreadiness_cell":                    "AWS::Route53RecoveryReadiness::Cell",
		"aws_route53recoveryreadiness_readiness_check":         "AWS::Route53RecoveryReadiness::ReadinessCheck",
		"aws_route53recoveryreadiness_recovery_group":          "AWS::Route53RecoveryReadiness::RecoveryGroup",
		"aws_route53recoveryreadiness_resource_set":            "AWS::Route53RecoveryReadiness::ResourceSet",
		"aws_s3_access_point":                                  "S3",
		"aws_s3_bucket":                                        "S3",
		"aws_s3_bucket_policy":                                 "S3",
		"aws_s3outposts_endpoint":                              "S3",
		"aws_s3_bucket_cors_configuration":                     "S3",
		"aws_sagemaker_app":                                    "AWS::SageMaker::App",
		"aws_sagemaker_app_image_config":                       "AWS::SageMaker::AppImageConfig",
		"aws_sagemaker_code_repository":                        "AWS::SageMaker::CodeRepository",
		"aws_sagemaker_device":                                 "AWS::SageMaker::Device",
		"aws_sagemaker_device_fleet":                           "AWS::SageMaker::DeviceFleet",
		"aws_sagemaker_domain":                                 "AWS::SageMaker::Domain",
		"aws_sagemaker_endpoint":                               "AWS::SageMaker::Endpoint",
		"aws_sagemaker_feature_group":                          "AWS::SageMaker::FeatureGroup",
		"aws_sagemaker_image":                                  "AWS::SageMaker::Image",
		"aws_sagemaker_image_version":                          "AWS::SageMaker::ImageVersion",
		"aws_sagemaker_model":                                  "AWS::SageMaker::Model",
		"aws_sagemaker_model_package_group":                    "AWS::SageMaker::ModelPackageGroup",
		"aws_sagemaker_notebook_instance":                      "AWS::SageMaker::NotebookInstance",
		"aws_sagemaker_user_profile":                           "AWS::SageMaker::UserProfile",
		"aws_sagemaker_workteam":                               "AWS::SageMaker::Workteam",
		"aws_schemas_discoverer":                               "AWS::EventSchemas::Discoverer",
		"aws_schemas_registry":                                 "AWS::EventSchemas::Registry",
		"aws_schemas_schema":                                   "AWS::EventSchemas::Schema",
		"aws_secretsmanager_secret":                            "AWS::SecretsManager::Secret",
		"aws_security_group":                                   "SecurityGroup",
		"aws_service_discovery_http_namespace":                 "AWS::ServiceDiscovery::HttpNamespace",
		"aws_service_discovery_instance":                       "AWS::ServiceDiscovery::Instance",
		"aws_service_discovery_private_dns_namespace":          "AWS::ServiceDiscovery::PrivateDnsNamespace",
		"aws_service_discovery_public_dns_namespace":           "AWS::ServiceDiscovery::PublicDnsNamespace",
		"aws_service_discovery_service":                        "AWS::ServiceDiscovery::Service",
		"aws_servicecatalog_portfolio":                         "AWS::ServiceCatalog::Portfolio",
		"aws_servicecatalog_portfolio_share":                   "AWS::ServiceCatalog::PortfolioShare",
		"aws_servicecatalog_service_action":                    "AWS::ServiceCatalog::ServiceAction",
		"aws_servicecatalog_tag_option":                        "AWS::ServiceCatalog::TagOption",
		"aws_ses_configuration_set":                            "AWS::SES::ConfigurationSet",
		"aws_ses_receipt_filter":                               "AWS::SES::ReceiptFilter",
		"aws_ses_receipt_rule":                                 "AWS::SES::ReceiptRule",
		"aws_ses_receipt_rule_set":                             "AWS::SES::ReceiptRuleSet",
		"aws_ses_template":                                     "AWS::SES::Template",
		"aws_signer_signing_profile":                           "AWS::Signer::SigningProfile",
		"aws_sns_topic":                                        "SNS",
		"aws_sns_topic_policy":                                 "AWS::SNS::TopicPolicy",
		"aws_sns_topic_subscription":                           "AWS::SNS::Subscription",
		"aws_sqs_queue":                                        "SQS",
		"aws_sqs_queue_policy":                                 "SQS",
		"aws_ssm_association":                                  "AWS::SSM::Association",
		"aws_ssm_document":                                     "AWS::SSM::Document",
		"aws_ssm_maintenance_window":                           "AWS::SSM::MaintenanceWindow",
		"aws_ssm_maintenance_window_target":                    "AWS::SSM::MaintenanceWindowTarget",
		"aws_ssm_maintenance_window_task":                      "AWS::SSM::MaintenanceWindowTask",
		"aws_ssm_parameter":                                    "AWS::SSM::Parameter",
		"aws_ssm_patch_baseline":                               "AWS::SSM::PatchBaseline",
		"aws_ssm_resource_data_sync":                           "AWS::SSM::ResourceDataSync",
		"aws_subnet":                                           "Subnet",
		"aws_synthetics_canary":                                "AWS::Synthetics::Canary",
		"aws_transfer_server":                                  "AWS::Transfer::Server",
		"aws_transfer_user":                                    "AWS::Transfer::User",
		"aws_vpc":                                              "Vpc",
		"aws_vpc_dhcp_options_association":                     "Vpc",
		"aws_vpc_endpoint":                                     "Vpc",
		"aws_vpc_endpoint_connection_notification":             "Vpc",
		"aws_vpc_endpoint_service":                             "Vpc",
		"aws_vpc_peering_connection":                           "Vpc",
		"aws_vpn_connection":                                   "AWS::EC2::VPNConnection",
		"aws_vpn_connection_route":                             "AWS::EC2::VPNConnection",
		"aws_vpn_gateway":                                      "AWS::EC2::VPNConnection",
		"aws_vpn_gateway_route_propagation":                    "AWS::EC2::VPNConnection",
		"aws_waf_byte_match_set":                               "AWS::WAF::ByteMatchSet",
		"aws_waf_ipset":                                        "AWS::WAF::IPSet",
		"aws_waf_rule":                                         "AWS::WAF::WAF",
		"aws_waf_size_constraint_set":                          "AWS::WAF::SizeConstraintSet",
		"aws_waf_sql_injection_match_set":                      "AWS::WAF::SqlInjectionMatchSet",
		"aws_waf_web_acl":                                      "AWS::WAF::WebACL",
		"aws_waf_xss_match_set":                                "AWS::WAF::XssMatchSet",
		"aws_wafregional_byte_match_set":                       "AWS::WAFRegional::ByteMatchSet",
		"aws_wafregional_geo_match_set":                        "AWS::WAFRegional::GeoMatchSet",
		"aws_wafregional_ipset":                                "AWS::WAFRegional::IPSet",
		"aws_wafregional_rate_based_rule":                      "AWS::WAFRegional::RateBasedRule",
		"aws_wafregional_regex_pattern_set":                    "AWS::WAFRegional::RegexPatternSet",
		"aws_wafregional_rule":                                 "AWS::WAFRegional::Rule",
		"aws_wafregional_size_constraint_set":                  "AWS::WAFRegional::SizeConstraintSet",
		"aws_wafregional_sql_injection_match_set":              "AWS::WAFRegional::SqlInjectionMatchSet",
		"aws_wafregional_web_acl":                              "AWS::WAFRegional::WebACL",
		"aws_wafregional_web_acl_association":                  "AWS::WAFRegional::WebACLAssociation",
		"aws_wafregional_xss_match_set":                        "AWS::WAFRegional::XssMatchSet",
		"aws_wafv2_ip_set":                                     "AWS::WAFv2::IPSet",
		"aws_wafv2_regex_pattern_set":                          "AWS::WAFv2::RegexPatternSet",
		"aws_wafv2_rule_group":                                 "AWS::WAFv2::RuleGroup",
		"aws_wafv2_web_acl":                                    "AWS::WAFv2::WebACL",
		"aws_wafv2_web_acl_association":                        "AWS::WAFv2::WebACLAssociation",
		"aws_workspaces_workspace":                             "AWS::WorkSpaces::Workspace",
		"aws_xray_group":                                       "AWS::XRay::Group",
		"aws_xray_sampling_rule":                               "AWS::XRay::SamplingRule",
	}

	terraformResourceTypesToGCP = map[string]string{
		"google_project_iam_audit_config":                      "Project",
		"google_api_gateway_api":                               "audited_resource",
		"google_api_gateway_api_config":                        "audited_resource",
		"google_api_gateway_api_config_iam_policy":             "audited_resource",
		"google_api_gateway_api_config_iam_binding":            "audited_resource",
		"google_api_gateway_api_config_iam_member":             "audited_resource",
		"google_app_engine_application":                        "gae_app",
		"google_app_engine_application_url_dispatch_rules":     "gae_app",
		"google_app_engine_domain_mapping":                     "gae_app",
		"google_app_engine_firewall_rule":                      "gae_app",
		"google_app_engine_flexible_app_version":               "gae_app",
		"google_app_engine_service_network_settings":           "gae_app",
		"google_app_engine_service_split_traffic":              "gae_app",
		"google_app_engine_standard_app_version":               "gae_app",
		"google_artifact_registry_repository":                  "ArtifactRepository",
		"google_artifact_registry_repository_iam_policy":       "ArtifactRepository",
		"google_artifact_registry_repository_iam_binding":      "ArtifactRepository",
		"google_artifact_registry_repository_iam_member":       "ArtifactRepository",
		"google_artifact_registry_vpcsc_config":                "ArtifactRepository",
		"google_dns_managed_zone":                              "DnsManagedZone",
		"google_dns_managed_zone_iam_policy":                   "DnsManagedZone",
		"google_dns_managed_zone_iam_binding":                  "DnsManagedZone",
		"google_dns_managed_zone_iam_member":                   "DnsManagedZone",
		"google_dns_policy":                                    "dns_policy",
		"google_dns_record_set":                                "dns_policy",
		"google_dns_response_policy":                           "ResponsePolicies",
		"google_dns_response_policy_rule":                      "ResponsePolicies",
		"google_cloudfunctions_function":                       "Function",
		"google_cloudfunctions_function_iam_policy":            "Function",
		"google_cloudfunctions_function_iam_binding":           "Function",
		"google_cloudfunctions_function_iam_member":            "Function",
		"google_cloudfunctions2_function":                      "Function",
		"google_cloudfunctions2_function_iam_policy":           "Function",
		"google_cloudfunctions2_function_iam_binding":          "Function",
		"google_cloudfunctions2_function_iam_member":           "Function",
		"google_iam_access_boundary_policy":                    "ServiceAccount",
		"google_iam_deny_policy":                               "ServiceAccount",
		"google_iam_workforce_pool":                            "ServiceAccount",
		"google_iam_workforce_pool_provider":                   "ServiceAccount",
		"google_iam_workload_identity_pool":                    "ServiceAccount",
		"google_iam_workload_identity_pool_provider":           "ServiceAccount",
		"google_kms_crypto_key_iam_policy":                     "KMS",
		"google_kms_crypto_key_iam_binding":                    "KMS",
		"google_kms_crypto_key_iam_member":                     "KMS",
		"google_kms_key_ring_iam_policy":                       "KMS",
		"google_kms_key_ring_iam_binding":                      "KMS",
		"google_kms_key_ring_iam_member":                       "KMS",
		"google_kms_crypto_key":                                "KMS",
		"google_kms_crypto_key_version":                        "KMS",
		"google_kms_key_ring":                                  "KMS",
		"google_kms_key_ring_import_job":                       "KMS",
		"google_kms_secret_ciphertext":                         "audited_resource",
		"google_pubsub_lite_reservation":                       "audited_resource",
		"google_pubsub_lite_subscription":                      "audited_resource",
		"google_pubsub_lite_topic":                             "audited_resource",
		"google_pubsub_schema":                                 "audited_resource",
		"google_pubsub_schema_iam_policy":                      "audited_resource",
		"google_pubsub_schema_iam_binding":                     "audited_resource",
		"google_pubsub_schema_iam_member":                      "audited_resource",
		"google_PUBSUB_SUBSCRIPTION":                           "PubsubSubscription",
		"google_PUBSUB_SUBSCRIPTION_iam_policy":                "PubsubSubscription",
		"google_PUBSUB_SUBSCRIPTION_iam_binding":               "PubsubSubscription",
		"google_PUBSUB_SUBSCRIPTION_iam_member":                "PubsubSubscription",
		"google_PUBSUB_TOPIC":                                  "PubsubTopic",
		"google_PUBSUB_TOPIC_iam_policy":                       "PubsubTopic",
		"google_PUBSUB_TOPIC_iam_member":                       "PubsubTopic",
		"google_sql_database":                                  "SQLDatabase",
		"google_sql_database_instance":                         "SQLDatabase",
		"google_sql_source_representation_instance":            "SQLDatabase",
		"google_sql_ssl_cert":                                  "SQLDatabase",
		"google_sql_user":                                      "SQLDatabase",
		"google_storage_bucket":                                "CloudStorage",
		"google_storage_bucket_access_control":                 "CloudStorage",
		"google_storage_bucket_acl":                            "CloudStorage",
		"google_storage_bucket_iam_policy":                     "CloudStorage",
		"google_storage_bucket_iam_binding":                    "CloudStorage",
		"google_storage_bucket_iam_member":                     "CloudStorage",
		"google_storage_bucket_object":                         "CloudStorage",
		"google_storage_default_object_access_control":         "CloudStorage",
		"google_storage_default_object_acl":                    "CloudStorage",
		"google_storage_hmac_key":                              "CloudStorage",
		"google_storage_notification":                          "CloudStorage",
		"google_storage_object_access_control":                 "CloudStorage",
		"google_storage_object_acl":                            "CloudStorage",
		"google_vmwareengine_cluster":                          "audited_resource",
		"google_vmwareengine_network":                          "audited_resource",
		"google_vmwareengine_private_cloud":                    "audited_resource",
		"google_secret_manager_secret":                         "audited_resource",
		"google_secret_manager_secret_iam_policy":              "audited_resource",
		"google_secret_manager_secret_iam_binding":             "audited_resource",
		"google_secret_manager_secret_iam_member":              "audited_resource",
		"google_secret_manager_secret_version":                 "SecretManagerVersion",
		"google_resource_manager_lien":                         "Project",
		"google_org_policy_custom_constraint":                  "audited_resource",
		"google_network_security_address_group":                "Network",
		"google_network_security_authorization_policy":         "Network",
		"google_network_security_client_tls_policy":            "Network",
		"google_network_security_gateway_security_policy":      "Network",
		"google_network_security_gateway_security_policy_rule": "Network",
		"google_network_security_server_tls_policy":            "Network",
		"google_network_security_tls_inspection_policy":        "Network",
		"google_network_security_url_lists":                    "Network",
		"google_compute_address":                               "ComputeAddress",
		"google_compute_attached_disk":                         "VMInstance",
		"google_compute_autoscaler":                            "VMInstance",
		"google_compute_backend_bucket":                        "gce_backend_bucket",
		"google_compute_backend_bucket_iam_policy":             "gce_backend_bucket",
		"google_compute_backend_bucket_iam_binding":            "gce_backend_bucket",
		"google_compute_backend_bucket_iam_member":             "gce_backend_bucket",
		"google_compute_backend_bucket_signed_url_key":         "gce_backend_bucket",
		"google_compute_backend_service":                       "BackendService",
		"google_compute_backend_service_iam_policy":            "BackendService",
		"google_compute_backend_service_iam_binding":           "BackendService",
		"google_compute_backend_service_iam_member":            "BackendService",
		"google_compute_backend_service_signed_url_key":        "BackendService",
		"google_compute_disk":                                  "VMDisks",
		"google_compute_disk_async_replication":                "VMDisks",
		"google_compute_disk_iam_policy":                       "VMDisks",
		"google_compute_disk_iam_binding":                      "VMDisks",
		"google_compute_disk_iam_member":                       "VMDisks",
		"google_compute_disk_resource_policy_attachment":       "VMDisks",
		"google_compute_firewall":                              "FirewallRule",
		"google_compute_firewall_policy":                       "FirewallRule",
		"google_compute_firewall_policy_association":           "FirewallRule",
		"google_compute_firewall_policy_rule":                  "FirewallRule",
		"google_compute_health_check":                          "HealthCheck",
		"google_compute_http_health_check":                     "HttpHealthCheck",
		"google_compute_https_health_check":                    "gce_health_check",
		"google_compute_instance":                              "VMInstance",
		"google_compute_instance_from_machine_image ":          "VMInstance",
		"google_compute_instance_from_template":                "VMInstance",
		"google_compute_instance_group":                        "InstanceGroup",
		"google_compute_instance_group_manager":                "InstanceGroup",
		"google_compute_instance_group_named_port":             "InstanceGroup",
		"google_compute_instance_iam":                          "VMInstance",
		"google_compute_instance_settings":                     "VMInstance",
		"google_compute_instance_template":                     "InstanceTemplate",
		"google_compute_subnetwork":                            "SubNetwork",
		"google_compute_route":                                 "gce_route",
		"google_compute_snapshot":                              "DiskSnapshot",
		"google_compute_snapshot_iam_policy":                   "DiskSnapshot",
		"google_bigquery_connection":                           "VMInstance",
		"google_compute_url_map":                               "LoadBalancer",
		"google_compute_region_instance_group_manager":         "InstanceGroupManager",
		"google_compute_target_http_proxy":                     "LoadBalancer",
		"google_bigquery_dataset":                              "BigQueryDataset",
		"google_bigquery_dataset_access":                       "BigQueryDataset",
		"google_bigquery_dataset_iam":                          "BigQueryDataset",
		"google_bigquery_table":                                "BigQueryTable",
		"google_bigquery_table_iam":                            "BigQueryTable",
		"google_compute_network_endpoint_group":                "NetworkEndpointGroup",
		"google_compute_forwarding_rule":                       "ForwardingRule",
		"google_container_node_pool":                           "NodePool",
		"google_compute_network":                               "Network",
		"google_composer_environment":                          "cloud_composer_environment",
		"google_container_cluster":                             "GKECluster",
		"google_service_account":                               "ServiceAccount",
		"google_compute_router":                                "gce_router",
	}

	terraformResourceTypesToAzure = map[string]string{
		"azurerm_api_management":                                                         "Microsoft.ApiManagement",
		"azurerm_api_management_api":                                                     "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_diagnostic":                                          "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_operation":                                           "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_operation_policy":                                    "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_operation_tag":                                       "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_policy":                                              "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_release":                                             "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_schema":                                              "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_tag":                                                 "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_tag_description":                                     "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_api_version_set":                                         "Microsoft.ApiManagement/service/apis",
		"azurerm_api_management_authorization_server":                                    "Microsoft.ApiManagement",
		"azurerm_api_management_backend":                                                 "Microsoft.ApiManagement",
		"azurerm_api_management_certificate":                                             "Microsoft.ApiManagement/service/certificates",
		"azurerm_api_management_custom_domain":                                           "Microsoft.ApiManagement",
		"azurerm_api_management_diagnostic":                                              "Microsoft.ApiManagement/service/diagnostics",
		"azurerm_api_management_email_template":                                          "Microsoft.ApiManagement",
		"azurerm_api_management_gateway":                                                 "Microsoft.ApiManagement",
		"azurerm_api_management_gateway_api":                                             "Microsoft.ApiManagement",
		"azurerm_api_management_gateway_certificate_authority":                           "Microsoft.ApiManagement",
		"azurerm_api_management_gateway_host_name_configuration":                         "Microsoft.ApiManagement",
		"azurerm_api_management_group":                                                   "Microsoft.ApiManagement",
		"azurerm_api_management_group_user":                                              "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_aad":                                   "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_aadb2c":                                "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_facebook":                              "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_google":                                "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_microsoft":                             "Microsoft.ApiManagement",
		"azurerm_api_management_identity_provider_twitter":                               "Microsoft.ApiManagement",
		"azurerm_api_management_logger":                                                  "Microsoft.ApiManagement",
		"azurerm_api_management_named_value":                                             "Microsoft.ApiManagement",
		"azurerm_api_management_notification_recipient_email":                            "Microsoft.ApiManagement",
		"azurerm_api_management_notification_recipient_user":                             "Microsoft.ApiManagement",
		"azurerm_api_management_openid_connect_provider":                                 "Microsoft.ApiManagement",
		"azurerm_api_management_policy":                                                  "Microsoft.ApiManagement",
		"azurerm_api_management_product":                                                 "Microsoft.ApiManagement",
		"azurerm_api_management_product_api":                                             "Microsoft.ApiManagement",
		"azurerm_api_management_product_group":                                           "Microsoft.ApiManagement",
		"azurerm_api_management_product_policy":                                          "Microsoft.ApiManagement",
		"azurerm_api_management_product_tag":                                             "Microsoft.ApiManagement",
		"azurerm_api_management_redis_cache":                                             "RedisCache",
		"azurerm_api_management_schema":                                                  "Microsoft.ApiManagement",
		"azurerm_api_management_subscription":                                            "Microsoft.ApiManagement",
		"azurerm_api_management_tag":                                                     "Microsoft.ApiManagement",
		"azurerm_api_management_user":                                                    "Microsoft.ApiManagement",
		"azurerm_active_directory_domain_service":                                        "Microsoft.AzureActiveDirectory",
		"azurerm_active_directory_domain_service_replica_set":                            "Microsoft.AzureActiveDirectory",
		"azurerm_active_directory_domain_service_trust":                                  "Microsoft.AzureActiveDirectory",
		"azurerm_analysis_services_server":                                               "Microsoft.AnalysisServices",
		"azurerm_app_configuration":                                                      "Microsoft.AppConfiguration",
		"azurerm_app_configuration_feature":                                              "Microsoft.AppConfiguration",
		"azurerm_app_configuration_key":                                                  "Microsoft.AppConfiguration",
		"azurerm_federated_identity_credential":                                          "Microsoft.Authorization",
		"azurerm_pim_active_role_assignment":                                             "ROLE_ASSIGNMENTS",
		"azurerm_pim_eligible_role_assignment":                                           "Microsoft.Authorization",
		"azurerm_role_assignment":                                                        "ROLE_ASSIGNMENTS",
		"azurerm_role_assignment_marketplace":                                            "ROLE_ASSIGNMENTS",
		"azurerm_role_definition":                                                        "Microsoft.Authorization",
		"azurerm_user_assigned_identity":                                                 "Microsoft.Authorization",
		"azurerm_cdn_endpoint":                                                           "Microsoft.Cdn",
		"azurerm_cdn_endpoint_custom_domain":                                             "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_custom_domain":                                            "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_custom_domain_association":                                "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_endpoint":                                                 "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_firewall_policy":                                          "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_origin":                                                   "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_origin_group":                                             "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_profile":                                                  "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_route":                                                    "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_route_disable_link_to_default_domain":                     "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_rule":                                                     "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_rule_set":                                                 "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_secret":                                                   "Microsoft.Cdn",
		"azurerm_cdn_frontdoor_security_policy":                                          "Microsoft.Cdn",
		"azurerm_cdn_profile":                                                            "Microsoft.Cdn",
		"azurerm_dedicated_host":                                                         "Microsoft.Compute",
		"azurerm_dedicated_host_group":                                                   "Microsoft.Compute",
		"azurerm_gallery_application":                                                    "Microsoft.Compute",
		"azurerm_gallery_application_version":                                            "Microsoft.Compute",
		"azurerm_image":                                                                  "Microsoft.Compute",
		"azurerm_linux_virtual_machine":                                                  "Microsoft.Compute",
		"azurerm_linux_virtual_machine_scale_set":                                        "VIRTUAL_MACHINE_SCALE",
		"azurerm_managed_disk":                                                           "Disk",
		"azurerm_marketplace_agreement":                                                  "Microsoft.Compute",
		"azurerm_orchestrated_virtual_machine_scale_set":                                 "VIRTUAL_MACHINE_SCALE",
		"azurerm_proximity_placement_group":                                              "Microsoft.Compute",
		"azurerm_shared_image":                                                           "Microsoft.Compute",
		"azurerm_shared_image_gallery":                                                   "Microsoft.Compute",
		"azurerm_shared_image_version":                                                   "Microsoft.Compute",
		"azurerm_snapshot":                                                               "AzureSnapshot",
		"azurerm_ssh_public_key":                                                         "Microsoft.Compute",
		"azurerm_container_connected_registry":                                           "Microsoft.ContainerRegistry",
		"azurerm_container_group":                                                        "Microsoft.ContainerRegistry",
		"azurerm_container_registry":                                                     "Microsoft.ContainerRegistry",
		"azurerm_container_registry_agent_pool":                                          "Microsoft.ContainerRegistry",
		"azurerm_container_registry_scope_map":                                           "Microsoft.ContainerRegistry",
		"azurerm_container_registry_task":                                                "Microsoft.ContainerRegistry",
		"azurerm_container_registry_task_schedule_run_now":                               "Microsoft.ContainerRegistry",
		"azurerm_container_registry_token":                                               "Microsoft.ContainerRegistry",
		"azurerm_container_registry_token_password":                                      "Microsoft.ContainerRegistry",
		"azurerm_container_registry_webhook":                                             "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_cluster":                                                     "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_cluster_extension":                                           "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_cluster_node_pool":                                           "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_cluster_trusted_access_role_binding":                         "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_fleet_manager":                                               "Microsoft.ContainerRegistry",
		"azurerm_kubernetes_flux_configuration":                                          "Microsoft.ContainerRegistry",
		"azurerm_cosmosdb_account":                                                       "CosmosDB",
		"azurerm_cosmosdb_cassandra_cluster":                                             "CosmosDB",
		"azurerm_cosmosdb_cassandra_datacenter":                                          "CosmosDB",
		"azurerm_cosmosdb_cassandra_keyspace":                                            "CosmosDB",
		"azurerm_cosmosdb_cassandra_table":                                               "CosmosDB",
		"azurerm_cosmosdb_gremlin_database":                                              "CosmosDB",
		"azurerm_cosmosdb_gremlin_graph":                                                 "CosmosDB",
		"azurerm_cosmosdb_mongo_collection":                                              "CosmosDB",
		"azurerm_cosmosdb_mongo_database":                                                "CosmosDB",
		"azurerm_cosmosdb_mongo_role_definition":                                         "CosmosDB",
		"azurerm_cosmosdb_mongo_user_definition":                                         "CosmosDB",
		"azurerm_cosmosdb_notebook_workspace":                                            "CosmosDB",
		"azurerm_cosmosdb_postgresql_cluster":                                            "CosmosDB",
		"azurerm_cosmosdb_postgresql_coordinator_configuration":                          "CosmosDB",
		"azurerm_cosmosdb_postgresql_firewall_rule":                                      "CosmosDB",
		"azurerm_cosmosdb_postgresql_node_configuration":                                 "CosmosDB",
		"azurerm_cosmosdb_postgresql_role":                                               "CosmosDB",
		"azurerm_cosmosdb_sql_container":                                                 "CosmosDB",
		"azurerm_cosmosdb_sql_database":                                                  "CosmosDB",
		"azurerm_cosmosdb_sql_dedicated_gateway":                                         "CosmosDB",
		"azurerm_cosmosdb_sql_function":                                                  "CosmosDB",
		"azurerm_cosmosdb_sql_role_assignment":                                           "ROLE_ASSIGNMENTS",
		"azurerm_cosmosdb_sql_role_definition":                                           "CosmosDB",
		"azurerm_cosmosdb_sql_stored_procedure":                                          "CosmosDB",
		"azurerm_cosmosdb_sql_trigger":                                                   "CosmosDB",
		"azurerm_cosmosdb_table":                                                         "CosmosDB",
		"azurerm_mariadb_configuration":                                                  "MariaDB",
		"azurerm_mariadb_database":                                                       "MariaDB",
		"azurerm_mariadb_firewall_rule":                                                  "MariaDB",
		"azurerm_mariadb_server":                                                         "MariaDB",
		"azurerm_mariadb_virtual_network_rule":                                           "MariaDB",
		"azurerm_mysql_active_directory_administrator":                                   "MySQL",
		"azurerm_mysql_configuration":                                                    "MySQL",
		"azurerm_mysql_database":                                                         "MySQL",
		"azurerm_mysql_firewall_rule":                                                    "MySQL",
		"azurerm_mysql_flexible_database":                                                "MySQL",
		"azurerm_mysql_flexible_server":                                                  "MySQL",
		"azurerm_mysql_flexible_server_aad_administrator":                                "MySQL",
		"azurerm_mysql_flexible_server_configuration":                                    "MySQL",
		"azurerm_mysql_flexible_server_firewall_rule":                                    "MySQL",
		"azurerm_mysql_server":                                                           "MySQL",
		"azurerm_mysql_server_key":                                                       "MySQL",
		"azurerm_mysql_virtual_network_rule":                                             "MySQL",
		"azurerm_postgresql_active_directory_administrator":                              "Postgres",
		"azurerm_postgresql_configuration":                                               "Postgres",
		"azurerm_postgresql_database":                                                    "Microsoft.DBforPostgreSQL/servers/databases",
		"azurerm_postgresql_firewall_rule":                                               "Postgres",
		"azurerm_postgresql_flexible_server":                                             "Postgres",
		"azurerm_postgresql_flexible_server_active_directory_administrator":              "Postgres",
		"azurerm_postgresql_flexible_server_configuration":                               "POSTGRES_DB_CONFS",
		"azurerm_postgresql_flexible_server_database":                                    "Postgres",
		"azurerm_postgresql_flexible_server_firewall_rule":                               "Postgres",
		"azurerm_postgresql_server":                                                      "Microsoft.DBforPostgreSQL/servers",
		"azurerm_postgresql_server_key":                                                  "Microsoft.DBforPostgreSQL/servers",
		"azurerm_postgresql_virtual_network_rule":                                        "Microsoft.DBforPostgreSQL/servers",
		"azurerm_sql_active_directory_administrator":                                     "SQLServer",
		"azurerm_sql_database":                                                           "SQL_DATABASES",
		"azurerm_sql_elasticpool":                                                        "SQLServer",
		"azurerm_sql_failover_group":                                                     "SQLServer",
		"azurerm_sql_firewall_rule":                                                      "SQLServer",
		"azurerm_sql_managed_database":                                                   "SQLServer",
		"azurerm_sql_managed_instance":                                                   "Microsoft.Sql/managedInstances",
		"azurerm_sql_managed_instance_active_directory_administrator":                    "Microsoft.Sql/managedInstances",
		"azurerm_sql_managed_instance_failover_group":                                    "Microsoft.Sql/managedInstances",
		"azurerm_sql_server":                                                             "SQLServer",
		"azurerm_sql_virtual_network_rule":                                               "SQLServer",
		"azurerm_virtual_desktop_application":                                            "Microsoft.DesktopVirtualization/applicationGroups/applications",
		"azurerm_virtual_desktop_application_group":                                      "Microsoft.DesktopVirtualization/applicationGroups",
		"azurerm_virtual_desktop_host_pool":                                              "Microsoft.DesktopVirtualization/hostPools",
		"azurerm_virtual_desktop_host_pool_registration_info":                            "Microsoft.DesktopVirtualization/hostPools",
		"azurerm_virtual_desktop_scaling_plan":                                           "Microsoft.DesktopVirtualization",
		"azurerm_virtual_desktop_workspace":                                              "Microsoft.DesktopVirtualization/workspaces",
		"azurerm_virtual_desktop_workspace_application_group_association":                "Microsoft.DesktopVirtualization/workspaces",
		"azurerm_elastic_cloud_elasticsearch":                                            "Microsoft.Elastic",
		"azurerm_management_group":                                                       "Group",
		"azurerm_management_group_subscription_association":                              "Group",
		"azurerm_management_lock":                                                        "Microsoft.App",
		"azurerm_resource_management_private_link":                                       "Microsoft.App",
		"azurerm_resource_management_private_link_association":                           "Microsoft.Network",
		"azurerm_application_gateway":                                                    "AZURE_APPLICATION_GATEWAY",
		"azurerm_application_security_group":                                             "NetworkSecurityGroup",
		"azurerm_bastion_host":                                                           "Microsoft.Network",
		"azurerm_custom_ip_prefix":                                                       "Microsoft.Network",
		"azurerm_express_route_circuit":                                                  "Microsoft.Network/expressRouteCircuits",
		"azurerm_express_route_circuit_authorization":                                    "Microsoft.Network/expressRouteCircuits",
		"azurerm_express_route_circuit_connection":                                       "Microsoft.Network/expressRouteCircuits",
		"azurerm_express_route_circuit_peering":                                          "Microsoft.Network/expressRouteCircuits/peerings",
		"azurerm_express_route_connection":                                               "Microsoft.Network/expressRouteGateways/expressRouteConnections",
		"azurerm_express_route_gateway":                                                  "Microsoft.Network/expressRouteGateways",
		"azurerm_express_route_port":                                                     "Microsoft.Network",
		"azurerm_express_route_port_authorization":                                       "Microsoft.Network",
		"azurerm_firewall":                                                               "Microsoft.Network/azureFirewalls",
		"azurerm_firewall_application_rule_collection":                                   "Microsoft.Network",
		"azurerm_firewall_nat_rule_collection":                                           "Microsoft.Network/localNetworkGateways",
		"azurerm_firewall_network_rule_collection":                                       "Microsoft.Network/localNetworkGateways",
		"azurerm_firewall_policy":                                                        "Microsoft.Network/firewallPolicies",
		"azurerm_firewall_policy_rule_collection_group":                                  "Microsoft.Network",
		"azurerm_frontdoor":                                                              "Microsoft.Network",
		"azurerm_frontdoor_custom_https_configuration":                                   "Microsoft.Network",
		"azurerm_frontdoor_firewall_policy":                                              "Microsoft.Network",
		"azurerm_frontdoor_rules_engine":                                                 "Microsoft.Network",
		"azurerm_ip_group":                                                               "Microsoft.Network",
		"azurerm_ip_group_cidr":                                                          "Microsoft.Network",
		"azurerm_local_network_gateway":                                                  "Microsoft.Network",
		"azurerm_nat_gateway":                                                            "AZURE_NAT_GATEWAY",
		"azurerm_nat_gateway_public_ip_association":                                      "AZURE_NAT_GATEWAY",
		"azurerm_nat_gateway_public_ip_prefix_association":                               "AZURE_NAT_GATEWAY",
		"azurerm_network_connection_monitor":                                             "Microsoft.Network/connections",
		"azurerm_network_ddos_protection_plan":                                           "Microsoft.Network",
		"azurerm_network_interface":                                                      "NetworkInterface",
		"azurerm_network_interface_application_gateway_backend_address_pool_association": "NetworkInterface",
		"azurerm_network_interface_application_security_group_association":               "NetworkInterface",
		"azurerm_network_interface_backend_address_pool_association":                     "NetworkInterface",
		"azurerm_network_interface_nat_rule_association":                                 "NetworkInterface",
		"azurerm_network_interface_security_group_association":                           "NetworkInterface",
		"azurerm_network_manager":                                                        "Microsoft.Network",
		"azurerm_network_manager_admin_rule":                                             "Microsoft.Network",
		"azurerm_network_manager_admin_rule_collection":                                  "Microsoft.Network",
		"azurerm_network_manager_connectivity_configuration":                             "Microsoft.Network",
		"azurerm_network_manager_deployment":                                             "Microsoft.Network",
		"azurerm_network_manager_management_group_connection":                            "Microsoft.Network",
		"azurerm_network_manager_network_group":                                          "Microsoft.Network",
		"azurerm_network_manager_scope_connection":                                       "Microsoft.Network",
		"azurerm_network_manager_security_admin_configuration":                           "Microsoft.Network",
		"azurerm_network_manager_static_member":                                          "Microsoft.Network",
		"azurerm_network_manager_subscription_connection":                                "Microsoft.Network",
		"azurerm_network_packet_capture":                                                 "Microsoft.Network/networkWatchers/packetCaptures",
		"azurerm_network_profile":                                                        "Microsoft.Network",
		"azurerm_network_security_group":                                                 "NetworkSecurityGroup",
		"azurerm_network_security_rule":                                                  "NetworkSecurityGroup",
		"azurerm_network_watcher":                                                        "Microsoft.Network/networkWatchers",
		"azurerm_network_watcher_flow_log":                                               "Microsoft.Network/networkWatchers",
		"azurerm_point_to_site_vpn_gateway":                                              "Microsoft.Network",
		"azurerm_private_endpoint":                                                       "NETWORK_PRV_ENDP",
		"azurerm_private_endpoint_application_security_group_association":                "NETWORK_PRV_ENDP",
		"azurerm_private_link_service":                                                   "Microsoft.Network",
		"azurerm_public_ip":                                                              "PublicIPAddress",
		"azurerm_public_ip_prefix":                                                       "PublicIPAddress",
		"azurerm_route":                                                                  "Microsoft.Network/routeTables/routes",
		"azurerm_route_filter":                                                           "NETWORK_ROUTE_FILTERS",
		"azurerm_route_map":                                                              "Microsoft.Network",
		"azurerm_route_server":                                                           "Microsoft.Network",
		"azurerm_route_server_bgp_connection":                                            "Microsoft.Network",
		"azurerm_route_table":                                                            "routeTables",
		"azurerm_subnet":                                                                 "VirtualNetworkSubnet",
		"azurerm_subnet_nat_gateway_association":                                         "VirtualNetworkSubnet",
		"azurerm_subnet_network_security_group_association":                              "VirtualNetworkSubnet",
		"azurerm_subnet_route_table_association":                                         "VirtualNetworkSubnet",
		"azurerm_subnet_service_endpoint_storage_policy":                                 "VirtualNetworkSubnet",
		"azurerm_traffic_manager_azure_endpoint":                                         "Microsoft.Network",
		"azurerm_traffic_manager_external_endpoint":                                      "Microsoft.Network",
		"azurerm_traffic_manager_nested_endpoint":                                        "Microsoft.Network",
		"azurerm_traffic_manager_profile":                                                "Microsoft.Network",
		"azurerm_virtual_hub":                                                            "Microsoft.Network/virtualHubs",
		"azurerm_virtual_hub_bgp_connection":                                             "Microsoft.Network/virtualHubs",
		"azurerm_virtual_hub_connection":                                                 "VirtualNetwork",
		"azurerm_virtual_hub_ip":                                                         "VirtualNetwork",
		"azurerm_virtual_hub_route_table":                                                "Microsoft.Network/routeTables",
		"azurerm_virtual_hub_route_table_route":                                          "Microsoft.Network/routeTables",
		"azurerm_virtual_hub_routing_intent":                                             "Microsoft.Network",
		"azurerm_virtual_hub_security_partner_provider":                                  "Microsoft.Network",
		"azurerm_virtual_machine_packet_capture":                                         "VirtualMachine",
		"azurerm_virtual_machine_scale_set_packet_capture":                               "VIRTUAL_MACHINE_SCALE",
		"azurerm_virtual_network":                                                        "VirtualNetwork",
		"azurerm_virtual_network_dns_servers":                                            "VirtualNetwork",
		"azurerm_virtual_network_gateway":                                                "VirtualNetwork",
		"azurerm_virtual_network_gateway_connection":                                     "VirtualNetwork",
		"azurerm_virtual_network_gateway_nat_rule":                                       "VirtualNetwork",
		"azurerm_virtual_network_peering":                                                "VirtualNetwork",
		"azurerm_virtual_wan":                                                            "Microsoft.Network",
		"azurerm_vpn_gateway":                                                            "Microsoft.Network",
		"azurerm_vpn_gateway_connection":                                                 "Microsoft.Network",
		"azurerm_vpn_gateway_nat_rule":                                                   "Microsoft.Network",
		"azurerm_vpn_server_configuration":                                               "Microsoft.Network",
		"azurerm_vpn_server_configuration_policy_group":                                  "Microsoft.Network",
		"azurerm_vpn_site":                                                               "Microsoft.Network",
		"azurerm_web_application_firewall_policy":                                        "Microsoft.Network",
		"azurerm_management_group_policy_assignment":                                     "Group",
		"azurerm_management_group_policy_exemption":                                      "Group",
		"azurerm_management_group_policy_remediation":                                    "Group",
		"azurerm_policy_definition":                                                      "Microsoft.PolicyInsights",
		"azurerm_policy_set_definition":                                                  "Microsoft.PolicyInsights",
		"azurerm_policy_virtual_machine_configuration_assignment":                        "Microsoft.PolicyInsights",
		"azurerm_resource_group":                                                         "ResourceGroup",
		"azurerm_resource_group_policy_assignment":                                       "ResourceGroup",
		"azurerm_resource_group_policy_exemption":                                        "ResourceGroup",
		"azurerm_resource_group_policy_remediation":                                      "ResourceGroup",
		"azurerm_resource_policy_assignment":                                             "Microsoft.PolicyInsights",
		"azurerm_resource_policy_exemption":                                              "Microsoft.PolicyInsights",
		"azurerm_resource_policy_remediation":                                            "Microsoft.PolicyInsights",
		"azurerm_subscription_policy_assignment":                                         "Microsoft.PolicyInsights",
		"azurerm_subscription_policy_exemption":                                          "Microsoft.PolicyInsights",
		"azurerm_subscription_policy_remediation":                                        "Microsoft.PolicyInsights",
		"azurerm_private_dns_a_record":                                                   "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_aaaa_record":                                                "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_cname_record":                                               "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_mx_record":                                                  "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_ptr_record":                                                 "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_srv_record":                                                 "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_txt_record":                                                 "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_zone":                                                       "NETWORK_PRV_DNS_ZONES",
		"azurerm_private_dns_zone_virtual_network_link":                                  "NETWORK_PRV_DNS_ZONES",
		"azurerm_application_load_balancer":                                              "LoadBalancer",
		"azurerm_application_load_balancer_frontend":                                     "LoadBalancer",
		"azurerm_application_load_balancer_subnet_association":                           "LoadBalancer",
		"azurerm_storage_account":                                                        "StorageAccount",
		"azurerm_storage_account_customer_managed_key":                                   "StorageAccount",
		"azurerm_storage_account_local_user":                                             "StorageAccount",
		"azurerm_storage_account_network_rules":                                          "StorageAccount",
		"azurerm_storage_blob":                                                           "StorageAccount",
		"azurerm_storage_blob_inventory_policy":                                          "StorageAccount",
		"azurerm_storage_container":                                                      "Microsoft.ContainerStorage",
		"azurerm_storage_data_lake_gen2_filesystem":                                      "Microsoft.DataLakeStore",
		"azurerm_storage_data_lake_gen2_path":                                            "Microsoft.DataLakeStore",
		"azurerm_storage_encryption_scope":                                               "Microsoft.Storage",
		"azurerm_storage_management_policy":                                              "Microsoft.Storage",
		"azurerm_storage_object_replication":                                             "StorageAccount",
		"azurerm_storage_queue":                                                          "Microsoft.Storage",
		"azurerm_storage_share":                                                          "Microsoft.Storage",
		"azurerm_storage_share_directory":                                                "Microsoft.Storage",
		"azurerm_storage_share_file":                                                     "Microsoft.Storage",
		"azurerm_storage_sync":                                                           "Microsoft.Storage",
		"azurerm_storage_sync_cloud_endpoint":                                            "Microsoft.Storage",
		"azurerm_storage_sync_group":                                                     "Microsoft.Storage",
		"azurerm_storage_table":                                                          "Microsoft.Storage",
		"azurerm_storage_table_entity":                                                   "Microsoft.Storage",
		"azurerm_disk_access":                                                            "Disk",
		"azurerm_disk_encryption_set":                                                    "Disk",
		"azurerm_disk_sas_token":                                                         "Disk",
		"azurerm_virtual_machine":                                                        "VirtualMachine",
		"azurerm_virtual_machine_data_disk_attachment":                                   "VirtualMachine",
		"azurerm_virtual_machine_extension":                                              "VirtualMachine",
		"azurerm_virtual_machine_run_command":                                            "VirtualMachine",
		"azurerm_virtual_machine_scale_set":                                              "VIRTUAL_MACHINE_SCALE",
		"azurerm_virtual_machine_scale_set_extension":                                    "VIRTUAL_MACHINE_SCALESETS_EXT",
		"azurerm_windows_virtual_machine":                                                "VirtualMachine",
		"azurerm_windows_virtual_machine_scale_set":                                      "VirtualMachine",
		"azurerm_redis_cache":                                                            "RedisCache",
		"azurerm_redis_cache_access_policy":                                              "RedisCache",
		"azurerm_redis_cache_access_policy_assignment":                                   "RedisCache",
		"azurerm_redis_firewall_rule":                                                    "RedisCache",
		"azurerm_redis_linked_server":                                                    "RedisCache",
		"azurerm_lb":                                                                     "LoadBalancer",
		"azurerm_mssql_server":                                                           "SQLServer",
		"azurerm_mssql_database":                                                         "SQLDB",
	}

	terraformResourcePropertiesToAWS = map[string]string{
		"aws_ebs_fast_snapshot_restore:availability_zone":   "zone",
		"aws_ebs_volume:availability_zone":                  "zone",
		"aws_ami:ebs_block_device":                          "blockDeviceMapping",
		"aws_ecr_lifecycle_policy:aws_ecr_lifecycle_policy": "lifecyclePolicyText",
		"aws_s3_bucket:bucket":                              "bucketName",
		"aws_iam_role:name":                                 "roleName",
		"aws_iam_policy:name":                               "policyName",
		"aws_dynamodb_table:name":                           "tableName",
		"aws_cloudformation_stack:name":                     "stackName",
	}

	terraformResourcePropertiesToGCP = map[string]string{
		"google_storage_bucket_access_control:entity": "members",
		"google_storage_bucket_acl:role_entity":       "members",
		"google_compute_instance:image":               "sourceImage",
		"google_compute_address:name":                 "displayName",
		"google_compute_address:region":               "location",
		"google_compute_backend_bucket:name":          "displayName",
		"image":                                       "sourceImage",
		"google_compute_firewall:protocol":            "IPProtocol",
		"google_storage_bucket_acl:bucket":            "name",
		"google_project_iam_audit_config:service":     "kind",
		"google_project_iam_audit_config:project":     "role",
		"google_compute_disk:size":                    "sizeGb",
		"google_storage_bucket_iam_member:member":     "members",
	}

	terraformResourcePropertiesToAzure = map[string]string{
		"azurerm_subnet:address_prefixes":                         "addressPrefix",
		"azurerm_subnet:resource_group_name":                      "id",
		"azurerm_managed_disk:disk_size_gb":                       "diskSizeGB",
		"azurerm_network_interface:private_ip_address_allocation": "privateIPAllocationMethod",
		"azurerm_virtual_machine:managed_disk_type":               "storageAccountType",
		"id": "name",
	}

	terraformResourcePropertiesPriorityConfigsAWS = map[string]struct{}{
		"user_data": {},
	}

	terraformResourcePropertiesPriorityConfigsGCP = map[string]struct{}{
		"metadata_startup_script": {},
	}

	terraformResourcePropertiesPriorityConfigsAzure = map[string]struct{}{
		"custom_data": {},
	}

	// terraformResourceTypesToAzure = map[string]string{}

	terraformResourceProviderToCSP = map[string]string{
		"aws":     "aws",
		"google":  "gcp",
		"azurerm": "azure",
	}

	cspToMapForResourceType = map[string]map[string]string{
		"aws":   terraformResourceTypesToAWS,
		"gcp":   terraformResourceTypesToGCP,
		"azure": terraformResourceTypesToAzure,
	}

	cspToMapForResourceProperties = map[string]map[string]string{
		"aws":   terraformResourcePropertiesToAWS,
		"gcp":   terraformResourcePropertiesToGCP,
		"azure": terraformResourcePropertiesToAzure,
	}

	cspToMapForPriorityConfigs = map[string]map[string]struct{}{
		"aws":   terraformResourcePropertiesPriorityConfigsAWS,
		"azure": terraformResourcePropertiesPriorityConfigsAzure,
		"gcp":   terraformResourcePropertiesPriorityConfigsGCP,
	}

	terraformResourceLinking = map[string][]string{
		"id":  {"name", "bucket"},
		"arn": {"name", "bucket"},
	}
)

func GetResourceMetadataFromTerraform(attributes InstanceAttributes, tfResourceType, provider string) (resourceEvent ResourceEvent) {

	csp := getCSPFromTerraformProvider(provider)

	resourceEvent.ResourceID = attributes.ID
	resourceEvent.ResourceName = attributes.Name
	resourceEvent.ResourceType = tfResourceType

	switch csp {

	case "aws":

		resourceEvent.Account, resourceEvent.Region = GetAccountAndRegionFromArn(attributes.Arn)

		if awsResourceType := terraformResourceTypesToAWS[tfResourceType]; len(awsResourceType) > 0 {
			resourceEvent.ResourceType = awsResourceType
		}

	case "azure":

		resourceEvent.Account = GetSubscriptionFromResourceID(attributes.ID)
		resourceEvent.Region = attributes.Location
		resourceEvent.ResourceGroup = attributes.ResourceGroup

		idSplit := strings.Split(attributes.ID, "/")
		resourceEvent.ResourceType = idSplit[len(idSplit)-1]
	}

	return
}

func getCSPFromTerraformProvider(provider string) (csp string) {

	if strings.Contains(provider, "hashicorp/aws") {
		csp = "aws"
	} else if strings.Contains(provider, "hashicorp/azurerm") {
		csp = "azure"
	} else {
		logger.Print(logger.INFO, "Unsupported provider", provider)
	}

	return
}
