package common

import (
	"encoding/json"
	"maps"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/google/go-github/v48/github"
	"github.com/hashicorp/hcl/v2"
	"github.com/hashicorp/hcl/v2/hclsyntax"
	jsoniter "github.com/json-iterator/go"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/xanzy/go-gitlab"
)

func ProcessTerragruntFile(fileContent, tenantID, gitClientName string, gitClient any) error {

	hclSyntaxFile, diagnostics := hclsyntax.ParseConfig([]byte(fileContent), "", hcl.InitialPos)
	if diagnostics != nil && diagnostics.HasErrors() {
		hclErrors := diagnostics.Errs()
		logger.Print(logger.INFO, "Failed to parse terragrunt hcl syntax file ", []string{tenantID}, fileContent, hclErrors)
		return nil
	}

	if hclSyntaxFile.Body == nil {
		logger.Print(logger.INFO, "Error parsing terragrunt file", []string{tenantID}, fileContent)
	}

	syntaxBody := hclSyntaxFile.Body.(*hclsyntax.Body)
	if len(syntaxBody.Blocks) == 0 {
		logger.Print(logger.INFO, "Got error while parsing block. Got zero blocks")
		return nil
	}

	var (
		blockType           string
		blockLabel          string
		syntaxBlocks        = syntaxBody.Blocks
		result              = make(map[string]any)
		tfVariablesMapping  = make(map[string]any)
		moduleVariablesMap  = make(map[string]any)
		extFileVariablesMap = make(map[string]any)
		commitID            = "unknown"
		bulkUpdateQuery     = ""
		repoName            = ""
	)

	for i := 0; i < len(syntaxBlocks); i++ {

		if len(syntaxBlocks[i].Type) > 0 {
			blockType = syntaxBlocks[i].Type
		} else {
			blockType = ""
		}

		if len(syntaxBlocks[i].Labels) > 0 {
			blockLabel = syntaxBlocks[i].Labels[0]
		} else {
			blockLabel = ""
		}

		err := mapBlocks(syntaxBlocks[i].Body, result, nil, false, gitClientName, nil, nil, blockType, "", blockLabel, tfVariablesMapping, nil, nil, tenantID)
		if err != nil {
			logger.Print(logger.ERROR, "Error parsing tf block arguments")
			continue
		}

		if blockType == "terraform" {
			for key, value := range result {
				if strings.Contains(key, "source") {
					if value, ok := value.([]any); ok {
						for _, source := range value {
							if source, ok := source.(string); ok {
								repoName = ExtractRepoNameFromGitUrl(source, gitClientName, gitClient)
								if len(repoName) <= 0 {
									return nil
								}
							}
						}
					}
				}
			}
		}
	}

	recentCodeEventsQuery := `{
		"query": {
				"bool": {
					"must": [
						{"match": {"tenantId.keyword": "` + tenantID + `"}},
						{"match": {"repoName.keyword": "` + repoName + `"}}
					]
				}
			}
		}`

	tfVarDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TF_VARIABLES_INDEX}, recentCodeEventsQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching variable record for a module", []string{commitID, tenantID}, err)
		return err
	}

	if len(tfVarDocs) > 0 {
		for _, tfVarDoc := range tfVarDocs {
			if variableName, ok := tfVarDoc["varName"].(string); ok {
				if _, ok := tfVariablesMapping[variableName]; !ok {
					if variablesJSONStr, ok := tfVarDoc["variables"].(string); ok {
						variableMap := make(map[string]any)
						err := json.Unmarshal([]byte(variablesJSONStr), &variableMap)
						if err != nil {
							logger.Print(logger.ERROR, "Got error while unmarshaling variables json")
							continue
						}
						moduleVariablesMap[variableName] = variableMap
					}
				}
			}
		}
	}

	// check if any external terragrunt file is referenced
	for key, val := range tfVariablesMapping {
		if key == "include" || key == "dependency" || key == "dependencies" {
			if val, ok := val.(map[string]any); ok {
				for k, v := range val {
					if k == "path" || k == "paths" || k == "config_path" {
						if paths, ok := v.([]any); ok {
							for _, path := range paths {
								if path, ok := path.(string); ok {
									// TODO if path does not specify a file redirect to terragrunt.hcl automatically
									fileContent, err := fetchExtTerragruntFile(gitClientName, gitClient, path)
									if err != nil {
										return err
									}
									processExternalTerragruntFiles(fileContent, tenantID, "github", extFileVariablesMap)
									tfVariablesMapping[key] = extFileVariablesMap
								}
							}
						}
					}
				}
			}
		}
	}

	result = make(map[string]any)

	for i := 0; i < len(syntaxBody.Attributes); i++ {
		err := mapAttributes(syntaxBody.Attributes, result, tfVariablesMapping, false, gitClientName, nil, nil, "", "", "", tfVariablesMapping, nil, nil, tenantID)
		if err != nil {
			return err
		}
	}

	for key, value := range result {
		if strings.Contains(key, "inputs") {
			if valArr, ok := value.([]any); ok {
				for _, valAr := range valArr {
					if valMap, ok := valAr.(map[string]any); ok {
						for variableKey, value := range valMap {
							if value != "" {
								if val, ok := moduleVariablesMap[variableKey]; !ok {
									valMap := make(map[any]any)
									if _, isValArray := value.([]any); isValArray {
										valMap[commitID] = value
									} else {
										valMapValArr := make([]any, 0)
										valMapValArr = append(valMapValArr, value)
										valMap[commitID] = valMapValArr
									}
									moduleVariablesMap[variableKey] = valMap
								} else {
									if valMap, isMap := val.(map[string]any); isMap {
										if valMapVal, ok := valMap[commitID]; ok {
											if valMapArr, isValMapArr := valMapVal.([]any); isValMapArr {
												if valArr, isArr := value.([]any); isArr {
													for _, v := range valArr {
														if !contains(valMapArr, v) {
															valMapArr = append(valMapArr, v)
														}
													}
													valMap[commitID] = valMapArr
												}
											}
										} else {
											valMapArr := []any{value}
											valMap[commitID] = valMapArr
										}
										moduleVariablesMap[variableKey] = valMap
									}
								}
							}
						}
					}
				}
			}
		}
	}

	if len(repoName) > 0 {

		for key, value := range moduleVariablesMap {
			variablesJsonData, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(value)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling variables", err)
				return err
			}
			tfVarDoc := TFVarDoc{
				TenantID:     tenantID,
				Variables:    string(variablesJsonData),
				VariableName: key,
				RepoName:     repoName,
				GitClient:    gitClientName,
			}
			tfVarDocID := GenerateCombinedHashID(key, repoName, tenantID)
			updateMetadata := map[string]any{
				"index": map[string]string{
					"_id": tfVarDocID,
				},
			}

			bulkRequestBody := map[string]any{
				"doc": tfVarDoc,
			}
			updateMetadataJSON, err := json.Marshal(updateMetadata)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling update metadata", err)
				return err
			}

			bulkRequestBodyJSON, err := json.Marshal(bulkRequestBody)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling bulk request body", err)
				return err
			}

			bulkUpdateQuery += string(updateMetadataJSON) + "\n" + string(bulkRequestBodyJSON) + "\n"
		}

		if len(bulkUpdateQuery) > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.TF_VARIABLES_INDEX, bulkUpdateQuery); err != nil {
				return err
			}
			logger.Print(logger.INFO, "TF Variables bulk Update API Successful", []string{tenantID})
		}
	}
	return nil
}

func fetchExtTerragruntFile(gitClientName string, gitClient any, destPath string) (string, error) {
	switch gitClientName {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			dirPath := filepath.Dir(githubApiClient.FilePath)
			resultPath := filepath.Join(dirPath, destPath)
			path := filepath.Clean(resultPath)

			repoContent, _, _, err := githubApiClient.GithubClient.Repositories.GetContents(githubApiClient.Context, githubApiClient.RepoOwner, githubApiClient.RepoName, path, &github.RepositoryContentGetOptions{
				Ref: githubApiClient.CommitSHA,
			})
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching file for github", []string{githubApiClient.TenantID, githubApiClient.RepoName, path}, err)
				return "", err
			}

			if repoContent != nil {
				contentString, err := repoContent.GetContent()
				if err != nil {
					logger.Print(logger.ERROR, "Got error fetching directory tree for github", []string{githubApiClient.TenantID, githubApiClient.RepoName}, err)
					return "", err
				}
				return contentString, nil
			}
		}
	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			dirPath := filepath.Dir(gitlabApiClient.FilePath)
			resultPath := filepath.Join(dirPath, destPath)
			path := filepath.Clean(resultPath)

			contentString, _, err := gitlabApiClient.GitlabClient.RepositoryFiles.GetRawFile(gitlabApiClient.ProjectID, path, &gitlab.GetRawFileOptions{})
			if err != nil {
				if strings.Contains(err.Error(), "401") {
					gitlabApiClient.GitlabClient = refreshGitlabToken(gitlabApiClient.AccessToken)
					contentString, _, err = gitlabApiClient.GitlabClient.RepositoryFiles.GetRawFile(gitlabApiClient.ProjectID, path, &gitlab.GetRawFileOptions{})
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting commit raw content", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID)}, err)

					}
				} else {
					logger.Print(logger.ERROR, "Got error fetching raw file for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), path}, err)
					return "", err
				}
			}

			return string(contentString), nil
		}
	case BITBUCKET:
		// TODO
	default:
		logger.Print(logger.INFO, "Git Provider not supported", gitClientName)
	}
	return "", nil
}

func processExternalTerragruntFiles(fileContent, tenantID, gitClientName string, extFileVariablesMap map[string]any) (err error) {

	hclSyntaxFile, diagnostics := hclsyntax.ParseConfig([]byte(fileContent), "", hcl.InitialPos)
	if diagnostics != nil && diagnostics.HasErrors() {
		hclErrors := diagnostics.Errs()
		logger.Print(logger.INFO, "Failed to parse terragrunt hcl syntax file ", []string{tenantID}, fileContent, hclErrors)
		return nil
	}

	if hclSyntaxFile.Body == nil {
		logger.Print(logger.INFO, "Error parsing terragrunt file", []string{tenantID}, fileContent)
	}

	syntaxBody := hclSyntaxFile.Body.(*hclsyntax.Body)
	if len(syntaxBody.Blocks) == 0 {
		logger.Print(logger.INFO, "Got error while parsing block. Got zero blocks")
		return nil
	}

	var (
		blockType          string
		blockLabel         string
		syntaxBlocks       = syntaxBody.Blocks
		result             = make(map[string]any)
		tfVariablesMapping = make(map[string]any)
	)

	for i := 0; i < len(syntaxBlocks); i++ {

		if len(syntaxBlocks[i].Type) > 0 {
			blockType = syntaxBlocks[i].Type
		}

		if len(syntaxBlocks[i].Labels) > 0 {
			blockLabel = syntaxBlocks[i].Labels[0]
		}

		err := mapBlocks(syntaxBlocks[i].Body, result, nil, false, gitClientName, nil, nil, blockType, "", blockLabel, tfVariablesMapping, nil, nil, tenantID)
		if err != nil {
			logger.Print(logger.ERROR, "Error parsing tf block arguments")
			continue
		}
	}

	result = make(map[string]any)

	for i := 0; i < len(syntaxBody.Attributes); i++ {
		err := mapAttributes(syntaxBody.Attributes, result, tfVariablesMapping, false, gitClientName, nil, nil, "", "", "", tfVariablesMapping, nil, nil, tenantID)
		if err != nil {
			return err
		}
	}

	if len(tfVariablesMapping) > 0 {
		maps.Copy(extFileVariablesMap, tfVariablesMapping)
	}

	return
}
