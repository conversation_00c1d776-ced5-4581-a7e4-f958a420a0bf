package common

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/google/go-github/v48/github"
	"github.com/precize/logger"
	"github.com/precize/transport"
	"github.com/xanzy/go-gitlab"
)

func fetchGitFilesWithType(provider string, gitClient any, variableFiles *[]VariableFile, fileName string, filePath string) error {
	switch provider {
	case GITHUB:
		if githubApiClient, ok := gitClient.(*GithubApiClient); ok {
			err := fetchSpecificFileGithub(filePath, *githubApiClient, fileName, variableFiles)
			if err != nil {
				return err
			}
		}
	case BITBUCKET:
		if bitbucketApiClient, ok := gitClient.(*BitbucketApiClient); ok {
			err := fetchSpecificFileBitbucket(*bitbucketApiClient, variableFiles, "commit_directory", filePath, fileName)
			if err != nil {
				return err
			}
		}

	case GITLAB:
		if gitlabApiClient, ok := gitClient.(*GitlabApiClient); ok {
			err := fetchSpecificFileGitlab(*gitlabApiClient, fileName, variableFiles, filePath)
			if err != nil {
				return err
			}
		}
	default:
		logger.Print(logger.ERROR, "Provider not supported", provider)
	}
	return nil
}

func fetchSpecificFileGithub(path string, githubApiClient GithubApiClient, fileName string, fileContents *[]VariableFile) error {
	repoContent, directories, _, err := githubApiClient.GithubClient.Repositories.GetContents(githubApiClient.Context, githubApiClient.RepoOwner, githubApiClient.RepoName, path, &github.RepositoryContentGetOptions{
		Ref: githubApiClient.CommitSHA,
	})
	if err != nil {
		if strings.Contains(err.Error(), "404 Not Found") {
			return nil
		}
		logger.Print(logger.ERROR, "Got error fetching directories from path for github", []string{githubApiClient.TenantID, githubApiClient.RepoName, path, githubApiClient.CommitSHA}, err)
		return err
	}

	if repoContent != nil {
		contentString, err := repoContent.GetContent()
		if err != nil {
			logger.Print(logger.ERROR, "Got error fetching directory tree for github", []string{githubApiClient.TenantID, githubApiClient.RepoName}, err)
			return err
		}
		if contentString != "" && (strings.HasSuffix(*repoContent.Path, fileName)) {
			*fileContents = append(*fileContents, VariableFile{
				fileName: *repoContent.Name,
				content:  contentString,
				filePath: *repoContent.Path,
			})

		}
	}

	for _, directory := range directories {
		fetchSpecificFileGithub((*directory.Path), githubApiClient, fileName, fileContents)
	}
	return nil
}

func fetchSpecificFileGitlab(gitlabApiClient GitlabApiClient, fileName string, fileContents *[]VariableFile, filePath string) error {
	recursive := true
	directoryContents, _, err := gitlabApiClient.GitlabClient.Repositories.ListTree(gitlabApiClient.ProjectID, &gitlab.ListTreeOptions{
		Path:      &filePath,
		Ref:       &gitlabApiClient.Branch,
		Recursive: &recursive,
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching directory tree for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), filePath}, err)
		return err
	}

	for _, fileContent := range directoryContents {
		if fileContent.Type == "blob" && (strings.HasSuffix(fileContent.Path, fileName)) {
			contentString, _, err := gitlabApiClient.GitlabClient.RepositoryFiles.GetRawFile(gitlabApiClient.ProjectID, fileContent.Path, &gitlab.GetRawFileOptions{Ref: gitlab.String(gitlabApiClient.CommitID)})
			if err != nil {
				logger.Print(logger.ERROR, "Got error fetching raw file for gitlab", []string{gitlabApiClient.TenantID, strconv.Itoa(gitlabApiClient.ProjectID), fileContent.Path}, err)
				continue
			}
			*fileContents = append(*fileContents, VariableFile{
				fileName: fileContent.Name,
				content:  string(contentString),
				filePath: fileContent.Path,
			})
		}
	}
	return nil
}

func fetchSpecificFileBitbucket(bitbucketApiClient BitbucketApiClient, variablesFileContents *[]VariableFile, fileType string, filePath string, fileName string) error {
	fileContentUrl := bitbucketApiClient.Domain + "/2.0/repositories/" + bitbucketApiClient.WorkspaceSlug + "/" +
		bitbucketApiClient.RepoSlug + "/src/" + bitbucketApiClient.CommitHash + "/" + filePath

	fileContentResp, err := transport.SendRequest(
		"GET",
		fileContentUrl,
		nil,
		map[string]string{"Authorization": "Bearer " + bitbucketApiClient.Token},
		nil,
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling while fetching directory contents for bitbucket", []string{bitbucketApiClient.WorkspaceSlug, bitbucketApiClient.RepoSlug, bitbucketApiClient.CommitHash, filePath}, err)
		return err
	}

	if fileType == "commit_file" {
		if strings.HasSuffix(filePath, fileName) {
			*variablesFileContents = append(*variablesFileContents, VariableFile{
				fileName: filePath,
				content:  string(fileContentResp),
				filePath: filePath,
			})
		}
	} else if fileType == "commit_directory" {
		var dirContents BitBucketDirectoryContents
		if err = json.Unmarshal(fileContentResp, &dirContents); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling while fetching directory contents for bitbucket", err)
			return err
		}
		for _, dirContent := range dirContents.Values {
			fetchVariableFilesBitbucket(bitbucketApiClient, variablesFileContents, dirContent.Type, dirContent.Path)
		}
	}
	return nil
}
