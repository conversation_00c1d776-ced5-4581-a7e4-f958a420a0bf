package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/precize/logger"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

func parseTfFunctions(name string, parameters any) (any, error) {
	switch name {
	case "abs":
		return parseAbs(parameters)
	case "ceil":
		return parseCeil(parameters)
	case "floor":
		return parseFloor(parameters)
	case "max":
		return parseMax(parameters)
	case "min":
		return parseMin(parameters)
	case "pow":
		return parsePow(parameters)
	case "lower":
		return parseLower(parameters)
	case "upper":
		return parseUpper(parameters)
	case "join":
		arr, ok := parameters.([]any)
		if !ok {
			logger.Print(logger.ERROR, "Unable to parse join function", parameters)
			return nil, errors.New("error parsing join function: expected array of parameters")
		}

		if len(arr) < 2 {
			logger.Print(logger.ERROR, "Join function requires at least two parameters: separator and at least one element", parameters)
			return nil, errors.New("join function requires at least two parameters")
		}

		separator, isSeparatorString := arr[0].(string)
		if !isSeparatorString {
			logger.Print(logger.ERROR, "Join function requires first parameter to be a string separator", parameters)
			return nil, errors.New("join function requires string separator as first parameter")
		}

		return parseJoinElements(separator, arr[1:])
	case "title":
		return parseTitle(parameters)
	case "sum":
		return parseSum(parameters)
	case "length":
		return parseLength(parameters)
	case "jsondecode":
		return parseJsonDecode(parameters)
	case "jsonencode":
		return parseJsonEncode(parameters)
	default:
		logger.Print(logger.INFO, "Unsupported function name", name)
		return nil, nil
	}
}

func parseAbs(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse abs function", parameters)
		return nil, errors.New("Unable to parse abs function")
	}

	if len(arr) < 2 {
		logger.Print(logger.ERROR, "min function requires at least two parameters", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "abs function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}

	if value, isFloat := arr[0].(float64); isFloat {
		return math.Abs(value), nil
	} else if value, isInt := arr[0].(int); isInt {
		return math.Abs(float64(value)), nil
	}

	logger.Print(logger.ERROR, "abs function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseCeil(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse ceil function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "ceil function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}

	if value, ok := arr[0].(float64); ok {
		result := math.Ceil(value)
		return result, nil
	}

	logger.Print(logger.ERROR, "ceil function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseFloor(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse floor function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 1 {
		logger.Print(logger.ERROR, "Floor function requires exactly one parameter", parameters)
		return nil, errors.New("error")
	}
	if value, ok := arr[0].(float64); ok {
		result := math.Floor(value)
		return result, nil
	}
	logger.Print(logger.ERROR, "Floor function requires a numeric parameter", parameters)
	return nil, errors.New("error")
}

func parseMin(parameter any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameter.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse min function", parameter)
		return nil, errors.New("error")
	}

	if len(arr) < 2 {
		logger.Print(logger.ERROR, "Min function requires at least two parameters", parameter)
		return nil, errors.New("error")
	}

	var convertedParams []float64
	for _, param := range arr {
		if value, isFloat := param.(float64); isFloat {
			convertedParams = append(convertedParams, value)
		} else if value, isInt := param.(int64); isInt {
			convertedParams = append(convertedParams, float64(value))
		} else {
			logger.Print(logger.ERROR, "Min function requires numeric parameters", parameter)
			return nil, errors.New("error")
		}
	}

	result := math.Min(convertedParams[0], convertedParams[1])
	for _, param := range convertedParams[2:] {
		result = math.Min(result, param)
	}

	return result, nil
}

func parseMax(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse max function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) < 2 {
		logger.Print(logger.ERROR, "Max function requires at least two parameters", parameters)
		return nil, errors.New("error")
	}

	var convertedParams []float64
	for _, param := range arr {
		if value, isFloat := param.(float64); isFloat {
			convertedParams = append(convertedParams, value)
		} else if value, isInt := param.(int64); isInt {
			convertedParams = append(convertedParams, float64(value))
		} else {
			logger.Print(logger.ERROR, "Max function requires numeric parameters", parameters)
			return nil, errors.New("error")
		}
	}

	result := math.Max(convertedParams[0], convertedParams[1])
	for _, param := range convertedParams[2:] {
		result = math.Max(result, param)
	}

	return result, nil
}

func parsePow(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse pow function", parameters)
		return nil, errors.New("error")
	}
	if len(arr) != 2 {
		logger.Print(logger.ERROR, "Pow function requires exactly two parameters", parameters)
		return nil, errors.New("error")
	}

	base, baseIsFloat := arr[0].(float64)
	exponent, exponentIsFloat := arr[1].(float64)

	if !baseIsFloat {
		base = float64(arr[0].(int))
	}

	if !exponentIsFloat {
		exponent = float64(arr[1].(int))
	}

	result := math.Pow(base, exponent)
	return result, nil
}

func parseJoinElements(separator string, elements any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = elements.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse join function", separator, elements)
		return nil, errors.New("error")
	}

	var stringElements []string

	for _, element := range arr {
		stringValue := fmt.Sprintf("%v", element)
		stringElements = append(stringElements, stringValue)
	}

	return strings.Join(stringElements, separator), nil
}

func parseLower(input any) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	return strings.ToLower(stringValue), nil
}

func parseUpper(input any) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	return strings.ToUpper(stringValue), nil
}

func parseTitle(input any) (string, error) {
	stringValue := fmt.Sprintf("%v", input)
	caser := cases.Title(language.English)
	titlecased := caser.String(stringValue)

	return titlecased, nil
}

func parseSum(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse sum function", parameters)
		return nil, errors.New("error")
	}

	var sumResult float64
	for _, param := range arr {

		if value, isInt := param.(int64); isInt {
			sumResult += float64(value)
		} else if value, isInt := param.(float64); isInt {
			sumResult += value
		}
	}

	return sumResult, nil
}

func parseLength(input any) (int, error) {
	switch v := input.(type) {
	case string:
		return len(v), nil
	case []any:
		return len(v), nil
	default:
		logger.Print(logger.ERROR, "Unsupported type for length function", input)
		return 0, errors.New("error")
	}
}

func parseJsonDecode(parameters any) (any, error) {
	var arr []any
	var ok bool
	if arr, ok = parameters.([]any); !ok {
		logger.Print(logger.ERROR, "Unable to parse pow function", parameters)
		return nil, errors.New("error")
	}
	stringValue := fmt.Sprintf("%v", arr[0])

	var singleValue any

	err := json.Unmarshal([]byte(stringValue), &singleValue)
	if err == nil {
		return singleValue, nil
	}

	var mapValue map[string]string
	err = json.Unmarshal([]byte(stringValue), &mapValue)
	if err == nil {
		return mapValue, nil
	}
	logger.Print(logger.ERROR, "Unable to parse jsondecode function", parameters)
	return nil, errors.New("error")
}

func parseJsonEncode(parameter any) (any, error) {

	jsonBytes, err := json.Marshal(parameter)
	if err != nil {
		logger.Print(logger.ERROR, "Got Error while unmarshalling parseJsonEncode func", err)
		return "", err
	}

	jsonString := string(jsonBytes)
	return jsonString, nil

}
