package common

import "strings"

func normalizeTFResource(tfResourceType, resourceID string, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string, deployInfo DeplopymentInfo) (string, string) {

	precizeRscType := ""
	switch tfResourceType {
	case "google_organization_policy":
		rscIDSplit := strings.Split(resourceID, "/")
		if len(rscIDSplit) > 1 {
			orgID := rscIDSplit[0]
			rscID := rscIDSplit[1]

			resourceID = "organizations/" + orgID + "/policies/" + rscID
			precizeRscType = "OrganizationPolicy"
		}
	case "google_project_organization_policy":
		rscIDSplit := strings.Split(resourceID, ":")
		if len(rscIDSplit) > 1 {
			projectName := rscIDSplit[0]
			projectID, ok := parentRscNameToID[projectName]
			if !ok {
				return "", ""
			}
			rscID := rscIDSplit[1]

			resourceID = "projects/" + projectID + "/policies/" + rscID
			precizeRscType = "OrganizationPolicy"
		}

	case "google_folder_organization_policy":
		rscIDSplit := strings.Split(resourceID, "/")
		if len(rscIDSplit) > 1 {
			folderID := rscIDSplit[0]
			rscID := rscIDSplit[1]

			resourceID = "folders/" + folderID + "/policies/" + rscID
			precizeRscType = "OrganizationPolicy"
		}
	default:
		missingNormalizeRsc[tfResourceType+":"+resourceID] = deployInfo.JobID
	}

	return resourceID, precizeRscType
}
