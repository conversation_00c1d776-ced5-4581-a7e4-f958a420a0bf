package common

import (
	"encoding/json"
	"net/url"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

func GenerateWizToken(wizEnv tenant.WizEnvironment, tenantID string) (token string, err error) {
	var authResp []byte

	payload := url.Values{}
	payload.Add("grant_type", "client_credentials")
	payload.Add("audience", "wiz-api")
	payload.Add("client_id", wizEnv.ClientID)
	payload.Add("client_secret", wizEnv.ClientSecret)

	headers := map[string]string{
		"Accept":       "application/json",
		"Content-Type": "application/x-www-form-urlencoded",
	}

	if authResp, err = transport.SendRequest("POST", "https://auth.app.wiz.io/oauth/token", nil, headers, strings.NewReader(payload.Encode())); err != nil {
		logger.Print(logger.ERROR, "Error in Wiz authentication API", []string{tenantID}, err)
		return
	}

	var authRes struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
	}

	if err = json.Unmarshal(authResp, &authRes); err != nil {
		logger.Print(logger.ERROR, "Error unmarshaling Wiz authentication response", []string{tenantID}, err)
		return
	}

	token = authRes.AccessToken
	return
}
