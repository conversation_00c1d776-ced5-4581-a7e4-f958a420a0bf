package elastic

func initializeAiIndices() error {

	err := CreateIndex(TEXT_LOOKUP_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "text":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "hasName":
                    {
                        "type": "boolean"
                    },
                    "email":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "name":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "insertTime":
    	            {
    	                "type": "date",
    	                "format": "date_time"
    	            }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(AI_RESOURCES_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "id":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "entityId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "entityType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "accountId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "source":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "stageCompleted":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deleted":
                    {
                        "type": "boolean"
                    },
                    "collectedAt":
                    {
                        "type": "long"
                    },
                    "createdDate":
                    {
                        "type": "long"
                    },
                    "serviceId":
                    {
                        "type": "integer"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "entityJson":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "insertTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	return nil
}
