package elastic

func initializeEnhancerIndices() error {

	err := CreateIndex(RESOURCE_CONTEXT_INDEX,
		`{
		    "settings":
		    {
		        "analysis":
		        {
		            "analyzer":
		            {
		                "default":
		                {
		                    "type": "keyword"
		                }
		            }
		        },
		        "number_of_shards": 1,
		        "number_of_replicas": 0
		    },
		    "mappings":
		    {
		        "properties":
		        {
		            "resourceId":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "resourceName":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "resourceType":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "account":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "tenantId":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "region":
		            {
		                "type": "text",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
	                "serviceId":
	                {
	                    "type": "long"
	                },
	                "lastCollectedAt":
	                {
	                    "type": "text",
	                    "fields":
	                    {
	                        "keyword":
	                        {
	                            "type": "keyword",
	                            "ignore_above": 256
	                        }
	                    }
	                },
	                "updatedTime":
	                {
	                    "type": "date",
	                    "format": "date_time"
	                },
		            "definedOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "derivedOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "inheritedOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "codeOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "costOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "securityOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
		            "opsOwners":
		            {
		                "type":"nested",
		                "properties":
		                {
		                    "name":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "type":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
	                        "desc":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
		                }
		            },
	                "definedEnv":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedEnv":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "inheritedEnv":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedApp":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedApp":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedDeployment":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedDeployment":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedCompliance":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedCompliance":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedSensitivity":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedSensitivity":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedCostCenter":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedCostCenter":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "inheritedCostCenter":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "definedTeam":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "derivedTeam":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                },
	                "inheritedTeam":
	                {
	                    "type":"nested",
	                    "properties":
	                    {
	                        "name":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        },
	                        "type":
	                        {
	                            "type": "text",
	                            "fields":
	                            {
	                                "keyword":
	                                {
	                                    "type": "keyword",
	                                    "ignore_above": 256
	                                }
	                            }
	                        }
	                    }
	                }
		        }
		    }
		}`,
	)
	if err != nil {
		return err
	}

	return nil
}
