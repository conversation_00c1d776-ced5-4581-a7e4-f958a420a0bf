package elastic

func initializeExternalRscIndices() error {

	err := CreateIndex(EXTERNAL_CLOUD_RESOURCES_INDEX,
		`{
			"settings": {
				"analysis": {
					"analyzer": {
						"default": {
							"type": "keyword"
						}
					}
				},
				"number_of_shards": 1,
				"number_of_replicas": 0
			},
			"mappings": {
				"properties": {
					"resourceGroup": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"id": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"entityType": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"accountId": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"region": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"serviceId": {
						"type": "long"
					},
					"entityId": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"createdDate": {
						"type": "date",
						"format": "date_time"
					},
					"deleted": {
						"type": "boolean"
					},
					"tenantId": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"entityJson": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					},
					"updatedDate": {
						"type": "date",
						"format": "date_time"
					},
					"insertTime": {
						"type": "date",
						"format": "date_time"
					},
					"tags": {
						"type": "nested",
						"properties":
						{
							"_class":
							{
								"index": false,
								"type": "keyword",
								"doc_values": false
							},
							"value":
							{
								"type": "text",
								"fields":
								{
									"keyword":
									{
										"ignore_above": 256,
										"type": "keyword"
									}
								}
							},
							"key":
							{
								"type": "text",
								"fields":
								{
									"keyword":
									{
										"ignore_above": 256,
										"type": "keyword"
									}
								}
							}
						}
					},
					"source": {
						"type": "text",
						"fields": {
							"keyword": {
								"type": "keyword",
								"ignore_above": 256
							}
						}
					}
				}
			}
		}`,
	)
	if err != nil {
		return err
	}

	return nil
}
