package elastic

func initializeIacIndices() error {

	err := CreateIndex(IAC_GIT_COMMITS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "commitId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "commitSummary":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "author":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "authorEmail":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "filename":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "gitFileHash":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "minifiedJSONHash":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "commitTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "fileStatus":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "repoName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "branch":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "iacType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "gitClient":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "fileContent":
                    {
                        "type": "text",
                        "index": false
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(CFSTACK_TEMPLATES_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "stackId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "stackName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "templateGithash":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "eventTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "stackCreationTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "region":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "account":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceEvents":
                    {
                        "properties":
                        {
                            "resourceId":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "resourceName":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "resourceType":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "eventTime":
                            {
                                "type": "date",
                                "format": "date_time"
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(ARM_TEMPLATES_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "deploymentId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deploymentName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "templateHash":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deploymentTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                     "eventTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "location":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceGroup":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "subscription":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceEvents":
                    {
                        "properties":
                        {
                            "resourceId":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "resourceName":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "resourceType":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "eventTime":
                            {
                                "type": "date",
                                "format": "date_time"
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(TERRAFORM_RESOURCES_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "resourceId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "eventTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "repository":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "commitId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "filename":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "resourceGroup":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "account":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "region":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "csp":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "approach":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(TF_COMMITS_INDEX,
		`{
    	    "settings":
    	    {
    	        "analysis":
    	        {
    	            "analyzer":
    	            {
    	                "default":
    	                {
    	                    "type": "keyword"
    	                }
    	            }
    	        },
    	        "number_of_shards": 1,
    	        "number_of_replicas": 0
    	    },
    	    "mappings":
    	    {
    	        "properties":
    	        {
    	            "commitDocId":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "resourceLabelName":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "resourceType":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "staticResourceProperties":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
                    "dynamicResourceProperties":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
                    "variables":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "serviceCode":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "tenantId":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
    	            "gitClient":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
                    "repoName":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
                    "commitTime":
    	            {
    	                "type": "date",
    	                "format": "date_time"
    	            },
                    "filePath":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            },
                    "unprocessedResourceType":
    	            {
    	                "type": "text",
    	                "fields":
    	                {
    	                    "keyword":
    	                    {
    	                        "type": "keyword",
    	                        "ignore_above": 256
    	                    }
    	                }
    	            }, 
                    "resourceTemplate":
                    {
                        "type": "text",
                        "index": false
                    },        
                    "variableModifiedCommitDocIds":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }   
    
    	        }
    	    }
    	}`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(TF_VARIABLES_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "filePath":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "varName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "variables":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 10000
                            }
                        }
                    },
                    "gitClient":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	return nil
}
