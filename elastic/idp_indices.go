package elastic

func initializeIdpIndices() error {

	err := CreateIndex(IDP_EVENTS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "eventId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "eventType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "eventDisplayMessage":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "actorId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "actorType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "publishedTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "clientId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "targets": {
                        "type":"nested",
                        "properties":
                        {
                            "targetId":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "targetType":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            }
                        }
                    },
                    "sourceIp":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "idpType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                     "domain":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(IDP_USERS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "userId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "email":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                     "secondaryEmail":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "name":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "title":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "managerId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "managerName":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "createdTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "lastUpdatedTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "insertTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "idpType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "userType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "organization":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "department":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "division":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "status":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deleted":
                    {
                        "type": "boolean"
                    },
                    "apps":
                    {
                        "type":"nested",
                        "properties":
                        {
                            "appId":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "appUsername":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            },
                            "appUserAdditional":
                            {
                                "type": "text",
                                "fields":
                                {
                                    "keyword":
                                    {
                                        "type": "keyword",
                                        "ignore_above": 256
                                    }
                                }
                            }
                        }
                    },
                     "groups":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "domain":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "lastLoginTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(IDP_APPS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "appId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "label":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "name":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "createdTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "lastUpdatedTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "insertTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "idpType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "signOnMode":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "status":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "settings":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deleted":
                    {
                        "type": "boolean"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "domain":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	err = CreateIndex(IDP_GROUPS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "groupId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "name":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "description":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "createdTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "lastUpdatedTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "insertTime":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "lastMembershipUpdated":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "idpType":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "type":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "deleted":
                    {
                        "type": "boolean"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "domain":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	return nil
}
