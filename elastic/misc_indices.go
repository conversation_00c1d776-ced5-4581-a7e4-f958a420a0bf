package elastic

func initializeMiscIndices() error {

	err := CreateIndex(PRECIZE_CREATIONS_INDEX,
		`{
            "settings":
            {
                "analysis":
                {
                    "analyzer":
                    {
                        "default":
                        {
                            "type": "keyword"
                        }
                    }
                },
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings":
            {
                "properties":
                {
                    "id":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "name":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "type":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "account":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "service":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "createdDate":
                    {
                        "type": "date",
                        "format": "date_time"
                    },
                    "serviceId":
                    {
                        "type": "integer"
                    },
                    "tenantId":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    },
                    "entityJson":
                    {
                        "type": "text",
                        "fields":
                        {
                            "keyword":
                            {
                                "type": "keyword",
                                "ignore_above": 256
                            }
                        }
                    }
                }
            }
        }`,
	)
	if err != nil {
		return err
	}

	// err = CreateIndex(CUSTOMER_DEFINITIONS_INDEX,
	// 	`{
	//         "settings":
	//         {
	//             "analysis":
	//             {
	//                 "analyzer":
	//                 {
	//                     "default":
	//                     {
	//                         "type": "keyword"
	//                     }
	//                 }
	//             },
	//             "number_of_shards": 1,
	//             "number_of_replicas": 0
	//         },
	//         "mappings":
	//         {
	//             "properties":
	//             {
	//                 "entityId":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "entityType":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "accountId":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "tenantId":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "serviceId":
	//                 {
	//                     "type": "integer"
	//                 },
	//                 "definitionType":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "owners":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "envs":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "compliances":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "sensitivities":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "apps":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "insertTime":
	//                 {
	//                     "type": "date",
	//                     "format": "date_time"
	//                 },
	//                 "updatedBy":
	//                 {
	//                     "type": "text",
	//                     "fields":
	//                     {
	//                         "keyword":
	//                         {
	//                             "type": "keyword",
	//                             "ignore_above": 256
	//                         }
	//                     }
	//                 },
	//                 "updateTime":
	//                 {
	//                     "type": "date",
	//                     "format": "date_time"
	//                 }
	//             }
	//         }
	//     }`,
	// )
	// if err != nil {
	// 	return err
	// }

	return nil
}
