package context

import (
	"encoding/json"
	"sort"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetTenantContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for azure tenant context", []string{resourceContext.TenantID})

	var (
		searchAfter          any
		tenantResourcesQuery = `{"_source":["entityId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_TENANT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		tenantResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore            = make(chan struct{}, MAX_PARENT_THREAD)
		wg                   sync.WaitGroup
		batchWg              sync.WaitGroup
		collectedDocIDs      []string
		mutex                sync.Mutex
	)

	go func() {
		for {
			tenantResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, tenantResourcesQuery, searchAfter)
			if err != nil {
				close(tenantResourcesChan)
				return
			}

			if len(tenantResourcesDocs) > 0 {
				searchAfter = sortResponse
				tenantResourcesChan <- tenantResourcesDocs
			} else {
				close(tenantResourcesChan)
				return
			}
		}
	}()

	for tenantResourcesDocs := range tenantResourcesChan {
		for tenantDocID, tenantDoc := range tenantResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processTenantResource(resourceContext, doc, docID, &mutex)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}
			}(tenantDocID, tenantDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		if err := getActivityContextOfResource(resourceContext, collectedDocIDs); err != nil {
			return
		}
	}

	logger.Print(logger.INFO, "Processing complete for azure tenant context", []string{resourceContext.TenantID})
}

func processTenantResource(resourceContext *ResourceContext, tenantResourcesDoc map[string]any, tenantResourcesDocID string, mutex *sync.Mutex) (contextDocID string) {
	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		tenantName               string
	)

	if azTenantID, ok := tenantResourcesDoc["entityId"].(string); ok {
		contextDocID = common.GenerateCombinedHashID(azTenantID, common.AZURE_TENANT_RESOURCE_TYPE, azTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         azTenantID,
			ResourceName:       tenantName,
			ResourceType:       common.AZURE_TENANT_RESOURCE_TYPE,
			Account:            azTenantID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AZURE_SERVICE_ID_INT,
			CloudResourceDocID: tenantResourcesDocID,
		}

		mutex.Lock()
		resourceContext.AzureTenantIDs = append(resourceContext.AzureTenantIDs, azTenantID)
		mutex.Unlock()

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, tenantResourcesDoc, &resourceContextInsertDoc)

		if entityJSON, ok := tenantResourcesDoc["entityJson"].(string); ok {
			tenantName = getNameForAzureResource(azTenantID, common.AZURE_TENANT_RESOURCE_TYPE, entityJSON)
			if envName := GetEnvironmentNameFromValue(tenantName); len(envName) > 0 {
				resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
					common.ResourceContextItem{
						Name: envName,
						Type: common.TENANT_NAME_ENV_TYPE,
					},
				)
			}
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func GetManagementGroupContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for azure management group context", []string{resourceContext.TenantID})

	var (
		searchAfter           any
		mgmtGrpResourcesQuery = `{"_source":["entityId","tags","accountId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"entityType.keyword":"` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		mgmtGrpResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		for {
			mgmtGrpResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, mgmtGrpResourcesQuery, searchAfter)
			if err != nil {
				close(mgmtGrpResourcesChan)
				return
			}

			if len(mgmtGrpResourcesDocs) > 0 {
				searchAfter = sortResponse
				mgmtGrpResourcesChan <- mgmtGrpResourcesDocs
			} else {
				close(mgmtGrpResourcesChan)
				return
			}
		}
	}()

	for mgmtGrpResourcesDocs := range mgmtGrpResourcesChan {
		for mgmtGrpDocID, mgmtGrpDoc := range mgmtGrpResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processManagementGroupResource(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}
			}(mgmtGrpDocID, mgmtGrpDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
	}

	// Have to iterate again because parent of management group can be another management group
	searchAfter = nil
	mgmtGrpResourcesChan = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
	semaphore = make(chan struct{}, MAX_PARENT_THREAD)

	go func() {
		for {
			mgmtGrpResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, mgmtGrpResourcesQuery, searchAfter)
			if err != nil {
				close(mgmtGrpResourcesChan)
				return
			}

			if len(mgmtGrpResourcesDocs) > 0 {
				searchAfter = sortResponse
				mgmtGrpResourcesChan <- mgmtGrpResourcesDocs
			} else {
				close(mgmtGrpResourcesChan)
				return
			}
		}
	}()

	for mgmtGrpResourcesDocs := range mgmtGrpResourcesChan {
		for _, mgmtGrpDoc := range mgmtGrpResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processManagementGroupParentRelationships(resourceContext, doc)
			}(mgmtGrpDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for azure management group context", []string{resourceContext.TenantID})
}

func processManagementGroupResource(resourceContext *ResourceContext, mgmtGrpResourcesDoc map[string]any, mgmtGrpResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		mgmtGrpName              string
	)

	if mgmtGrpID, ok := mgmtGrpResourcesDoc["entityId"].(string); ok {

		mgmtGrpOrAzTenantID, _ := mgmtGrpResourcesDoc["accountId"].(string)

		entityJSONString, _ := mgmtGrpResourcesDoc["entityJson"].(string)
		entityJSON := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err == nil {
			mgmtGrpName = getNameForAzureResource(mgmtGrpID, common.AZURE_MGMTGRP_RESOURCE_TYPE, entityJSONString)
		}

		contextDocID = common.GenerateCombinedHashID(mgmtGrpID, common.AZURE_MGMTGRP_RESOURCE_TYPE, mgmtGrpID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         mgmtGrpID,
			ResourceName:       mgmtGrpName,
			ResourceType:       common.AZURE_MGMTGRP_RESOURCE_TYPE,
			Account:            mgmtGrpOrAzTenantID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.AZURE_SERVICE_ID_INT,
			CloudResourceDocID: mgmtGrpResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, mgmtGrpResourcesDoc, &resourceContextInsertDoc)

		//TODO: Get owner from updated by in entity json

		if envName := GetEnvironmentNameFromValue(mgmtGrpName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.MGMTGRP_NAME_ENV_TYPE,
				},
			)

			incrementParentChildEnvCount(resourceContext, envName, mgmtGrpOrAzTenantID, "")
		}

		if team := GetTeamNameFromValue(mgmtGrpName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.MGMTGRP_NAME_TEAM_TYPE,
				},
			)
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func processManagementGroupParentRelationships(resourceContext *ResourceContext, mgmtGrpDoc map[string]any) {

	if mgmtGrpID, ok := mgmtGrpDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(mgmtGrpID, common.AZURE_MGMTGRP_RESOURCE_TYPE, mgmtGrpID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		mgmtGrpOrAzTenantID, _ := mgmtGrpDoc["accountId"].(string)

		if len(mgmtGrpOrAzTenantID) > 0 {

			mgmtGrpContextID := common.GenerateCombinedHashID(mgmtGrpOrAzTenantID, common.AZURE_MGMTGRP_RESOURCE_TYPE, mgmtGrpOrAzTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if mgmtGrpRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(mgmtGrpContextID); ok {

				if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
					tmp.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(mgmtGrpRscCtxInsertDoc, common.AZURE_MGMTGRP_RESOURCE_TYPE)
					tmp.ResourceEnvTypes.InheritedEnv = getInheritedEnv(mgmtGrpRscCtxInsertDoc)
					tmp.ResourceTeamTypes.InheritedTeam = getInheritedTeam(mgmtGrpRscCtxInsertDoc)

					resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

					assignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType,
						mgmtGrpOrAzTenantID, mgmtGrpContextID, common.AZURE_MGMTGRP_RESOURCE_TYPE, false)
				}

			} else {

				azTenantContextID := common.GenerateCombinedHashID(mgmtGrpOrAzTenantID, common.AZURE_TENANT_RESOURCE_TYPE, mgmtGrpOrAzTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if azTenantRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(azTenantContextID); ok {

					if tmp, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {

						tmp.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(azTenantRscCtxInsertDoc, common.AZURE_TENANT_RESOURCE_TYPE)
						tmp.ResourceEnvTypes.InheritedEnv = getInheritedEnv(azTenantRscCtxInsertDoc)
						resourceContext.SetResourceContextInsertDoc(contextDocID, tmp)

						assignRelatedResource(resourceContext, tmp.ResourceID, contextDocID, tmp.ResourceType,
							mgmtGrpOrAzTenantID, azTenantContextID, common.AZURE_TENANT_RESOURCE_TYPE, false)
					}
				}
			}
		}
	}
}

func GetSubscriptionContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for azure subscription context", []string{resourceContext.TenantID})

	var (
		searchAfter                any
		subscriptionResourcesQuery = `{"_source":["entityId","tags","entityJson", "accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		subscriptionResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore                  = make(chan struct{}, MAX_PARENT_THREAD)
		wg                         sync.WaitGroup
		batchWg                    sync.WaitGroup
		collectedDocIDs            []string
		mutex                      sync.Mutex
	)

	// Start goroutine to fetch subscription resources
	go func() {
		for {
			subscriptionResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, subscriptionResourcesQuery, searchAfter)
			if err != nil {
				close(subscriptionResourcesChan)
				return
			}

			if len(subscriptionResourcesDocs) > 0 {
				searchAfter = sortResponse
				subscriptionResourcesChan <- subscriptionResourcesDocs
			} else {
				close(subscriptionResourcesChan)
				return
			}
		}
	}()

	for subscriptionResourcesDocs := range subscriptionResourcesChan {
		for subscriptionResourcesDocID, subscriptionResourcesDoc := range subscriptionResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()
				contextDocID := processSubscriptionResource(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}
			}(subscriptionResourcesDocID, subscriptionResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for azure subscription context", []string{resourceContext.TenantID})
}

func processSubscriptionResource(resourceContext *ResourceContext, subscriptionResourcesDoc map[string]any, subscriptionResourcesDocID string) (contextDocID string) {
	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		subscriptionName         string
		entityJSONMap            = make(map[string]any)
	)

	if subscriptionID, ok := subscriptionResourcesDoc["entityId"].(string); ok {

		if mgmtGrpOrAzTenantID, ok := subscriptionResourcesDoc["accountId"].(string); ok {

			if entityJSON, ok := subscriptionResourcesDoc["entityJson"].(string); ok {
				subscriptionName = getNameForAzureResource(subscriptionID, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, entityJSON)
				if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshal", err)
					return
				}
			}

			contextDocID = common.GenerateCombinedHashID(subscriptionID, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, subscriptionID, resourceContext.LastCollectedAt, resourceContext.TenantID)

			resourceContextInsertDoc = common.ResourceContextInsertDoc{
				ResourceID:         subscriptionID,
				ResourceName:       subscriptionName,
				ResourceType:       common.AZURE_SUBSCRIPTION_RESOURCE_TYPE,
				Account:            mgmtGrpOrAzTenantID,
				TenantID:           resourceContext.TenantID,
				Region:             "Global",
				ServiceID:          common.AZURE_SERVICE_ID_INT,
				CloudResourceDocID: subscriptionResourcesDocID,
			}

			setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
			getTagContextOfResource(resourceContext, subscriptionResourcesDoc, &resourceContextInsertDoc)

			if envName := GetEnvironmentNameFromValue(subscriptionName); len(envName) > 0 {
				resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
					common.ResourceContextItem{
						Name: envName,
						Type: common.SUBSCRIPTION_NAME_ENV_TYPE,
					},
				)

				incrementParentChildEnvCount(resourceContext, envName, mgmtGrpOrAzTenantID, "")
			}

			if team := GetTeamNameFromValue(subscriptionName); len(team) > 0 {
				resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
					common.ResourceContextItem{
						Name: team,
						Type: common.SUBSCRIPTION_NAME_TEAM_TYPE,
					},
				)
			}

			if classicAdministrators, ok := entityJSONMap["classicAdministrators"].([]any); ok {
				for _, classicAdministrator := range classicAdministrators {
					if adminMap, ok := classicAdministrator.(map[string]any); ok {
						if username, ok := adminMap["emailAddress"].(string); ok && len(username) > 0 {
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
								resourceContext.GetUserContextItem(username, common.SUBSCRIPTION_OWNER_USER_TYPE, "", "", nil),
							)
						}
					}
				}
			}

			if contactInterfaceArray, ok := entityJSONMap["alternateContacts"].([]any); ok {

				for _, contactInterface := range contactInterfaceArray {

					if contactMap, ok := contactInterface.(map[string]any); ok {

						if ownerType, ok := contactMap["alternateContactType"].(string); ok {

							var username string

							if username, _ = contactMap["emailAddress"].(string); len(username) <= 0 {
								if name, _ := contactMap["name"].(string); len(name) > 0 {
									username = name
								}
							}

							if len(username) > 0 {

								userType := common.SUBSCRIPTION_CONTACT_USER_TYPE

								switch ownerType {

								case "Microsoft.Billing/billingAccounts":

									resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.CostOwners,
										resourceContext.GetUserContextItem(username, userType, "", "", nil),
									)

								case "Microsoft.Security/securityContacts":

									resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners,
										resourceContext.GetUserContextItem(username, userType, "", "", nil),
									)
								}
							}
						}
					}
				}
			}

			// Subsription parent can be either management group or tenant
			if len(mgmtGrpOrAzTenantID) > 0 {

				mgmtGrpContextID := common.GenerateCombinedHashID(mgmtGrpOrAzTenantID, common.AZURE_MGMTGRP_RESOURCE_TYPE, mgmtGrpOrAzTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if mgmtGrpRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(mgmtGrpContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(mgmtGrpRscCtxInsertDoc, common.AZURE_MGMTGRP_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(mgmtGrpRscCtxInsertDoc)
					resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = getInheritedTeam(mgmtGrpRscCtxInsertDoc)

					assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
						mgmtGrpOrAzTenantID, mgmtGrpContextID, common.AZURE_MGMTGRP_RESOURCE_TYPE, false)
				} else {

					azTenantContextID := common.GenerateCombinedHashID(mgmtGrpOrAzTenantID, common.AZURE_TENANT_RESOURCE_TYPE, mgmtGrpOrAzTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

					if azTenantRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(azTenantContextID); ok {
						resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(azTenantRscCtxInsertDoc, common.AZURE_TENANT_RESOURCE_TYPE)
						resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(azTenantRscCtxInsertDoc)

						assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
							mgmtGrpOrAzTenantID, azTenantContextID, common.AZURE_TENANT_RESOURCE_TYPE, false)
					}

				}
			}

			resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

		}
	}

	return
}

func GetResourceGroupContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for azure resourcegroup context", []string{resourceContext.TenantID})

	var (
		searchAfter      any
		rgResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_RG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		rgResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore        = make(chan struct{}, MAX_PARENT_THREAD)
		wg               sync.WaitGroup
		batchWg          sync.WaitGroup
		collectedDocIDs  []string
		mutex            sync.Mutex
	)

	// Start goroutine to fetch resource group resources
	go func() {
		for {
			rgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, rgResourcesQuery, searchAfter)
			if err != nil {
				close(rgResourcesChan)
				return
			}

			if len(rgResourcesDocs) > 0 {
				searchAfter = sortResponse
				rgResourcesChan <- rgResourcesDocs
			} else {
				close(rgResourcesChan)
				return
			}
		}
	}()

	for rgResourcesDocs := range rgResourcesChan {
		for rgResourcesDocID, rgResourcesDoc := range rgResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processResourceGroup(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(rgResourcesDocID, rgResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for azure resourcegroup context", []string{resourceContext.TenantID})
}

func GetApplicationContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for azure application context", []string{resourceContext.TenantID})

	var (
		searchAfter       any
		appResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_GRAPHAPP_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		appResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
		wg                sync.WaitGroup
		batchWg           sync.WaitGroup
		collectedDocIDs   []string
		mutex             sync.Mutex
	)

	go func() {
		for {
			appResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, appResourcesQuery, searchAfter)
			if err != nil {
				close(appResourcesChan)
				return
			}

			if len(appResourcesDocs) > 0 {
				searchAfter = sortResponse
				appResourcesChan <- appResourcesDocs
			} else {
				close(appResourcesChan)
				return
			}
		}
	}()

	for appResourcesDocs := range appResourcesChan {
		for appResourcesDocID, appResourcesDoc := range appResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processApplication(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(appResourcesDocID, appResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for azure application context", []string{resourceContext.TenantID})
}

func processApplication(resourceContext *ResourceContext, appResourcesDoc map[string]any, appResourcesDocID string) (contextDocID string) {
	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
	)

	if appID, ok := appResourcesDoc["entityId"].(string); ok {

		if azureTenantID, ok := appResourcesDoc["accountId"].(string); ok {

			if region, ok := appResourcesDoc["region"].(string); ok {

				var appName, entityJSONString string

				if entityJSONString, ok = appResourcesDoc["entityJson"].(string); ok {
					appName = getNameForAzureResource(appID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, entityJSONString)
				}

				contextDocID = common.GenerateCombinedHashID(appID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         appID,
					ResourceName:       appName,
					ResourceType:       common.AZURE_GRAPHAPP_RESOURCE_TYPE,
					Account:            azureTenantID,
					TenantID:           resourceContext.TenantID,
					Region:             region,
					ServiceID:          common.AZURE_SERVICE_ID_INT,
					CloudResourceDocID: appResourcesDocID,
				}

				entityJSON := make(map[string]any)
				if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
					return
				}

				if accountType, _ := entityJSON["accountType"].(float64); int(accountType) == 4 {
					// Azure managed enterprise app
					if registeredAppID, _ := entityJSON["appId"].(string); len(registeredAppID) > 0 {
						if displayName, _ := entityJSON["displayName"].(string); len(displayName) > 0 {
							resourceContext.SetAzureManagedRegisteredApps(registeredAppID, displayName)
						}
					}
				}

				setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
				getResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
				getTagContextOfResource(resourceContext, appResourcesDoc, &resourceContextInsertDoc)

				if envName := GetEnvironmentNameFromValue(appName); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RG_NAME_ENV_TYPE,
						},
					)
				}

				// TODO: Inherited owners for apps?

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
			}
		}
	}

	return
}

func GetRoleAssignments(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Gathering started of role assignments", []string{resourceContext.TenantID})

	var (
		searchAfter                  any
		roleAssignmentResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_ROLEASSIGNMENT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		roleAssignmentResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore                    = make(chan struct{}, MAX_PARENT_THREAD)
		wg                           sync.WaitGroup
		batchWg                      sync.WaitGroup
	)

	go func() {
		for {
			roleAssignmentResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, roleAssignmentResourcesQuery, searchAfter)
			if err != nil {
				close(roleAssignmentResourcesChan)
				return
			}

			if len(roleAssignmentResourcesDocs) > 0 {
				searchAfter = sortResponse
				roleAssignmentResourcesChan <- roleAssignmentResourcesDocs
			} else {
				close(roleAssignmentResourcesChan)
				return
			}
		}
	}()

	for roleAssignmentResourcesDocs := range roleAssignmentResourcesChan {
		for _, roleAssignmentResourcesDoc := range roleAssignmentResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()
				processRoleAssignment(resourceContext, doc)
			}(roleAssignmentResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	logger.Print(logger.INFO, "Gathering complete of role assignments", []string{resourceContext.TenantID})
}

type AzureRoleAssignment struct {
	Properties struct {
		RoleDefinitionID string `json:"roleDefinitionId"`
		PrincipalID      string `json:"principalId"`
		PrincipalType    string `json:"principalType"`
		Scope            string `json:"scope"`
		CreatedBy        string `json:"createdBy"`
		UpdatedBy        string `json:"updatedBy"`
		AzureTenantID    string `json:"azureTenantId"`
	} `json:"properties"`
}

func processRoleAssignment(resourceContext *ResourceContext, roleAssignmentResourcesDoc map[string]any) {

	if _, ok := roleAssignmentResourcesDoc["entityId"].(string); ok {
		if entityJSON, ok := roleAssignmentResourcesDoc["entityJson"].(string); ok {
			var azureRoleAssignment AzureRoleAssignment
			if err := json.Unmarshal([]byte(entityJSON), &azureRoleAssignment); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if azureRoleAssignment.Properties.PrincipalType == "ServicePrincipal" {
				appID := azureRoleAssignment.Properties.PrincipalID
				azureTenantID := azureRoleAssignment.Properties.AzureTenantID
				appContextDocID := common.GenerateCombinedHashID(appID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if appResourceContextInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(appContextDocID); ok {

					createdBy := azureRoleAssignment.Properties.CreatedBy
					updatedBy := azureRoleAssignment.Properties.UpdatedBy

					createdByEmail, ok := resourceContext.GetUserIDToEmail(createdBy)
					if ok {
						appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(createdByEmail, common.POLICYBINDING_USER_TYPE, "User has assigned roles for the resource", "", nil),
						)
					} else {
						createdByAppName := getAzureAppName(resourceContext, createdBy, azureTenantID)
						appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(createdByAppName+APP_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
								"Application has assigned roles for the resource", createdBy, nil),
						)

						uniqueIdentities := make(map[string]struct{})
						getApplicationOwnersForPolicy(resourceContext, createdBy, azureTenantID, &appResourceContextInsertDoc, uniqueIdentities,
							"User owned Application "+createdBy+" has assigned roles for the resource", 1)
					}

					updatedByEmail, ok := resourceContext.GetUserIDToEmail(updatedBy)
					if ok {
						if updatedByEmail != createdByEmail {
							appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
								appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
								resourceContext.GetUserContextItem(updatedByEmail, common.POLICYBINDING_USER_TYPE, "User has assigned roles for the resource", "", nil),
							)
						}
					} else {
						updatedByAppName := getAzureAppName(resourceContext, updatedBy, azureTenantID)
						appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(updatedByAppName+APP_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
								"Application has assigned roles for the resource", updatedBy, nil),
						)

						uniqueIdentities := make(map[string]struct{})
						getApplicationOwnersForPolicy(resourceContext, updatedBy, azureTenantID, &appResourceContextInsertDoc, uniqueIdentities,
							"User owned Application "+updatedBy+" has assigned roles for the resource", 1)
					}

					sortUserOverApplication(appResourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners)
					resourceContext.SetResourceContextInsertDoc(appContextDocID, appResourceContextInsertDoc)
				}
			}

		}
	}

	return
}

func processResourceGroup(resourceContext *ResourceContext, rgResourcesDoc map[string]any, rgResourcesDocID string) (contextDocID string) {
	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
	)

	if rgID, ok := rgResourcesDoc["entityId"].(string); ok {

		if subscriptionID, ok := rgResourcesDoc["accountId"].(string); ok {

			if region, ok := rgResourcesDoc["region"].(string); ok {

				rgName := getNameForAzureResource(rgID, common.AZURE_RG_RESOURCE_TYPE, "")

				contextDocID = common.GenerateCombinedHashID(rgID, common.AZURE_RG_RESOURCE_TYPE, subscriptionID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         rgID,
					ResourceName:       rgName,
					ResourceType:       common.AZURE_RG_RESOURCE_TYPE,
					Account:            subscriptionID,
					TenantID:           resourceContext.TenantID,
					Region:             region,
					ServiceID:          common.AZURE_SERVICE_ID_INT,
					CloudResourceDocID: rgResourcesDocID,
				}

				setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)

				if entityJSONString, ok := rgResourcesDoc["entityJson"].(string); ok {

					entityJSON := make(map[string]any)

					if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
						logger.Print(logger.ERROR, "Error unmarshalling entity json", []string{resourceContext.TenantID}, err)
						return
					}

					getResourceInbuiltProperty(resourceContext, entityJSON, &resourceContextInsertDoc)
				}

				getTagContextOfResource(resourceContext, rgResourcesDoc, &resourceContextInsertDoc)

				if envName := GetEnvironmentNameFromValue(rgName); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RG_NAME_ENV_TYPE,
						},
					)

					incrementParentChildEnvCount(resourceContext, envName, subscriptionID, "")
				}

				if team := GetTeamNameFromValue(rgName); len(team) > 0 {
					resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
						common.ResourceContextItem{
							Name: team,
							Type: common.RG_NAME_TEAM_TYPE,
						},
					)
				}

				if ownersByType, ok := resourceContext.GetJiraOpsOwners(subscriptionID); ok {
					jiraOpsOwners, ok := ownersByType[common.AZURE_RG_RESOURCE_TYPE]
					if ok {

						for _, jiraOpsOwner := range jiraOpsOwners {
							resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
								resourceContext.GetUserContextItem(jiraOpsOwner, common.JIRA_USER_TYPE, "User has worked on JIRA issues in this subscription related to this resource type", "", nil),
							)
						}
					}
				}

				subsContextID := common.GenerateCombinedHashID(subscriptionID, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, subscriptionID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if subsRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(subsContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(subsRscCtxInsertDoc, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(subsRscCtxInsertDoc)
					resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = getInheritedTeam(subsRscCtxInsertDoc)
				}

				assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
					subscriptionID, subsContextID, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, false)

				// TODO: Once subscription has other types of owners, add those to rg as well (cost, security, ops)

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
			}
		}
	}

	return
}

func GetNetworkSecurityGroupRules(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Gathering started of network security group rules", []string{resourceContext.TenantID})

	var (
		searchAfter       any
		nsgResourcesQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.AZURE_NSG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.AZURE_SERVICE_ID + `}}]}}}`
		nsgResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
		wg                sync.WaitGroup
	)

	go func() {
		for {
			nsgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, nsgResourcesQuery, searchAfter)
			if err != nil {
				close(nsgResourcesChan)
				return
			}

			if len(nsgResourcesDocs) > 0 {
				searchAfter = sortResponse
				nsgResourcesChan <- nsgResourcesDocs
			} else {
				close(nsgResourcesChan)
				return
			}
		}
	}()

	for nsgResourcesDocs := range nsgResourcesChan {
		for nsgResourcesDocID, nsgResourcesDoc := range nsgResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(nsgResourcesDocID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processNetworkSecurityGroup(resourceContext, doc)
			}(nsgResourcesDocID, nsgResourcesDoc)
		}
	}

	wg.Wait()
	logger.Print(logger.INFO, "Gathering complete of network security group rules", []string{resourceContext.TenantID})
}

func processNetworkSecurityGroup(resourceContext *ResourceContext, nsgResourcesDoc map[string]any) {
	nsgID, ok := nsgResourcesDoc["entityId"].(string)
	if !ok {
		return
	}

	entityJson, ok := nsgResourcesDoc["entityJson"].(string)
	if !ok {
		return
	}

	entityJsonMap := make(map[string]any)
	if err := json.Unmarshal([]byte(entityJson), &entityJsonMap); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	properties, ok := entityJsonMap["properties"].(map[string]any)
	if !ok {
		return
	}

	securityRules, ok := properties["securityRules"].([]any)
	if !ok {
		return
	}

	networkInboundPorts := make(map[int]struct{})

	for _, securityRule := range securityRules {
		securityRuleJson, ok := securityRule.(map[string]any)
		if !ok {
			continue
		}

		securityRuleProperties, ok := securityRuleJson["properties"].(map[string]any)
		if !ok {
			continue
		}

		direction, ok := securityRuleProperties["direction"].(string)
		if !ok || direction != "Inbound" {
			continue
		}

		fromPort, _ := securityRuleProperties["fromPort"].(float64)
		toPort, _ := securityRuleProperties["toPort"].(float64)

		networkInboundPorts[int(fromPort)] = struct{}{}
		networkInboundPorts[int(toPort)] = struct{}{}
	}

	resourceContext.SetNetworkInboundPorts(strings.ToLower(nsgID), networkInboundPorts)
}

func getAzureAppName(resourceContext *ResourceContext, applicationID, azureTenantID string) (applicationName string) {

	applicationName = applicationID

	if len(azureTenantID) > 0 {
		applicationContextID := common.GenerateCombinedHashID(applicationID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		if applicationDoc, ok := resourceContext.GetResourceContextInsertDoc(applicationContextID); ok {
			if len(applicationDoc.ResourceName) > 0 {
				applicationName = applicationDoc.ResourceName
				return
			}
		}
	} else {
		for _, azTenant := range resourceContext.AzureTenantIDs {
			applicationContextID := common.GenerateCombinedHashID(applicationID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azTenant, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if applicationDoc, ok := resourceContext.GetResourceContextInsertDoc(applicationContextID); ok {
				if len(applicationDoc.ResourceName) > 0 {
					applicationName = applicationDoc.ResourceName
					return
				}
			}
		}
	}

	if commonApp, ok := common.COMMON_AZURE_APPS[applicationID]; ok {
		applicationName = commonApp
		return
	}

	if appName, ok := resourceContext.GetAzureManagedRegisteredApps(applicationID); ok {
		// Some ownerships are giving registered app id and not enterprise app id of azure managed apps
		applicationName = appName
	}

	return
}

func sortUserOverApplication(slice []common.ResourceContextItem) {
	sort.SliceStable(slice, func(i, j int) bool {
		isAppI := strings.HasSuffix(slice[i].Name, APP_USER_SUFFIX)
		isAppJ := strings.HasSuffix(slice[j].Name, APP_USER_SUFFIX)

		if isAppI != isAppJ {
			return !isAppI
		}

		return false
	})
}
