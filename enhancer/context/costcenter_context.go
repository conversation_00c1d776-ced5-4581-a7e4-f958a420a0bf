package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func isCostCenterKey(tagKey string) bool {

	for r := range CostCenterTagKeys {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func getInheritedCostCenter(parentDoc common.ResourceContextInsertDoc) (inheritedCostCenter []common.ResourceContextItem) {

	if len(parentDoc.DefinedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.DefinedCostCenter...)
	} else if len(parentDoc.InheritedCostCenter) > 0 {
		inheritedCostCenter = append(inheritedCostCenter, parentDoc.InheritedCostCenter...)
	}

	return
}

func GetUniqueCostCenterContext(resourceContextDoc *common.ResourceContextInsertDoc) (costCenter []string) {

	uniqueCostCenter := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceCostCenterTypes.DefinedCostCenter {
		uniqueCostCenter[v.Name] = struct{}{}
	}
	for _, v := range resourceContextDoc.ResourceCostCenterTypes.InheritedCostCenter {
		uniqueCostCenter[v.Name] = struct{}{}
	}

	for costCenterName := range uniqueCostCenter {
		costCenter = append(costCenter, costCenterName)
	}

	return
}
