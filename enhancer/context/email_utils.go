package context

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

type TextExampleAggs struct {
	HasNameTrue struct {
		TopHasNameTrue struct {
			Hits struct {
				Hits []struct {
					Source struct {
						Text string `json:"text"`
					} `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		} `json:"top_hasName_true"`
	} `json:"hasName_true"`
	HasNameFalse struct {
		TopHasNameFalse struct {
			Hits struct {
				Hits []struct {
					Source struct {
						Text string `json:"text"`
					} `json:"_source"`
				} `json:"hits"`
			} `json:"hits"`
		} `json:"top_hasName_false"`
	} `json:"hasName_false"`
}

// Not being Used
func UpdateEmailAliasValues(resourceContext *ResourceContext) {

	var (
		bulkUserEmailDetailsRequest string
		recordsCount                int
		maxRecords                  = 10000
	)

	resourceContext.RangeAliasUpdateMap(func(emailDocID string, isAlias bool) bool {

		userEmailDetailsUpdateMetadata := `{"update": {"_id": "` + emailDocID + `"}}`
		userEmailDetailsUpdateDoc := `{"doc":{"isAlias":` + strconv.FormatBool(isAlias) + `}}`

		bulkUserEmailDetailsRequest = bulkUserEmailDetailsRequest + userEmailDetailsUpdateMetadata + "\n" + userEmailDetailsUpdateDoc + "\n"
		recordsCount++

		if recordsCount >= maxRecords {

			if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.USER_EMAIL_DETAILS_INDEX, bulkUserEmailDetailsRequest); err != nil {
				return false
			}

			logger.Print(logger.INFO, "User email details bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

			recordsCount = 0
			bulkUserEmailDetailsRequest = ""
		}

		return true
	})

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.USER_EMAIL_DETAILS_INDEX, bulkUserEmailDetailsRequest); err != nil {
			return
		}

		logger.Print(logger.INFO, "User email details bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})
	}
}

func GetEmailFormatsForTenant(tenantID string) (emailFormats []string, err error) {

	emailFormatsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	emailFormatsDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, emailFormatsQuery)
	if err != nil {
		return
	}

	if len(emailFormatsDocs) == 0 {
		body := "Company MetaData Not Present for Tenant: " + tenantID

		recipients := make(map[string]string)
		cc := make(map[string]string)
		if config.Environment == config.QA_ENV || config.Environment == config.PREPROD_ENV {
			recipients = map[string]string{
				"Aniket": "<EMAIL>",
			}
		} else {
			recipients = map[string]string{
				"Muskaan": "<EMAIL>",
			}
			cc = map[string]string{
				"Abhay":  "<EMAIL>",
				"Aniket": "<EMAIL>",
			}
		}
		email.SendEmail("Company Metadata not present", body, recipients, cc, nil)
	}

	for _, emailFormatsDoc := range emailFormatsDocs {

		if companyDetail, ok := emailFormatsDoc["companyDetail"].(map[string]any); ok {

			if emailFormatsList, ok := companyDetail["emailFormat"].([]any); ok {

				for _, emailFormat := range emailFormatsList {

					if emailFormatString, ok := emailFormat.(string); ok {
						emailFormats = append(emailFormats, emailFormatString)
					}
				}
			}
		}
	}

	return
}

func GetSDDLStatus(tenantID string) (sddlStatus bool, err error) {
	emailFormatsQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	companyMetaDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, emailFormatsQuery)
	if err != nil {
		return
	}

	for _, companyMetaDoc := range companyMetaDocs {

		if isSDDL, ok := companyMetaDoc["isSDDLFollowed"].(bool); ok {
			return isSDDL, nil
		}
	}

	return
}

func getPrimaryDomains(tenantID string) (primaryDomains []string) {

	primaryDomainQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	primaryDomainDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, primaryDomainQuery)
	if err != nil {
		return
	}

	if len(primaryDomainDocs) == 1 {
		if companyDetail, ok := primaryDomainDocs[0]["companyDetail"].(map[string]any); ok {
			if domainDetails, ok := companyDetail["domainDetails"].([]any); ok {
				for _, domainDetail := range domainDetails {
					if domainDetailMap, ok := domainDetail.(map[string]any); ok {
						if primaryDomain, ok := domainDetailMap["domain"].(string); ok {
							primaryDomains = append(primaryDomains, primaryDomain)
						}
					}
				}
			}
		}
	}

	return
}

func deriveEmailFromGCPTag(str string, r *ResourceContext) (string, error) {

	for _, emailFormat := range r.EmailFormats {

		var (
			isValidEmail, isValidEmailDerived bool
		)

		email := convertGCPTagToEmail(str, emailFormat, r)
		if len(email) <= 0 {
			return "", nil
		}

		if _, ok := r.GetUserResource(email); ok {
			return email, nil
		}

		name := common.GetFormattedNameFromEmail(email)

		if _, isValidEmailDerived = r.GetEmailStatus(email); !isValidEmailDerived {
			emailNameMap := map[string]string{
				email: name,
			}

			emailStatusMap, err := checkEmailValidity(emailNameMap, true, r)
			if err != nil {
				return "", err
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[email]
				r.SetEmailStatus(email, isValidEmail)
				_, isValidEmailDerived = emailStatusMap[email]
			}
		}

		if isValidEmailDerived {
			return email, nil
		}
	}

	if len(r.EmailFormats) > 0 {
		return convertGCPTagToEmail(str, r.EmailFormats[0], r), errors.New(INVALID_EMAIL_ERROR)
	}

	return "", errors.New(NO_EMAIL_FORMAT_ERROR)
}

func checkEmailValidity(emailNameMap map[string]string, isEmailDerived bool, r *ResourceContext) (map[string]bool, error) {

	emailResponse := make(map[string]bool)

	// Checking invalid emails
	for email := range emailNameMap {
		if ok := r.GetInvalidEmailCache(email); ok {
			if isEmailDerived {
				delete(emailNameMap, email)
			} else {
				emailResponse[email] = false
			}
		}
	}

	if len(emailNameMap) <= 0 {
		return emailResponse, nil
	}

	logger.Print(logger.INFO, "Email Status Request", emailNameMap)

	jsonData, err := json.Marshal(emailNameMap)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshaling JSON", err)
		return nil, err
	}
	resp, err := transport.SendRequestToServer("POST", "/precize/private/"+r.TenantID+"/emailStatus", nil, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	var verifyEmailResponse VerifyEmailResponse

	if err = json.Unmarshal(resp, &verifyEmailResponse); err != nil {

		if verifyEmailResponse.ServerResponseInfo.Status == 429 {
			var verifyEmailErrorResponse VerifyEmailErrorResponse

			errRsp := strings.Replace(string(resp), "429 Too Many Requests: ", "", 1)
			if err = json.Unmarshal([]byte(errRsp), &verifyEmailErrorResponse); err == nil {

				unescaped := common.UnescapeString(verifyEmailErrorResponse.Data)
				var response VerifyEmailError
				err := json.Unmarshal([]byte(unescaped), &response)
				if err != nil {
					logger.Print(logger.ERROR, "Got error while unmarshalling rate limit error ", []string{r.TenantID}, err)
				}
				if waitTime, err := strconv.ParseFloat(response.Wait, 64); err == nil {
					logger.Print(logger.INFO, "Rate Limiting Response from EmailStatus sleeping until: ", response.Wait, "seconds")
					time.Sleep(time.Duration(waitTime * float64(time.Second)))
					resp, err := transport.SendRequestToServer("POST", "/precize/private/"+r.TenantID+"/emailStatus", nil, bytes.NewBuffer(jsonData))
					if err != nil {
						return nil, err
					}

					if err = json.Unmarshal(resp, &verifyEmailResponse); err != nil {
						logger.Print(logger.ERROR, "Got error calling unmarshal while calling emailStatus after wait time", []string{r.TenantID}, err)
						return nil, err

					}
				}

			}
		} else {
			logger.Print(logger.ERROR, "Got error calling unmarshal", []string{r.TenantID}, err)
			return nil, err
		}
	}

	for email, status := range verifyEmailResponse.Data {
		switch status {
		case "deliverable":
			emailResponse[email] = true
		case "undeliverable":
			emailResponse[email] = false
		case "invalid":
			if !isEmailDerived {
				emailResponse[email] = false
				r.SetUpdateUndeliverableValidEmail(email)
			} else {
				r.SetInvalidEmailCache(email)
			}
		default:
			// in all other cases treat email as a valid email
			emailResponse[email] = true
		}
	}

	logger.Print(logger.INFO, "Email Status Response", verifyEmailResponse.Data)
	return emailResponse, nil
}

// Function not being used
// func deriveEmailFromGCPTagDeprecated(str, tenantID string) (email string) {

// 	var (
// 		tenantEmailFormat map[string]string
// 		emailFormatPath   = "./email_formats.json"
// 	)

// 	emailFormat, err := os.ReadFile(emailFormatPath)
// 	if err == nil {
// 		if err = json.Unmarshal(emailFormat, &tenantEmailFormat); err != nil {
// 			logger.Print(logger.ERROR, "Got error calling unmarshal", emailFormatPath, err)
// 			return
// 		}
// 	}

// 	if tenantEmailFormat, ok := tenantEmailFormat[tenantID]; ok && len(tenantEmailFormat) > 0 {
// 		email = common.DeriveEmailFromText(str, tenantEmailFormat, tenantID)
// 	}

// 	return
// }

func separateEmailsFromDescription(desc string) []string {
	emails := make([]string, 0)
	descWords := strings.Split(desc, " ")

	for i := 0; i < len(descWords); i++ {
		if strings.Contains(descWords[i], "@") {
			email := removeExtraSpecialCharactersFromEmail(descWords[i])

			if addr, err := common.ParseAddress(email); err == nil {
				emails = append(emails, addr.Address)
			}
		}
	}
	return emails
}

func removeNamesOfTaggedEmails(names, emails []string) []string {
	for i := 0; i < len(names); i++ {
		for _, email := range emails {

			name := names[i]
			name = common.RemoveSpecialCharactersFromString(name)
			emailName := common.GetEmailNameWithoutSpecialCharacters(email)

			if strings.EqualFold(strings.ToLower(name), strings.ToLower(emailName)) || strings.EqualFold(strings.ToLower(emailName), strings.ToLower(name)) {
				names = append(names[:i], names[i+1:]...)
				i--
			}
		}

	}
	return names
}

func formCompleteEmailFormat(name, email string) string {

	if len(name) <= 0 {
		name = common.GetFormattedNameFromEmail(email)
	} else if len(name) > 0 && unicode.IsLower(rune(name[0])) {
		name = common.ConvertToTitleCase(name)
	}

	return name + " <" + strings.ToLower(email) + ">"
}

func convertGCPTagToEmail(gcpTag, tenantEmailFormat string, r *ResourceContext) (email string) {

	if strings.Contains(gcpTag, "_at") {
		gcpTag = strings.Split(gcpTag, "_at")[0]
	} else {
		// in case when _at is not present eg: firstName_lastName_precize_io
		regex := regexp.MustCompile(`[^a-zA-Z0-9\s]`)
		gcpTagSplit := regex.Split(gcpTag, -1)
		emailFormatSplit := strings.Split(tenantEmailFormat, "@")

		// determine split w.r.t to company domain
		if len(emailFormatSplit) > 1 {
			companyDomain := emailFormatSplit[1]
			domainPartLen := len(strings.Split(companyDomain, "."))
			if len(gcpTagSplit)-domainPartLen > 0 {
				gcpTagSplit = gcpTagSplit[:len(gcpTagSplit)-domainPartLen]
			}

		} else {
			if len(gcpTagSplit)-2 > 0 {
				gcpTagSplit = gcpTagSplit[:len(gcpTagSplit)-2]
			}
		}
		gcpTag = strings.Join(gcpTagSplit, "_")
	}

	re := regexp.MustCompile("[_-]")
	gcpTag = re.ReplaceAllString(gcpTag, " ")
	gcpTag = strings.Trim(gcpTag, " ")

	return deriveEmailFromNameAndEmailFormat(gcpTag, tenantEmailFormat, r)

}

// Not being used
// func removeExtraSpecialCharactersFromPrefix(email string) string {
// 	index := 0
// 	for i := 0; i < len(email); i++ {
// 		if !strings.Contains("~!$%^&*_=+}{'?-.", string(email[i])) {
// 			index = i
// 			break
// 		}
// 	}

// 	return email[index:]
// }

func removeExtraSpecialCharactersFromEmail(email string) string {
	for i := 0; i < len(email); i++ {
		if strings.Contains("~!$%^&*_=+}{'?-.@", string(email[i])) {
			if i == 0 {
				email = email[1:]
				i--

			} else if i == len(email)-1 {
				if strings.Contains("~!$%^&*_=+}{'?-.@", string(email[i])) {
					email = email[:i]
				}
			} else {
				if strings.Contains("~!$%^&*_=+}{'?-.@", string(email[i-1])) {
					email = email[:i-1] + email[i:]
					i--
				}
			}
		}
	}

	return email
}

func deriveEmailFromName(name string, r *ResourceContext, opts ...string) string {

	excludedEmailForMerging := ""

	// If optional parameter is provided, use it
	if len(opts) > 0 {
		excludedEmailForMerging = opts[0]
	}

	for _, emailFormat := range r.EmailFormats {

		var (
			email                             = deriveEmailFromNameAndEmailFormat(name, emailFormat, r)
			isValidEmail, isValidEmailDerived bool
		)

		if len(excludedEmailForMerging) > 0 && email == excludedEmailForMerging {
			continue
		}

		// Always send the longest name to email verification for partner evaluation
		emailName := name
		if formattedName := common.GetFormattedNameFromEmail(email); len(formattedName) > len(emailName) {
			emailName = formattedName
		}

		if _, isValidEmailDerived = r.GetEmailStatus(email); !isValidEmailDerived {
			emailNameMap := map[string]string{
				email: emailName,
			}

			emailStatusMap, err := checkEmailValidity(emailNameMap, true, r)
			if err != nil {
				return ""
			}

			if len(emailStatusMap) > 0 {
				isValidEmail = emailStatusMap[email]
				r.SetEmailStatus(email, isValidEmail)
				_, isValidEmailDerived = emailStatusMap[email]
			}
		}

		if isValidEmailDerived {
			return email
		}
	}

	return ""
}

func deriveEmailFromNameAndEmailFormat(name, emailFormat string, r *ResourceContext) (email string) {
	if name == "" {
		return
	}

	exceptions := make([]string, 0)

	if email, ok := r.GetDerivedEmailInclusions(strings.ToLower(name)); ok && len(email) > 0 {
		return email[0]
	}

	if email, ok := r.GetChildPrimaryEmail(strings.ToLower(name)); ok {
		if _, err := common.ParseAddress(email); err == nil {
			return email
		}
	}

	if formattedName := common.SeparateHumanNameFromNameString(name, r.TenantID); len(formattedName) > 0 {
		name = formattedName
	}

	if exclEmails, ok := r.GetDerivedEmailExclusions(strings.ToLower(name)); ok && len(exclEmails) > 0 {
		for _, exclEmail := range exclEmails {
			exceptions = append(exceptions, exclEmail)
		}
	}

	name = common.SeparateCamelCaseWithSpecialChar(name, "-")
	name = common.ReplaceSpecialCharactersWithChar(name, " ")
	name = strings.Trim(name, " ")
	nameParts := strings.Split(name, " ")
	partToNameMap := map[string]string{"first": "", "middle": "", "last": ""}

	if len(nameParts) >= 1 {
		if nameParts[0] != "" {
			partToNameMap["first"] = nameParts[0]
		}
		if len(nameParts) > 2 {
			partToNameMap["middle"] = nameParts[1]
			partToNameMap["last"] = nameParts[2]
		} else if len(nameParts) == 2 {
			partToNameMap["last"] = nameParts[1]
		}

	}
	emailTag := ""
	email = emailFormat

	for i := len(emailFormat) - 1; i >= 0; i-- {
		if emailFormat[i] == ']' {
			emailTag = ""
		}
		emailTag = string(emailFormat[i]) + emailTag
		if emailFormat[i] == '[' {
			if strings.Contains(strings.ToLower(emailTag), "first") || strings.Contains(strings.ToLower(emailTag), "middle") || strings.Contains(strings.ToLower(emailTag), "last") {
				val := ""
				if strings.Contains(strings.ToLower(emailTag), "first") {
					val = partToNameMap["first"]
				} else if strings.Contains(strings.ToLower(emailTag), "middle") {
					val = partToNameMap["middle"]
				} else if strings.Contains(strings.ToLower(emailTag), "last") {
					val = partToNameMap["last"]
				}

				if strings.Contains(strings.ToLower(emailTag), "initial") && val != "" {
					val = val[:1]
				} else if strings.Contains(strings.ToLower(emailTag), "(") && val != "" {

					startPos := strings.Index(emailTag, "(")
					endPos := strings.Index(emailTag, ")")
					if startPos > -1 && endPos > startPos {
						directive := emailTag[startPos+1 : endPos]

						if len(directive) >= 3 {
							direction := directive[0]
							operation := directive[1]
							count, err := strconv.Atoi(directive[2:])
							if err == nil && count > 0 {
								switch direction {
								case 'r', 'R':
									if operation == '-' && len(val) > count {
										// Remove 'count' characters from the right
										val = val[:len(val)-count]
									} else if operation == '+' && count > 0 {
										// Take only the rightmost 'count' characters
										if len(val) > count {
											val = val[len(val)-count:]
										}
									}
								case 'l', 'L':
									if operation == '+' && count > 0 {
										// Take only 'count' characters from the left
										if len(val) > count {
											val = val[:count]
										}
									} else if operation == '-' && len(val) > count {
										// Remove 'count' characters from the left
										val = val[count:]
									}
								}
							}
						}
					}
				}

				email = strings.Replace(email, emailTag, val, 1)
			} else {
				email = strings.Replace(email, emailTag, "", 1)
			}
		}
	}
	email = removeExtraSpecialCharactersFromEmail(email)
	email = strings.ToLower(email)

	// check if derived email is not part of exemptions list
	for _, exemptedEmails := range exceptions {
		if email == exemptedEmails {
			return ""
		}
	}
	return email
}

func UpdateUndeliverableValidEmails(resourceContext *ResourceContext) {
	emails := make([]string, 0)
	resourceContext.RangeUpdateUndeliverableValidEmails(func(email string) {
		emails = append(emails, email)
	})

	if len(emails) > 0 {
		jsonData, err := json.Marshal(emails)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling JSON", err)
			return
		}
		_, err = transport.SendRequestToServer("POST", "/precize/private/tenant/"+resourceContext.TenantID+"/updateUndeliveredValidEmails", nil, bytes.NewBuffer(jsonData))
		if err != nil {
			return
		}
	}
}

func DerivePrimaryIdentityFromPartner(childEmail, partnerName string, resourceContext *ResourceContext) (email string) {

	if parentEmail, ok := resourceContext.GetChildParentInclusions(childEmail); ok && len(parentEmail) > 0 {
		return parentEmail
	}

	parts := strings.Split(childEmail, "@")
	username := parts[0]

	for _, domain := range resourceContext.PrimaryDomains {
		primaryEmail := fmt.Sprintf("%s@%s", username, domain)
		if usrRsc, ok := resourceContext.GetUserResource(primaryEmail); ok && usrRsc.Email != childEmail {

			// if same email only tld differs return proper email
			if emailsTLDDiff(childEmail, primaryEmail) {
				return usrRsc.Email
			}

			name := strings.TrimSpace(strings.ToLower(usrRsc.Name))
			re := regexp.MustCompile(`[^a-zA-Z0-9]`)
			cleanedName := re.ReplaceAllString(name, "")

			if strings.Contains(partnerName, usrRsc.Name) || strings.Contains(usrRsc.Name, partnerName) || strings.Contains(partnerName, cleanedName) {
				return usrRsc.Email
			}
		}
	}

	// derive email from partner Name
	pEmail := deriveEmailFromName(partnerName, resourceContext, childEmail)
	if len(pEmail) > 0 {
		if usrRsc, ok := resourceContext.GetUserResource(pEmail); ok {
			if strings.Contains(partnerName, usrRsc.Name) || strings.Contains(usrRsc.Name, partnerName) {
				return usrRsc.Email
			}
		}
	}

	return
}

// Not being used
// func updatePrimaryEmailForIdentities(sddlPrimaryEmailMap map[string]string, tenantID string) {

// 	var (
// 		bulkIdentitiesRequest string
// 		recordsCount          int
// 		maxRecords            = 10000
// 	)

// 	for identityDocID, primaryEmail := range sddlPrimaryEmailMap {
// 		identitiesUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
// 		identitiesUpdateDoc := `{"doc":{"primaryEmail":"` + primaryEmail + `"}}`
// 		bulkIdentitiesRequest = bulkIdentitiesRequest + identitiesUpdateMetadata + "\n" + identitiesUpdateDoc + "\n"
// 		recordsCount++

// 		if recordsCount >= maxRecords {
// 			if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
// 				return
// 			}
// 			logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

// 			recordsCount = 0
// 			bulkIdentitiesRequest = ""
// 		}
// 	}

// 	if recordsCount > 0 {
// 		if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesRequest); err != nil {
// 			return
// 		}
// 		logger.Print(logger.INFO, "Identities bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
// 	}
// }

func extractExternalEmail(extEmail string) string {
	childEmail := strings.ToLower(extEmail)
	extIndex := strings.Index(childEmail, EXT_KEYWORD)
	if extIndex != -1 {
		childEmail = childEmail[:extIndex]
	}
	lastIndex := strings.LastIndex(childEmail, "_")
	if lastIndex != -1 {
		childEmail = childEmail[:lastIndex] + "@" + childEmail[lastIndex+1:]
	}
	return childEmail
}

// <EMAIL>, <EMAIL>
func emailsTLDDiff(email1, email2 string) bool {
	parts1 := strings.Split(email1, "@")
	parts2 := strings.Split(email2, "@")

	if len(parts1) != 2 || len(parts2) != 2 {
		return false
	}

	domain1 := strings.Split(parts1[1], ".")
	domain2 := strings.Split(parts2[1], ".")

	if len(domain1) < 2 || len(domain2) < 2 {
		return false
	}

	return parts1[0] == parts2[0] &&
		strings.Join(domain1[:len(domain1)-1], ".") ==
			strings.Join(domain2[:len(domain2)-1], ".")
}

func processGithubPrivateEmail(email string) (modifiedEmail string) {
	// Github private email usecase

	splitPlusSign := strings.Split(email, "+")
	if len(splitPlusSign) > 0 {
		splitEmailsign := strings.Split(splitPlusSign[1], "@")
		if len(splitEmailsign) > 0 {
			modifiedEmail = splitEmailsign[0]
		} else {
			return ""
		}
	}

	return
}

// Not being used
// func getDomainFromEmail(email string) string {
// 	parts := strings.Split(email, "@")
// 	if len(parts) != 2 {
// 		return ""
// 	}
// 	return parts[1]
// }
