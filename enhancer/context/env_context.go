package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
)

func GetEnvironmentNameFromValue(str string) string {

	str = strings.ToLower(str)

	for env, values := range envValues {

		for _, val := range values {

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {

				isEnv := true

				for _, notVal := range notMatchValues[val] {
					regex2 := regexp.MustCompile(notVal)
					if regex2.MatchString(str) {
						isEnv = false
					}
				}

				if isEnv {
					return env
				}
			}
		}
	}

	return ""
}

func getInheritedEnv(parentDoc common.ResourceContextInsertDoc) (inheritedEnv []common.ResourceContextItem) {

	if len(parentDoc.DefinedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.DefinedEnv...)
	} else if len(parentDoc.DerivedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.DerivedEnv...)
	} else if len(parentDoc.InheritedEnv) > 0 {
		inheritedEnv = append(inheritedEnv, parentDoc.InheritedEnv...)
	}

	return
}

func incrementParentChildEnvCount(resourceContext *ResourceContext, env, accountID, resourceGroup string) {
	if len(accountID) > 0 {
		value, exists := resourceContext.ParentChildEnv.Load(accountID)
		var envCounts map[string]int
		if exists {
			envCounts = value.(map[string]int)
		} else {
			envCounts = make(map[string]int)
		}

		envCounts[env]++
		resourceContext.ParentChildEnv.Store(accountID, envCounts)
	}

	if len(resourceGroup) > 0 {
		value, exists := resourceContext.ParentChildEnv.Load(resourceGroup)
		var envCounts map[string]int
		if exists {
			envCounts = value.(map[string]int)
		} else {
			envCounts = make(map[string]int)
		}

		envCounts[env]++
		resourceContext.ParentChildEnv.Store(resourceGroup, envCounts)
	}
}

func getMaxChildEnvOfParent(parentChildEnv map[string]int) (env string) {
	var max = 20

	for envKey, count := range parentChildEnv {
		if count > max {
			max = count
			env = envKey
		} else if count == max {
			// No clear majority
			env = ""
		}
	}
	return
}

func GetUniqueEnvContext(resourceContextDoc *common.ResourceContextInsertDoc, r *ResourceContext) (env []string) {
	uniqueEnv := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceEnvTypes.DefinedEnv {
		if len(v.Name) > 0 {
			uniqueEnv[v.Name] = struct{}{}
		}
	}
	for _, v := range resourceContextDoc.ResourceEnvTypes.DerivedEnv {
		if len(v.Name) > 0 {
			uniqueEnv[v.Name] = struct{}{}
		}
	}
	for _, v := range resourceContextDoc.ResourceEnvTypes.InheritedEnv {
		if len(v.Name) > 0 {
			uniqueEnv[v.Name] = struct{}{}
		}
	}
	for envName := range uniqueEnv {
		env = append(env, envName)
	}

	if len(env) <= 0 {

		// Parent env from child env

		switch resourceContextDoc.ResourceType {
		case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
			common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
			common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:

			if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.ResourceID); exists {
				maxChildEnv := getMaxChildEnvOfParent(childEnvs)
				if len(maxChildEnv) > 0 {
					resourceContextDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: maxChildEnv,
							Type: common.MAJORITY_ENV_TYPE,
						},
					)
					env = []string{maxChildEnv}
				}
			}
		}
	}

	if len(resourceContextDoc.ResourceEnvTypes.InheritedEnv) <= 0 {

		// Child env from parent env derived from child

		switch resourceContextDoc.ResourceType {
		case common.AZURE_TENANT_RESOURCE_TYPE, common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE:
			// no parent env
		default:

			var maxChildEnv string

			if len(resourceContextDoc.ResourceGroup) > 0 {
				if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.ResourceGroup); exists {
					maxChildEnv = getMaxChildEnvOfParent(childEnvs)
				}
			}

			if len(maxChildEnv) <= 0 && len(resourceContextDoc.Account) > 0 {
				if childEnvs, exists := r.GetParentChildEnv(resourceContextDoc.Account); exists {
					maxChildEnv = getMaxChildEnvOfParent(childEnvs)
				}
			}

			if len(maxChildEnv) > 0 {
				resourceContextDoc.ResourceEnvTypes.InheritedEnv = append(resourceContextDoc.ResourceEnvTypes.InheritedEnv,
					common.ResourceContextItem{
						Name: maxChildEnv,
						Type: common.MAJORITY_ENV_TYPE,
					},
				)

				if !slices.Contains(env, maxChildEnv) {
					env = append(env, maxChildEnv)
				}
			}
		}
	}

	return
}
