package context

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetGlobalOrgContext(resourceContext *ResourceContext) {

	docID := common.GenerateCombinedHashID(ORG_CONTEXT_TYPE, resourceContext.TenantID)

	orgContext, err := elastic.GetDocument(elastic.CUST_ENTITY_CONTEXT_INDEX, docID)
	if err != nil {
		return
	}

	if len(orgContext) > 0 {

		orgContextJson, err := json.Marshal(orgContext)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			return
		}

		var orgEntityContext common.CustomerEntityContextDoc

		if err = json.Unmarshal(orgContextJson, &orgEntityContext); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		for _, contextProperty := range orgEntityContext.ContextProperties {

			for _, includedProperty := range contextProperty.Include {

				switch contextProperty.PropertyName {

				case APP_PROPERTY_NAME:

					var appVals = strings.Split(includedProperty, ",")

					appName := appVals[0]

					for _, appVal := range appVals {

						appVal = strings.ToLower(appVal)
						if len(appVal) <= 4 {
							appVal = `\b` + appVal + `\b`
						}
						appVal = strings.ReplaceAll(appVal, " ", `[-_\s]*`)
						appVal = strings.ReplaceAll(appVal, "-", `[-_\s]*`)
						appVal = strings.ReplaceAll(appVal, "_", `[-_\s]*`)

						appValues[appName] = append(appValues[appName], appVal)
					}

					defaultAppValues = append(defaultAppValues, includedProperty)

				case TEAM_PROPERTY_NAME:

					var teamVals = strings.Split(includedProperty, ",")

					teamName := teamVals[0]

					for _, teamVal := range teamVals {

						teamVal = strings.ToLower(teamVal)
						if len(teamVal) <= 4 {
							teamVal = `\b` + teamVal + `\b`
						}
						teamVal = strings.ReplaceAll(teamVal, " ", `[-_\s]*`)
						teamVal = strings.ReplaceAll(teamVal, "-", `[-_\s]*`)
						teamVal = strings.ReplaceAll(teamVal, "_", `[-_\s]*`)

						teamValues[teamName] = append(teamValues[teamName], teamVal)
					}

					defaultTeamValues = append(defaultTeamValues, includedProperty)
				}
			}
		}
	}

	logger.Print(logger.INFO, "Global App Context for tenant", []string{resourceContext.TenantID}, defaultAppValues)
	logger.Print(logger.INFO, "Global Team Context for tenant", []string{resourceContext.TenantID}, defaultTeamValues)
}

func SetGlobalOrgContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Setting Global App Context for tenant", []string{resourceContext.TenantID}, defaultAppValues)
	logger.Print(logger.INFO, "Setting Global Team Context for tenant", []string{resourceContext.TenantID}, defaultTeamValues)

	docID := common.GenerateCombinedHashID(ORG_CONTEXT_TYPE, resourceContext.TenantID)

	orgContext, err := elastic.GetDocument(elastic.CUST_ENTITY_CONTEXT_INDEX, docID)
	if err != nil {
		return
	}

	if len(orgContext) > 0 {

		orgContextJson, err := json.Marshal(orgContext)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			return
		}

		var orgEntityContext common.CustomerEntityContextDoc

		if err = json.Unmarshal(orgContextJson, &orgEntityContext); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		if orgEntityContext.ContextProperties == nil {
			orgEntityContext.ContextProperties = []common.EntityContextProperties{
				{
					PropertyName: APP_PROPERTY_NAME,
					Include:      defaultAppValues,
				},
				{
					PropertyName: TEAM_PROPERTY_NAME,
					Include:      defaultTeamValues,
				},
			}
		} else {
			for i, contextProperty := range orgEntityContext.ContextProperties {

				switch contextProperty.PropertyName {
				case APP_PROPERTY_NAME:
					tmp := orgEntityContext.ContextProperties[i]
					tmp.Include = append(defaultAppValues, []string{}...)
					orgEntityContext.ContextProperties[i] = tmp
				case TEAM_PROPERTY_NAME:
					tmp := orgEntityContext.ContextProperties[i]
					tmp.Include = append(defaultTeamValues, []string{}...)
					orgEntityContext.ContextProperties[i] = tmp
				}
			}
		}

		if len(orgEntityContext.InsertTime) <= 0 {
			orgEntityContext.InsertTime = elastic.DateTime(time.Now().UTC())
		}
		orgEntityContext.UpdateTime = elastic.DateTime(time.Now().UTC())

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			return
		}

	} else {

		orgEntityContext := common.CustomerEntityContextDoc{
			InsertTime: elastic.DateTime(time.Now().UTC()),
			TenantID:   resourceContext.TenantID,
			UpdateTime: elastic.DateTime(time.Now().UTC()),
			ID:         docID,
			Type:       ORG_CONTEXT_TYPE,
			ContextProperties: []common.EntityContextProperties{
				{
					PropertyName: APP_PROPERTY_NAME,
					Include:      defaultAppValues,
				},
				{
					PropertyName: TEAM_PROPERTY_NAME,
					Include:      defaultTeamValues,
				},
			},
		}

		if _, err = elastic.InsertDocument(resourceContext.TenantID, elastic.CUST_ENTITY_CONTEXT_INDEX, orgEntityContext, docID); err != nil {
			return
		}

	}
}
