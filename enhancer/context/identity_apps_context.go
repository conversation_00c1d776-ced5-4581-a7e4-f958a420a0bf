package context

import (
	"encoding/json"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetIdentityAppsContext(resourceContext *ResourceContext) {

	identityApps := make(map[string][]string)

	err := getTerraformIdentities(resourceContext, identityApps)
	if err != nil {
		return
	}

	for identity, apps := range identityApps {
		resourceContext.SetIdentityApps(identity, apps)
	}
}

type AppIdentityAgg struct {
	ByUser AggsByUserComposite `json:"byUser"`
}

type AggsByUserComposite struct {
	AfterKey struct {
		Username string `json:"username"`
	} `json:"after_key"`

	Buckets []struct {
		Key struct {
			Username string `json:"username"`
		} `json:"key"`
	} `json:"buckets"`
}

func getTerraformIdentities(resourceContext *ResourceContext, identityApps map[string][]string) error {

	var afterKey string

	for {

		aggregatedEventsQuery := `{"size":0,"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"isUpdateEvent":"true"}},{"match":{"serviceCode":"` + common.IdStrToCspStrMap[resourceContext.ServiceID] + `"}},{"term":{"sourceApp.keyword":"` + TERRAFORM_USER_AGENT + `"}}]}},"aggs":{	"byUser":{"composite":{"size":1000,"sources":[{"username":{"terms":{"field":"username.keyword"}}}]}}}}`

		if len(afterKey) > 0 {
			aggregatedEventsQuery = `{"size":0,"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"isUpdateEvent":"true"}},{"match":{"serviceCode":"` + common.IdStrToCspStrMap[resourceContext.ServiceID] + `"}},{"term":{"sourceApp.keyword":"` + TERRAFORM_USER_AGENT + `"}}]}},"aggs":{"byUser":{"composite":{"size":1000,"sources":[{"username":{"terms":{"field":"username.keyword"}}}],"after":{"username":"` + afterKey + `"}}}}}`
		}

		eventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_ACTIVITY_INDEX}, aggregatedEventsQuery)
		if err != nil {
			return err
		}

		eventsAggBytes, err := json.Marshal(eventsAggregation)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{resourceContext.TenantID}, err)
			return err
		}

		var appIdentityAgg AppIdentityAgg
		if err = json.Unmarshal(eventsAggBytes, &appIdentityAgg); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
			return err
		}

		if len(appIdentityAgg.ByUser.Buckets) > 0 {
			afterKey = appIdentityAgg.ByUser.AfterKey.Username
			for _, userBucket := range appIdentityAgg.ByUser.Buckets {
				identityApps[userBucket.Key.Username] = append(identityApps[userBucket.Key.Username], TERRAFORM_APP)
			}
		} else {
			break
		}
	}

	return nil
}
