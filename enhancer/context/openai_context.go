package context

import (
	"encoding/json"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetOpenAIOrgContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Processing started for openai org context", []string{resourceContext.TenantID})

	var (
		orgSearchAfter    any
		orgResourcesQuery = `{"_source":["entityId","entityJson","tags"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_ORG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.OPENAI_SERVICE_ID + `}}]}}}`
	)

	for {

		orgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgResourcesQuery, orgSearchAfter)
		if err != nil {
			return
		}

		if len(orgResourcesDocs) > 0 {
			orgSearchAfter = sortResponse
		} else {
			break
		}

		for orgResourcesDocID, orgResourcesDoc := range orgResourcesDocs {

			var resourceContextInsertDoc common.ResourceContextInsertDoc

			if orgID, ok := orgResourcesDoc["entityId"].(string); ok {

				contextDocID := common.GenerateCombinedHashID(orgID, common.OPENAI_ORG_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         orgID,
					ResourceType:       common.OPENAI_ORG_RESOURCE_TYPE,
					Account:            orgID,
					TenantID:           resourceContext.TenantID,
					ServiceID:          common.OPENAI_SERVICE_ID_INT,
					CloudResourceDocID: orgResourcesDocID,
				}

				getTagContextOfResource(resourceContext, orgResourcesDoc, &resourceContextInsertDoc)

				var (
					orgUsersQuery   = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_USER_RESOURCE_TYPE + `"}},{"term":{"accountId.keyword":"` + orgID + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.OPENAI_SERVICE_ID + `}}]}}}`
					orgOwners       []string
					userSearchAfter any
				)

				for {

					orgUsersDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgUsersQuery, userSearchAfter)
					if err != nil {
						return
					}

					if len(orgUsersDocs) > 0 {
						userSearchAfter = sortResponse
					} else {
						break
					}

					for _, orgUsersDoc := range orgUsersDocs {
						if entityJSON, ok := orgUsersDoc["entityJson"].(string); ok {

							entityJSONMap := make(map[string]any)
							if err = json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
								continue
							}

							if orgRole, ok := entityJSONMap["role"].(string); ok && orgRole == "owner" {
								if userEmail, ok := entityJSONMap["email"].(string); ok && len(userEmail) > 0 {
									orgOwners = append(orgOwners, userEmail)
								} else if username, ok := entityJSONMap["name"].(string); ok && len(username) > 0 {
									orgOwners = append(orgOwners, username)
								}
							}
						}
					}
				}

				for _, orgOwner := range orgOwners {
					resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
						resourceContext.GetUserContextItem(orgOwner, common.OPENAIORG_OWNER_USER_TYPE, "", "", nil),
					)
				}

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
			}
		}
	}
}

func GetOpenAIProjectContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Processing started for openai project context", []string{resourceContext.TenantID})

	var (
		projectSearchAfter    any
		projectResourcesQuery = `{"_source":["entityId","entityJson","tags","accountId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_PROJECT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.OPENAI_SERVICE_ID + `}}]}}}`
	)

	for {

		projectResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, projectResourcesQuery, projectSearchAfter)
		if err != nil {
			return
		}

		if len(projectResourcesDocs) > 0 {
			projectSearchAfter = sortResponse
		} else {
			break
		}

		for projectResourcesDocID, projectResourcesDoc := range projectResourcesDocs {

			var resourceContextInsertDoc common.ResourceContextInsertDoc

			if projectID, ok := projectResourcesDoc["entityId"].(string); ok {

				if orgID, ok := projectResourcesDoc["accountId"].(string); ok {

					contextDocID := common.GenerateCombinedHashID(projectID, common.OPENAI_PROJECT_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

					resourceContextInsertDoc = common.ResourceContextInsertDoc{
						ResourceID:         projectID,
						ResourceType:       common.OPENAI_PROJECT_RESOURCE_TYPE,
						Account:            orgID,
						TenantID:           resourceContext.TenantID,
						Region:             "Global",
						ServiceID:          common.OPENAI_SERVICE_ID_INT,
						CloudResourceDocID: projectResourcesDocID,
					}

					getTagContextOfResource(resourceContext, projectResourcesDoc, &resourceContextInsertDoc)

					orgIDDocID := common.GenerateCombinedHashID(orgID, common.OPENAI_ORG_RESOURCE_TYPE, orgID, resourceContext.TenantID)

					// assign openAI org as related resource
					assignRelatedResource(resourceContext, projectID, contextDocID, resourceContextInsertDoc.ResourceType,
						orgID, orgIDDocID, common.OPENAI_ORG_RESOURCE_TYPE, false)

					if entityJSON, ok := projectResourcesDoc["entityJson"].(string); ok {

						entityJSONMap := make(map[string]any)

						if err = json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
							continue
						}

						if projectName, ok := entityJSONMap["name"].(string); ok && len(projectName) > 0 {

							resourceContextInsertDoc.ResourceName = projectName
							if envName := GetEnvironmentNameFromValue(projectName); len(envName) > 0 {
								resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
									common.ResourceContextItem{
										Name: envName,
										Type: common.OPENAIPROJECT_NAME_ENV_TYPE,
									},
								)
							}
						}
					}

					var (
						projectUsersQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.OPENAI_USER_RESOURCE_TYPE + `"}},{"term":{"accountId.keyword":"` + projectID + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.OPENAI_SERVICE_ID + `}}]}}}`
						projectOwners     []string
						userSearchAfter   any
					)

					for {

						projectUsersDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, projectUsersQuery, userSearchAfter)
						if err != nil {
							return
						}

						if len(projectUsersDocs) > 0 {
							userSearchAfter = sortResponse
						} else {
							break
						}

						for _, projectUsersDoc := range projectUsersDocs {
							if entityJSON, ok := projectUsersDoc["entityJson"].(string); ok {

								entityJSONMap := make(map[string]any)
								if err = json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
									continue
								}

								if projectRole, ok := entityJSONMap["role"].(string); ok && projectRole == "owner" {
									if userEmail, ok := entityJSONMap["email"].(string); ok && len(userEmail) > 0 {
										projectOwners = append(projectOwners, userEmail)
									} else if username, ok := entityJSONMap["name"].(string); ok && len(username) > 0 {
										projectOwners = append(projectOwners, username)
									}
								}
							}
						}
					}

					for _, projectOwner := range projectOwners {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							common.ResourceContextItem{
								Name: projectOwner,
								Type: common.OPENAIPROJECT_OWNER_USER_TYPE,
							},
						)
					}

					orgContextID := common.GenerateCombinedHashID(orgID, common.OPENAI_ORG_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)
					if parentResourceContextInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {
						resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(parentResourceContextInsertDoc, common.OPENAI_ORG_RESOURCE_TYPE)
						resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(parentResourceContextInsertDoc)

					}

					resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
				}
			}
		}
	}

	logger.Print(logger.INFO, "Processing complete for openai project context", []string{resourceContext.TenantID})
}
