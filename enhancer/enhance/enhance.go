package enhance

import (
	"encoding/json"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const (
	MAX_THREADS                   = 10
	IDENTITY_INSERTION_MAXRECORDS = 1000
)

func StartContextProcessing(tenantID, lastCollectedAt, serviceID string, debugMode bool) {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("enhancer")
		}

		logger.LogEmailProcessor("", true)
	}()

	if len(serviceID) <= 0 {
		serviceID = common.GetServiceID(tenantID, lastCollectedAt)
		if len(serviceID) <= 0 {
			logger.Print(logger.INFO, "Invalid collectedAt at or tenantId", tenantID, lastCollectedAt)
			return
		}
	}

	logger.Print(logger.INFO, "Processing context data for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	tenantData, err := tenant.GetTenantData(tenantID, false)
	if err != nil {
		return
	}

	startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID, tenantData, debugMode)
}

func startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID string, tenantData tenant.TenantData, debugMode bool) {

	var (
		resourceContext = context.NewResourceContext(tenantID, lastCollectedAt, serviceID, tenantData)
		currentTime     = time.Now().UTC()
		// All insertions for enhancer should happen here and must check for persistEnhancerData flag
		persistEnhancerData = !debugMode
	)

	resourceContext.GetUsers()

	if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
		resourceContext.GetOktaContext()
	}

	if enabled, ok := resourceContext.GetEnabledService("jira"); ok && enabled {
		resourceContext.GetJiraContext()
	}

	switch serviceID {

	case common.AWS_SERVICE_ID:
		resourceContext.GetAWSOrgContext()
		resourceContext.GetOrgUnitContext()
		resourceContext.GetAccountContext()
		resourceContext.GetAWSUserAndRoleContext()
		resourceContext.GetSecurityGroupRules()

	case common.AZURE_SERVICE_ID:
		resourceContext.GetTenantContext()
		resourceContext.GetManagementGroupContext()
		resourceContext.GetSubscriptionContext()
		resourceContext.GetApplicationContext()
		resourceContext.GetRoleAssignments()
		resourceContext.GetResourceGroupContext()
		resourceContext.GetNetworkSecurityGroupRules()

	case common.GCP_SERVICE_ID:
		resourceContext.GetRoles()
		resourceContext.GetGCPOrgContext()
		resourceContext.GetFolderContext()
		resourceContext.GetProjectContext()
		resourceContext.GetServiceAccountKeyContext()
		resourceContext.GetServiceAccountContext()
		resourceContext.GetSAPolicyBindingContext()
		resourceContext.GetFirewallRules()

	case common.OPENAI_SERVICE_ID:
		resourceContext.GetOpenAIOrgContext()
		resourceContext.GetOpenAIProjectContext()
	}

	resourceContext.GetIdentityAppsContext()
	resourceContext.GetResourceContext()

	nameList := make(map[string][]string)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource context.UserContext) bool {

		if len(userResource.Email) > 0 {
			aiInput := common.GetEmailNameWithoutSpecialCharacters(userResource.Email)
			if userResource.Name != "" {
				aiInput = userResource.Name
			}

			// Set name defined in exceptions index
			if name, ok := resourceContext.GetOwnerEmailName(userResource.Email); ok && len(name) > 0 {
				userResource.Name = name
				aiInput = name
			}

			// if !userResource.CloudUser {
			// 	delete(resourceContext.UserResources, email)
			// }

			context.HandleChildEmail(resourceContext, &userResource)

			// child email should have the same name as primary email if primary email has a name defined (from customer)
			if primaryEmail, ok := resourceContext.GetChildPrimaryEmail(usrRscKey); ok {
				if name, ok := resourceContext.GetOwnerEmailName(primaryEmail); ok && len(name) > 0 {
					userResource.Name = name
					aiInput = name
				}
			}

			if isAlias, ok := resourceContext.GetAliasUpdate(userResource.Email); ok {
				userResource.IsUser = !isAlias
				aiInput = ""
			} else if primaryEmail, ok := resourceContext.GetChildPrimaryEmail(usrRscKey); ok {
				// If customer has defined the identity status (alias) of parent email use that for child email as well
				if isAlias, ok := resourceContext.GetAliasUpdate(primaryEmail); ok {
					userResource.IsUser = !isAlias
					aiInput = ""
				}
			}

			if len(aiInput) > 0 {
				nameList[aiInput] = append(nameList[aiInput], usrRscKey)
			}

			resourceContext.SetUserResource(usrRscKey, userResource)
		}

		return true
	})

	if len(nameList) > 0 {

		exampleNames := map[string]bool{
			"John Doe":    true,
			"Engineering": false,
			"Satyam":      true,
			"DevOps":      false,
		}

		// fetch samples
		context.PopulateExampleNames(tenantID, exampleNames)
		resp := common.HumanOrNonHumanName(nameList, exampleNames, tenantID)

		for input, hasName := range resp {
			if usrRscKeys, ok := nameList[input]; ok {
				for _, usrRscKey := range usrRscKeys {
					if tmp, ok := resourceContext.GetUserResource(usrRscKey); ok {

						if hasName {
							tmp.IsUser = true
						} else {
							if ok := resourceContext.GetInvalidEmailCache(tmp.Email); ok || !tmp.Active {
								if ok := resourceContext.GetUpdateUndeliverableValidEmail(tmp.Email); ok {
									resourceContext.DeleteUpdateUndeliverableValidEmail(tmp.Email)
								}

								tmp.IsInvalid = true
							}
						}

						// If customer has defined the identity status (alias), use that
						if isAlias, ok := resourceContext.GetAliasUpdate(tmp.Email); ok {
							tmp.IsUser, hasName = !isAlias, !isAlias
						}

						resourceContext.SetUserResource(usrRscKey, tmp)
						resourceContext.SetAliasUpdate(tmp.Email, !hasName)

					}
				}
			}
		}
		nameList = make(map[string][]string)
	}

	var (
		uniqueOwners       sync.Map
		uniqueEnv          sync.Map
		uniqueApp          sync.Map
		uniqueSoftware     sync.Map
		uniqueDeployment   sync.Map
		uniqueCompliance   sync.Map
		uniqueSensitivity  sync.Map
		uniqueCostCenter   sync.Map
		uniqueTeam         sync.Map
		accountSensitivity sync.Map
		accountCompliance  sync.Map
		uniqueUsrAgent     sync.Map
	)

	logger.Print(logger.INFO, "User list for post processing", []string{tenantID}, lastCollectedAt)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource context.UserContext) bool {
		logger.Print(logger.INFO, usrRscKey, userResource)
		return true
	})

	logger.Print(logger.INFO, "Starting post process", []string{tenantID}, serviceID, lastCollectedAt)

	var wg sync.WaitGroup
	semStage1 := make(chan struct{}, MAX_THREADS)

	logger.Print(logger.INFO, "Starting post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {
		semStage1 <- struct{}{}
		wg.Add(1)

		go func(docID string, rContextDoc *common.ResourceContextInsertDoc) {

			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", r, docID)
					email.SendPanicEmail("enhancer")
				}
				wg.Done()
				<-semStage1
			}()

			owners := resourceContext.PostProcessOwners(rContextDoc)
			uniqueOwners.Store(docID, owners)

			envContext := resourceContext.GetUniqueEnvContext(rContextDoc)
			uniqueEnv.Store(docID, envContext)

			appContext := context.GetUniqueAppContext(rContextDoc)
			uniqueApp.Store(docID, appContext)

			teamContext := context.GetUniqueTeamContext(rContextDoc)
			uniqueTeam.Store(docID, teamContext)

			softwareContext := context.GetUniqueSoftwareContext(rContextDoc)
			uniqueSoftware.Store(docID, softwareContext)

			deploymentContext := context.GetUniqueDeploymentContext(rContextDoc)
			uniqueDeployment.Store(docID, deploymentContext)

			complianceContext := context.GetUniqueComplianceContext(rContextDoc, &accountCompliance)
			uniqueCompliance.Store(docID, complianceContext)

			sensitivityContext := context.GetUniqueSensitivityContext(rContextDoc, &accountSensitivity)
			uniqueSensitivity.Store(docID, sensitivityContext)

			costCenterContext := context.GetUniqueCostCenterContext(rContextDoc)
			uniqueCostCenter.Store(docID, costCenterContext)

			usrAgentContext := context.GetUniqueUserAgentContext(rContextDoc)
			uniqueUsrAgent.Store(docID, usrAgentContext)

			resourceContext.PostProcessCommitContext(rContextDoc, docID)

			ownersInterface, _ := uniqueOwners.Load(docID)
			if owners, ok := ownersInterface.([]string); ok {
				for _, owner := range owners {
					// Only increment for resources where owner is present
					context.IncrementParentChildOwnerCount(resourceContext, owner, rContextDoc.Account)
				}
			}

			// Insert in map even if owner is absent
			context.IncrementResourceTypeOwnerCount(resourceContext, *rContextDoc, ownersInterface.([]string))

			resourceContext.SetResourceContextInsertDoc(docID, *rContextDoc)
		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage1)

	logger.Print(logger.INFO, "Completed post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {
		context.UpdateUndeliverableValidEmails(resourceContext)
	}

	logger.Print(logger.INFO, "Starting post process stage 2", []string{tenantID}, serviceID, lastCollectedAt)

	var (
		bulkResourceContextRequest, bulkCloudResourceRequest strings.Builder
		recordsCount                                         int
		maxRecords                                           = 10000
		identitiesMap                                        sync.Map
		bulkMutex                                            sync.Mutex
		identityCount                                        int32
		identityMapLock                                      sync.RWMutex
	)

	semStage2 := make(chan struct{}, MAX_THREADS)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {

		semStage2 <- struct{}{}
		wg.Add(1)
		go func(docID string, resourceContextDoc *common.ResourceContextInsertDoc) {

			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", r, docID)
					email.SendPanicEmail("enhancer")
				}

				wg.Done()
				<-semStage2
			}()

			var (
				enhancedUniqueOwners = make([]string, 0, 10)
				enhancedUniqueApps   = make([]string, 0, 5)
				enhancedUniqueTeams  = make([]string, 0, 5)
				relatedResources     []byte
				err                  error
				uniqueUsers          = make([]string, 0, 20)
			)

			if resourceContextDoc.SkipContext {

				owner := "Precize Support" + " <" + strings.ToLower("<EMAIL>") + ">"

				resourceContextDoc.ResourceOwnerTypes.DefinedOwners = append(
					resourceContextDoc.ResourceOwnerTypes.DefinedOwners,
					common.ResourceContextItem{
						Name:       owner,
						Type:       common.PRECIZE_DETECTED_USER_TYPE,
						Desc:       context.GetStaticDescriptionOfUserType(common.PRECIZE_DETECTED_USER_TYPE),
						IdentityId: "<EMAIL>",
					},
				)

				resourceContextDoc.LastCollectedAt = lastCollectedAt
				resourceContextDoc.UpdatedTime = elastic.DateTime(currentTime)
				resourceContextDoc.ID = docID

				resourceContext.SetResourceContextInsertDoc(docID, *resourceContextDoc)

				resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					return
				}

				bulkMutex.Lock()
				bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
				bulkResourceContextRequest.WriteString("\n")
				bulkResourceContextRequest.Write(resourceContextInsertDoc)
				bulkResourceContextRequest.WriteString("\n")
				bulkMutex.Unlock()

				recordsCount++
				return
			}

			// Get unique owners, apps and teams from sync.Map
			if ownersVal, ok := uniqueOwners.Load(docID); ok {
				enhancedUniqueOwners = append(enhancedUniqueOwners, ownersVal.([]string)...)
			}

			if appsVal, ok := uniqueApp.Load(docID); ok {
				enhancedUniqueApps = append(enhancedUniqueApps, appsVal.([]string)...)
			}

			if teamsVal, ok := uniqueTeam.Load(docID); ok {
				enhancedUniqueTeams = append(enhancedUniqueTeams, teamsVal.([]string)...)
			}

			resourceContext.PostProcessServiceIdentities(resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			context.PostProcessRelatedResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			context.PostProcessMaxRelatedOwner(docID, resourceContext, resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			context.PostProcessSimilarResourceNames(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			context.PostProcessSameAppResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueTeams)

			context.PostProcessSameTagResources(docID, resourceContext, resourceContextDoc, &uniqueOwners, &uniqueApp, &uniqueTeam, &enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams)

			context.PostProcessResourceTypeOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners)

			context.PostProcessSmallAccounts(resourceContext, resourceContextDoc, &uniqueOwners, &enhancedUniqueOwners)

			context.PostProcessParentComplianceAndSensitivity(docID, resourceContextDoc, &uniqueSensitivity, &uniqueCompliance, &accountSensitivity, &accountCompliance)

			context.PostProcessTeamsOfOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners, &enhancedUniqueTeams)

			// Remove excluded context from the resource - Doing it here because we have final list here
			var uniqueMap = map[string][]string{}

			// Load values from sync.Maps
			if envVal, ok := uniqueEnv.Load(docID); ok {
				uniqueMap[context.ENVIRONMENT_PROPERTY_NAME] = envVal.([]string)
			} else {
				uniqueMap[context.ENVIRONMENT_PROPERTY_NAME] = []string{}
			}

			if softwareVal, ok := uniqueSoftware.Load(docID); ok {
				uniqueMap[context.SOFTWARE_PROPERTY_NAME] = softwareVal.([]string)
			} else {
				uniqueMap[context.SOFTWARE_PROPERTY_NAME] = []string{}
			}

			if sensVal, ok := uniqueSensitivity.Load(docID); ok {
				uniqueMap[context.SENSITIVITY_PROPERTY_NAME] = sensVal.([]string)
			} else {
				uniqueMap[context.SENSITIVITY_PROPERTY_NAME] = []string{}
			}

			if compVal, ok := uniqueCompliance.Load(docID); ok {
				uniqueMap[context.COMPLIANCE_PROPERTY_NAME] = compVal.([]string)
			} else {
				uniqueMap[context.COMPLIANCE_PROPERTY_NAME] = []string{}
			}

			if depVal, ok := uniqueDeployment.Load(docID); ok {
				uniqueMap[context.DEPLOYMENT_PROPERTY_NAME] = depVal.([]string)
			} else {
				uniqueMap[context.DEPLOYMENT_PROPERTY_NAME] = []string{}
			}

			if ccVal, ok := uniqueCostCenter.Load(docID); ok {
				uniqueMap[context.COSTCENTER_PROPERTY_NAME] = ccVal.([]string)
			} else {
				uniqueMap[context.COSTCENTER_PROPERTY_NAME] = []string{}
			}

			if uaVal, ok := uniqueUsrAgent.Load(docID); ok {
				uniqueMap[context.USER_AGENT_PROPERTY_NAME] = uaVal.([]string)
			} else {
				uniqueMap[context.USER_AGENT_PROPERTY_NAME] = []string{}
			}

			uniqueMap[context.OWNER_PROPERTY_NAME] = enhancedUniqueOwners
			uniqueMap[context.TEAM_PROPERTY_NAME] = enhancedUniqueTeams
			uniqueMap[context.APP_PROPERTY_NAME] = enhancedUniqueApps

			resourceContext.RemoveCustomerEntityExcludeContextOfResource(resourceContextDoc, uniqueMap)

			// Store updated values back to sync.Maps
			enhancedUniqueOwners = uniqueMap[context.OWNER_PROPERTY_NAME]
			uniqueEnv.Store(docID, uniqueMap[context.ENVIRONMENT_PROPERTY_NAME])
			enhancedUniqueApps = uniqueMap[context.APP_PROPERTY_NAME]
			enhancedUniqueTeams = uniqueMap[context.TEAM_PROPERTY_NAME]
			uniqueSoftware.Store(docID, uniqueMap[context.SOFTWARE_PROPERTY_NAME])
			uniqueSensitivity.Store(docID, uniqueMap[context.SENSITIVITY_PROPERTY_NAME])
			uniqueCompliance.Store(docID, uniqueMap[context.COMPLIANCE_PROPERTY_NAME])
			uniqueDeployment.Store(docID, uniqueMap[context.DEPLOYMENT_PROPERTY_NAME])
			uniqueCostCenter.Store(docID, uniqueMap[context.COSTCENTER_PROPERTY_NAME])
			uniqueUsrAgent.Store(docID, uniqueMap[context.USER_AGENT_PROPERTY_NAME])

			// Set default values if empty
			if len(enhancedUniqueOwners) <= 0 {
				enhancedUniqueOwners = []string{"NONE"}
			}

			// Get values from sync.Maps with defaults if empty
			envList := []string{"NONE"}
			if envVal, ok := uniqueEnv.Load(docID); ok && len(envVal.([]string)) > 0 {
				envList = envVal.([]string)
			}

			if len(enhancedUniqueApps) <= 0 {
				enhancedUniqueApps = []string{"NONE"}
			}

			if len(enhancedUniqueTeams) <= 0 {
				enhancedUniqueTeams = []string{"NONE"}
			}

			softwareList := []string{"NONE"}
			if softwareVal, ok := uniqueSoftware.Load(docID); ok && len(softwareVal.([]string)) > 0 {
				softwareList = softwareVal.([]string)
			}

			depList := []string{"NONE"}
			if depVal, ok := uniqueDeployment.Load(docID); ok && len(depVal.([]string)) > 0 {
				depList = depVal.([]string)
			}

			compList := []string{"NONE"}
			if compVal, ok := uniqueCompliance.Load(docID); ok && len(compVal.([]string)) > 0 {
				compList = compVal.([]string)
			}

			sensList := []string{"NONE"}
			if sensVal, ok := uniqueSensitivity.Load(docID); ok && len(sensVal.([]string)) > 0 {
				sensList = sensVal.([]string)
			}

			ccList := []string{"NONE"}
			if ccVal, ok := uniqueCostCenter.Load(docID); ok && len(ccVal.([]string)) > 0 {
				ccList = ccVal.([]string)
			}

			uaList := []string{"NONE"}
			if uaVal, ok := uniqueUsrAgent.Load(docID); ok && len(uaVal.([]string)) > 0 {
				uaList = uaVal.([]string)
			}

			relatedResourceList := make([]context.RelatedResource, 0)
			if rList, ok := resourceContext.GetRelatedResourceList(docID); ok {
				relatedResourceList = rList
			}

			relatedResources, err = json.Marshal(relatedResourceList)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			commitInfo, err := json.Marshal(resourceContextDoc.CommitInfo)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			resourceContextDoc.LastCollectedAt = lastCollectedAt
			resourceContextDoc.UpdatedTime = elastic.DateTime(currentTime)
			resourceContextDoc.ID = docID

			resourceContext.SetResourceContextInsertDoc(docID, *resourceContextDoc)

			if persistEnhancerData {
				processResourceContextForIdentityCreation(&identitiesMap, resourceContextDoc, docID, &uniqueUsers, resourceContext, &identityCount, &identityMapLock)
			}

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			cloudResourceUpdateMetadata := `{"update": {"_id": "` + resourceContextDoc.CloudResourceDocID + `"}}`

			var cloudResourceUpdateDoc strings.Builder
			cloudResourceUpdateDoc.WriteString(`{"doc":{"relatedResources":`)
			cloudResourceUpdateDoc.Write(relatedResources)
			cloudResourceUpdateDoc.WriteString(`, "owner":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueOwners, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "environment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(envList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "app":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueApps, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "software":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(softwareList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "deployment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(depList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "compliance":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(compList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "sensitivity":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(sensList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "costCenter":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ccList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "team":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(enhancedUniqueTeams, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "users":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uniqueUsers, `","`))
			cloudResourceUpdateDoc.WriteString(`"],  "stageCompleted":["dc","enhancer"],"userAgent":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uaList, `","`))
			cloudResourceUpdateDoc.WriteString(`"],"extContext": {"commitInfo": `)
			cloudResourceUpdateDoc.Write(commitInfo)
			cloudResourceUpdateDoc.WriteString(`}}}`)

			bulkMutex.Lock()
			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")

			bulkCloudResourceRequest.WriteString(cloudResourceUpdateMetadata)
			bulkCloudResourceRequest.WriteString("\n")
			bulkCloudResourceRequest.WriteString(cloudResourceUpdateDoc.String())
			bulkCloudResourceRequest.WriteString("\n")

			recordsCount++

			if recordsCount >= maxRecords {

				if persistEnhancerData {

					if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
						bulkMutex.Unlock()
						return
					}

					logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records	", []string{tenantID}, lastCollectedAt)

					if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
						bulkMutex.Unlock()
						return
					}

					logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)
				}

				recordsCount = 0
				bulkResourceContextRequest.Reset()
				bulkCloudResourceRequest.Reset()
			}

			bulkMutex.Unlock()

			uniqueMap = nil
			relatedResources = nil
			commitInfo = nil
			resourceContextInsertDoc = nil

		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage2)

	if recordsCount > 0 && persistEnhancerData {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID}, lastCollectedAt)
	}

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {

		if int(identityCount) > 0 {
			err := insertIdentites(&identitiesMap, resourceContext.TenantID)
			if err != nil {
				return
			}
		}

		err := processDeletedResourceContextDocs(elastic.DateTime(currentTime), resourceContext.TenantID, resourceContext.ServiceID)
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Starting identity primary email updating process")

		err = updatePrimaryEmailForNonEnhancerIdentities(resourceContext, elastic.DateTime(currentTime))
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Completed identity primary email updating process")

		logger.Print(logger.INFO, "Starting identity status updating process", []string{tenantID}, lastCollectedAt)

		err = updateIdentityType(resourceContext)
		if err != nil {
			return
		}

		context.SetGlobalOrgContext(resourceContext)

		logger.Print(logger.INFO, "Completed identity status updating process", []string{tenantID}, lastCollectedAt)
	}

	logger.Print(logger.INFO, "Completed post process stage 2", lastCollectedAt)

	if config.Environment == config.PROD_ENV {
		logger.Print(logger.INFO, "Sleeping 5 mins for data sync", []string{tenantID}, lastCollectedAt)
		time.Sleep(5 * time.Minute)
	}

	logger.Print(logger.INFO, "Enhancer completed for tenant", []string{tenantID}, lastCollectedAt)
}
