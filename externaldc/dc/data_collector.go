package dc

import (
	"github.com/precize/common"
	"github.com/precize/email"
	"github.com/precize/externaldc/dc/orca"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

func StartDataCollection(tenantID, lastCollectedAt, serviceID string) {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("externaldc")
		}

		logger.LogEmailProcessor("", true)
	}()

	if len(serviceID) <= 0 {
		serviceID = common.GetServiceID(tenantID, lastCollectedAt)
		if len(serviceID) <= 0 {
			logger.Print(logger.INFO, "Invalid collectedAt at or tenantId", tenantID, lastCollectedAt)
			return
		}
	}

	serviceID = common.IdStrToCspStrMap[serviceID]
	tenantData, err := tenant.GetTenantData(tenantID, false)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Started External Data Collector for tenant", []string{tenantID}, lastCollectedAt)

	for _, orcaEnv := range tenantData.OrcaAccounts.Environment {
		err = orca.OrcaDataCollector(tenantID, lastCollectedAt, serviceID, orcaEnv)
		if err != nil {
			return
		}
	}

	logger.Print(logger.INFO, "Completed External Data Collector for tenant", []string{tenantID}, lastCollectedAt)
}
