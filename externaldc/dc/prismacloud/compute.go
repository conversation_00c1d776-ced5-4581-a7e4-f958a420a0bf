package prisma

import (
	// "encoding/json"
	// "strings"
	// "time"

	// "github.com/precize/common"
	// "github.com/precize/elastic"
	// "github.com/precize/logger"
	// "github.com/precize/provider/prismacloud"
	"github.com/precize/provider/tenant"
	// "github.com/precize/transport"
)

func PrismaComputeDataCollector(tenantID, lastCollectedAt string, prismaCloudEnv tenant.PrismaCloudEnvironment) error {

	// Refer https://pan.dev/prisma-cloud/api

	return nil
}
