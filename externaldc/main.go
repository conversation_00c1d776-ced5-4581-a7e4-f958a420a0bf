package main

import (
	"flag"
	"os"

	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/externaldc/dc"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func main() {

	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		tenantID        = flag.String("tenant", "", "TenantId to run enhancer for")
		lastCollectedAt = flag.String("collected", "", "Last scan time for tenantId")
		serviceID       = flag.String("serviceId", "", "ServiceId of the service")
		debug           = flag.Bool("debug", false, "Debug mode")
	)

	flag.Parse()

	logger.InitializeLogs("externaldc", *debug)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	} else if len(*lastCollectedAt) <= 0 {
		logger.Print(logger.ERROR, "Last collected not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()

	dc.StartDataCollection(*tenantID, *lastCollectedAt, *serviceID)
	os.Exit(0)
}
