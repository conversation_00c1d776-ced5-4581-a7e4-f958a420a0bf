package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gocolly/colly"
	"github.com/robfig/cron"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

const (
	FILINGS_JSON_FILE      = "/app/fetch8k/filings.json"
	EMAILS_JSON_FILE       = "/app/fetch8k/emails.json"
	BOARD_CYBER_SEC_SOURCE = "Board CyberSec"
	openAIAPIURL           = "https://api.openai.com/v1/chat/completions"
	openAIAPIKey           = "***************************************************************************************************************************************************************************************"
)

type OpenAIRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
}

type FilingData struct {
	ID     string `json:"_id"`
	Source struct {
		Adsh         string   `json:"adsh"`
		Ciks         []string `json:"ciks"`
		DisplayNames []string `json:"display_names"`
		FileType     string   `json:"file_type"`
		FileDate     string   `json:"file_date"`
		Items        []string `json:"items"`
	} `json:"_source"`
	FilingSource string
}

type Response struct {
	Hits struct {
		Hits []FilingData `json:"hits"`
	} `json:"hits"`
}

type CyberSecBoardScrapper struct {
	CompanyName string
	UpdatedTime string
}

func main() {

	var wg sync.WaitGroup

	wg.Add(1)

	c := cron.New()
	c.AddFunc("0 */30 * * * *", searchAndNotify)
	c.Start()

	wg.Wait()
}

func searchAndNotify() {

	startDate := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	endDate := time.Now().Format("2006-01-02")

	log.Println("Searching for 8-K's filed since " + startDate)

	url := `https://efts.sec.gov/LATEST/search-index`
	urlParams := map[string]string{
		"q":         `"Material Cybersecurity Incidents"`,
		"dateRange": "custom",
		"category":  "custom",
		"startdt":   startDate,
		"enddt":     endDate,
		"forms":     "8-K,8-K12B,8-K12G3",
		"from":      "0",
	}
	var (
		headers      = map[string]string{"User-Agent": "<EMAIL>"}
		filingsData  []FilingData
		pageSize     = 100
		totalResults = 0
		page         = 1
	)

	for {
		resp, err := makeRequest(url, headers, urlParams)
		if err != nil {
			log.Println("Error while fetching material cybersecurity incidents - ", url, urlParams, err)
			return
		}

		var apiResp Response
		err = json.Unmarshal(resp, &apiResp)
		if err != nil {
			log.Println("Error Unmarshalling Response")
			return
		}

		filingsData = append(filingsData, apiResp.Hits.Hits...)
		totalResults += len(apiResp.Hits.Hits)

		if len(apiResp.Hits.Hits) < pageSize {
			break
		}

		page++
		nextFrom := strconv.Itoa(totalResults)
		urlParams["from"] = nextFrom
		urlParams["page"] = strconv.Itoa(page)

		if page >= 5 {
			time.Sleep(1 * time.Second)
		}
	}

	urlParams = map[string]string{
		"q":         `"Other Events"`,
		"dateRange": "custom",
		"category":  "custom",
		"startdt":   startDate,
		"enddt":     endDate,
		"forms":     "8-K,8-K12B,8-K12G3",
	}
	page = 1
	totalResults = 0

	for {
		resp, err := makeRequest(url, headers, urlParams)
		if err != nil {
			log.Println("Error while fetching other events - ", url, urlParams, err)
			return
		}

		var apiResp Response
		err = json.Unmarshal(resp, &apiResp)
		if err != nil {
			log.Println("Error Unmarshalling Response")
			return
		}

		filingsData = append(filingsData, apiResp.Hits.Hits...)
		totalResults += len(apiResp.Hits.Hits)

		if len(apiResp.Hits.Hits) < pageSize {
			break
		}

		page++
		nextFrom := strconv.Itoa(totalResults)
		urlParams["from"] = nextFrom
		urlParams["page"] = strconv.Itoa(page)

		if page >= 5 {
			time.Sleep(1 * time.Second)
		}
	}

	urlParams["q"] = `"Other Information"`
	page = 1
	totalResults = 0
	delete(urlParams, "page")
	delete(urlParams, "from")

	for {
		resp, err := makeRequest(url, headers, urlParams)
		if err != nil {
			log.Println("Error while fetching other information - ", urlParams, err)
			return
		}

		var apiResp Response
		err = json.Unmarshal(resp, &apiResp)
		if err != nil {
			log.Println("Error Unmarshalling Response")
			return
		}

		filingsData = append(filingsData, apiResp.Hits.Hits...)
		totalResults += len(apiResp.Hits.Hits)

		if len(apiResp.Hits.Hits) < pageSize {
			break
		}

		page++
		nextFrom := strconv.Itoa(totalResults)
		urlParams["from"] = nextFrom
		urlParams["page"] = strconv.Itoa(page)
	}

	for _, resp := range getLatestFilingCompanyFromCyberSecBoard() {
		page = 1
		totalResults = 0
		delete(urlParams, "q")
		delete(urlParams, "page")
		delete(urlParams, "from")
		urlParams["entityName"] = resp.CompanyName

		if len(resp.UpdatedTime) > 0 {
			urlParams["startdt"] = resp.UpdatedTime
		}

		for {
			resp, err := makeRequest(url, headers, urlParams)
			if err != nil {
				log.Println("Error while fetching other information - ", urlParams, err)
				return
			}

			var apiResp Response
			err = json.Unmarshal(resp, &apiResp)
			if err != nil {
				log.Println("Error Unmarshalling Response")
				return
			}

			for _, hit := range apiResp.Hits.Hits {
				hit.FilingSource = BOARD_CYBER_SEC_SOURCE
				filingsData = append(filingsData, hit)
			}

			totalResults += len(apiResp.Hits.Hits)

			if len(apiResp.Hits.Hits) < pageSize {
				break
			}

			page++
			nextFrom := strconv.Itoa(totalResults)
			urlParams["from"] = nextFrom
			urlParams["page"] = strconv.Itoa(page)
		}
	}

	var (
		externalEmailBody    string = `<!DOCTYPE html><html><head><meta charset="UTF-8"><b>New 8-K's filed</b><br /><br /></head><body>`
		internalEmailBody    string = `<!DOCTYPE html><html><head><meta charset="UTF-8"><b>New 8-K's filed</b><br /><br /></head><body>`
		subject              string = "\nPrecize report on 8-K filings"
		sendEmail            bool
		filingsMap           = make(map[string]string)
		internalFilingsFound = 0
		externalFilingsFound = 0
		filingsProcessed     = 0
		internalEmail        = false
	)

	if len(filingsData) > 0 {
		filingsJson, err := os.Open(FILINGS_JSON_FILE)
		if err != nil {
			log.Println("Got error opening filings.json: ", err)
			return
		}

		defer filingsJson.Close()

		filingBytes, err := ioutil.ReadAll(filingsJson)
		if err != nil {
			log.Println("Got error reading filings.json: ", err)
			return
		}

		if err = json.Unmarshal([]byte(filingBytes), &filingsMap); err != nil {
			log.Println("Got error unmarshalling json file: ", err)
			return
		}

		for _, v := range filingsData {

			filingBody := ""
			src := v.Source

			if _, ok := filingsMap[src.Adsh]; ok {
				log.Println("Already sent email for filing: " + src.Adsh + ". Skipping.")
				continue
			}

			if src.FileType != "8-K" && src.FileType != "8-K12B" && src.FileType != "8-K12G3" {
				log.Println("Not 8-K filetype: ", src.Adsh)
				continue
			}

			if !slices.Contains(src.Items, "1.05") && !slices.Contains(src.Items, "8.01") {
				log.Println("Not Cybersecurity Item: ", src.Adsh)
				continue
			}

			log.Println("Identified new filing: " + src.Adsh)
			sendEmail = false

			var (
				idSuffix string
				idSplit  = strings.Split(v.ID, ":")
			)

			if len(idSplit) > 1 {
				idSuffix = idSplit[len(idSplit)-1]
			}

			filingBody = filingBody + strings.Join(src.DisplayNames, "<br />") + `<br />` + src.FileType + `<br />` +
				src.FileDate

			for _, v2 := range src.Ciks {

				filingsProcessed++

				if filingsProcessed >= 10 {
					time.Sleep(1 * time.Second)
					filingsProcessed = 0
				}

				ciksWithoutZeroes, err := strconv.Atoi(v2)
				if err != nil {
					log.Println("Got error converting to int: ", err)
					return
				}

				if ciksWithoutZeroes > 0 {

					if strings.Contains(idSuffix, "htm") {
						link1 := `https://www.sec.gov/ix?doc=/Archives/edgar/data/` + strconv.Itoa(ciksWithoutZeroes) + `/` + strings.ReplaceAll(src.Adsh, "-", "") + `/` + idSuffix
						filingBody = filingBody + `<br />` + `<a href="` + link1 + `">` + link1 + `</a>`
					}

					link2 := `https://www.sec.gov/Archives/edgar/data/` + strconv.Itoa(ciksWithoutZeroes) + `/` + strings.ReplaceAll(src.Adsh, "-", "") + `/` + src.Adsh + `-index.html`
					filingBody = filingBody + `<br />` + `<a href="` + link2 + `">` + link2 + `</a>`

					if slices.Contains(src.Items, "1.05") {
						sendEmail = true
					} else if slices.Contains(src.Items, "8.01") {

						url := `https://www.sec.gov/Archives/edgar/data/` + strconv.Itoa(ciksWithoutZeroes) + `/` + strings.ReplaceAll(src.Adsh, "-", "") + `/` + idSuffix
						headers = map[string]string{"User-Agent": "<EMAIL>"}

						resp, err := makeRequest(url, headers, nil)
						if err != nil {
							log.Println("Error while getting file content - ", url, err)
							continue
						}

						fileContent := strings.ToLower(string(resp))

						// split fileContent by Item 8.01 Other Event/Information and fetch the second part
						itemRegex := regexp.MustCompile(`(?i)item.*?8\.01.*?(other\s+events|information)?`)
						parts := itemRegex.Split(fileContent, 2)
						if len(parts) < 2 {
							log.Println("Item 8.01 not found in the content.")
							continue
						}
						if len(parts) > 1 {
							fileContent = parts[1]
						}

						// split fileContent by Item 9.01 and fetch the first part
						itemRegex = regexp.MustCompile(`(?i)item.*?9\.01.*?`)
						parts = itemRegex.Split(fileContent, 2)
						if len(parts) >= 1 {
							fileContent = parts[0]
						}

						// split by looking statement and get the first part
						before, _, found := strings.Cut(fileContent, "looking statements")
						if found {
							fileContent = before
						}

						// split by forward looking statement and get the first part
						before, _, found = strings.Cut(fileContent, "forward looking")
						if found {
							fileContent = before
						}

						// split by forward-looking statement and get the first part
						before, _, found = strings.Cut(fileContent, "forward-looking")
						if found {
							fileContent = before
						}

						tagsFileContent := []string{}

						if strings.Contains(fileContent, "</p>") {
							tagsFileContent = strings.Split(fileContent, "</p>")
						} else if strings.Contains(fileContent, "</div>") {
							tagsFileContent = strings.Split(fileContent, "</div>")
						}

						trimmedfileContent := ""
						searchKeywords := []string{"unauthorized activity", "unauthorized activities", "unauthorized access", "unauthorized third-party access", "unauthorized party", "unauthorized occurrence", "cybersecurity incident", "cyber-attack", "cyberattack", "threat actor", "security incident", "ransomware", "ransomware incident", "ransomware attack", "cyber incident", "unauthorized third party", "material cybersecurity incidents", "unusual network activity"}
						excludeSearchKeywords := []string{"not a cyberattack", "not caused by a cyberattack", "no unauthorized activity", "no unauthorized access"}
						breakPoint := 4
						breakLoop := false

						for {

							if len(tagsFileContent) > 0 {
								if len(tagsFileContent) >= breakPoint {
									trimmedfileContent = strings.Join(tagsFileContent[:breakPoint], "")
								} else {
									trimmedfileContent = strings.Join(tagsFileContent, "")
								}
							}

							for _, searchKeyword := range searchKeywords {
								if strings.Contains(trimmedfileContent, searchKeyword) {
									sendEmail = true
									internalEmail = false
									break
								}
							}

							for _, excludeSearchKeyword := range excludeSearchKeywords {
								if strings.Contains(trimmedfileContent, excludeSearchKeyword) {
									sendEmail = false
									breakLoop = true
									break
								}
							}

							if sendEmail || breakLoop || breakPoint >= 5 {
								break
							}

							breakPoint++
						}

						if !sendEmail {
							for _, searchKeyword := range searchKeywords {
								if strings.Contains(fileContent, searchKeyword) {
									sendEmail = true
									internalEmail = true
									break
								}
							}

							for _, excludeSearchKeyword := range excludeSearchKeywords {
								if strings.Contains(fileContent, excludeSearchKeyword) {
									sendEmail = false
									break
								}
							}
						}

						if !sendEmail {
							// for 8k's identified from Board CyberSec trigger emails directly
							if v.FilingSource == BOARD_CYBER_SEC_SOURCE {
								sendEmail = true

								//trigger internal email for not identifying
								body := "8K not identified by Precize (fetched from board of cybersec): " + strings.Join(src.DisplayNames, "<br />")

								to := mail.NewEmail("Aniket", "<EMAIL>")
								ccEmails := []*mail.Email{mail.NewEmail("Abhay", "<EMAIL>")}
								triggerEmail(to, nil, ccEmails, subject, body, filingsMap)

							}
						}
					}
				}
			}

			filingBody = filingBody + `<br /><br />`

			if sendEmail {

				if internalEmail {
					internalEmailBody = internalEmailBody + filingBody
					internalFilingsFound++
				} else {
					externalEmailBody = externalEmailBody + filingBody
					externalFilingsFound++
				}

				filingsMap[src.Adsh] = strings.Join(src.DisplayNames, " ")
			}
		}
	}

	if externalFilingsFound <= 0 && internalFilingsFound <= 0 {
		log.Println("No new filings to be sent")
		return
	}

	if internalFilingsFound > 0 {
		internalEmailBody = internalEmailBody + `- Precize Inc (bot)</body></html>`
	} else if externalFilingsFound > 0 {
		externalEmailBody = externalEmailBody + `- Precize Inc (bot)</body></html>`
	}

	var (
		to   *mail.Email
		body string
	)
	if internalFilingsFound > 0 {
		log.Println("Sending Internal Email")
		var bccEmails []*mail.Email
		to = mail.NewEmail("Mathan", "<EMAIL>")

		bccEmail := mail.NewEmail("Aniket", "<EMAIL>")
		bccEmails = append(bccEmails, bccEmail)
		bccEmail = mail.NewEmail("Abhay", "<EMAIL>")
		bccEmails = append(bccEmails, bccEmail)

		body = internalEmailBody
		triggerEmail(to, bccEmails, nil, subject, body, filingsMap)
	}

	if externalFilingsFound > 0 {
		log.Println("Sending External Email")
		var bccEmails []*mail.Email
		to = mail.NewEmail("Vishwas", "<EMAIL>")

		emailListJson, err := os.Open(EMAILS_JSON_FILE)
		if err != nil {
			log.Println("Got error opening json file: ", err)
			return
		}

		defer emailListJson.Close()

		emailBytes, err := ioutil.ReadAll(emailListJson)
		if err != nil {
			log.Println("Got error reading emails.json: ", err)
			return
		}

		var bccEmailsMap = make(map[string]string)
		if err = json.Unmarshal([]byte(emailBytes), &bccEmailsMap); err != nil {
			log.Println("Got error unmarshalling json file: ", err)
			return
		}

		for name, email := range bccEmailsMap {
			bccEmail := mail.NewEmail(name, email)
			bccEmails = append(bccEmails, bccEmail)
		}

		body = externalEmailBody
		triggerEmail(to, bccEmails, nil, subject, body, filingsMap)
	}
}

func makeRequest(url string, headers, params map[string]string) ([]byte, error) {
	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("creating new request: %w", err)
	}

	for headerKey, headerValue := range headers {
		req.Header.Set(headerKey, headerValue)
	}

	q := req.URL.Query()
	for k, v := range params {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("sending request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("status code: %d", resp.StatusCode)
	}

	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("reading response: %w", err)
	}

	return b, nil
}

func triggerEmail(to *mail.Email, bccEmails []*mail.Email, ccEmails []*mail.Email, subject, body string, filingsMap map[string]string) {
	m := mail.NewV3Mail()

	from := mail.NewEmail("Precize Notifications", "<EMAIL>")
	content := mail.NewContent("text/html", body)

	m.SetFrom(from)
	m.AddContent(content)

	personalization := mail.NewPersonalization()
	personalization.AddTos(to)
	personalization.AddBCCs(bccEmails...)
	personalization.AddCCs(ccEmails...)

	personalization.Subject = subject

	m.AddPersonalizations(personalization)

	request := sendgrid.GetRequest("*********************************************************************", "/v3/mail/send", "https://api.sendgrid.com")
	request.Method = "POST"
	request.Body = mail.GetRequestBody(m)
	response, err := sendgrid.API(request)
	if err != nil || response.StatusCode < 200 || response.StatusCode >= 300 {
		log.Println("Email sending failed: ", err, response.StatusCode, response.Body, response.Headers)
		return
	} else {

		filingsMapJson, err := json.Marshal(filingsMap)
		if err != nil {
			log.Println("Got error marshalling: ", err)
			return
		}

		if err = os.WriteFile(FILINGS_JSON_FILE, filingsMapJson, 0664); err != nil {
			log.Println("Got error writing to filings.json: ", err)
			return
		}

		log.Println("Completed with status: ", response.StatusCode)
	}
}

func getLatestFilingCompanyFromCyberSecBoard() []CyberSecBoardScrapper {
	c := colly.NewCollector()

	url := "https://www.board-cybersecurity.com/incidents/tracker/"
	scrappedRes := make([]CyberSecBoardScrapper, 0)

	c.OnHTML(".table-container tbody", func(e *colly.HTMLElement) {
		e.ForEachWithBreak("tr", func(i int, el *colly.HTMLElement) bool {
			// get first 5 filings
			if i <= 4 {
				companyName := el.ChildText("td:nth-child(3)")
				dateStr := el.ChildText("td:nth-child(2)")
				if t, err := time.Parse("2006-01-02", dateStr); err == nil {
					if t.After(time.Now().AddDate(0, 0, -7)) {
						lastUpdatedTime := t.Format("2006-01-02")
						scrappedRes = append(scrappedRes, CyberSecBoardScrapper{
							CompanyName: companyName,
							UpdatedTime: lastUpdatedTime,
						})
					}
				}

			} else {
				return false
			}
			return true
		})
	})
	c.Visit(url)
	return scrappedRes
}

// Not being used
func is8KCyberSecurityIncident(content string) (bool, error) {
	log.Println("OpenAI Request", content)
	reqBody, err := json.Marshal(OpenAIRequest{
		Model: "gpt-4o-mini",
		Messages: []Message{
			{
				Role:    "system",
				Content: "You are a cybersecurity expert. Respond with ONLY 'Yes' or 'No' to the following question.",
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("Based on cybersecurity reporting requirements for 8K filings, does the following text describe a reportable cybersecurity incident? Respond with ONLY 'Yes' or 'No':\n\n%s", content),
			},
		},
	})
	if err != nil {
		return false, fmt.Errorf("error creating request body: %v", err)
	}

	req, err := http.NewRequest("POST", openAIAPIURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return false, fmt.Errorf("error creating HTTP request: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", openAIAPIKey))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("error reading response body: %v", err)
	}

	var apiResp OpenAIResponse
	err = json.Unmarshal(body, &apiResp)
	if err != nil {
		return false, fmt.Errorf("error parsing response: %v", err)
	}

	log.Println("OpenAI Response", apiResp)

	if len(apiResp.Choices) > 0 {
		responseContent := strings.TrimSpace(strings.ToLower(apiResp.Choices[0].Message.Content))

		switch responseContent {
		case "yes":
			return true, nil
		case "no":
			return false, nil
		default:
			return false, fmt.Errorf("unexpected response from GPT: %s", responseContent)
		}
	}

	return false, fmt.Errorf("no response from OpenAI API")
}
