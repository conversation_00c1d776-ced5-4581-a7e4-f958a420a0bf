package main

import (
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func DeleteTextLookupByCategory(category, tenantID string) (err error) {

	logger.Print(logger.INFO, "Deleting text lookup for category", category, []string{tenantID})

	query := `{"query":{"bool":{"must":[{"term":{"category.keyword":"` + category + `"}}]}}}`

	err = elastic.DeleteByQuery(elastic.TEXT_LOOKUP_INDEX, query)
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Deleted text lookup for category", category, []string{tenantID})
	return
}
