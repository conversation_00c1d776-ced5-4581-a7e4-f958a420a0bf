package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 30/05/2025
// Target - Text Lookup
// This migration is to delete text lookup documents for a specific category for limited records
// Note: If the data is large do not use this, delete in batches

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		tenantID      = flag.String("tenant", "", "TenantId to run migration for")
		category      = flag.String("category", "", "Category of text lookup to delete")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	} else if len(*category) <= 0 {
		logger.Print(logger.ERROR, "Category not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	gracefullyShutDown()

	DeleteTextLookupByCategory(*category, *tenantID)
	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
