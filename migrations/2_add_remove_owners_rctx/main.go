package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 05/06/2025
// Target - Resource Context
// This migration is to add/remove owners to/from resource context for previous collectedAt's due to improved owner derivation logic/ false positive owner derivations

func main() {
	var (
		appConfigPath  = flag.String("config", "application.yml", "Path to application.yml")
		tenantID       = flag.String("tenant", "", "TenantId to run migration for")
		ownerName      = flag.String("name", "", "Name of the owner to add/remove")
		ownerType      = flag.String("type", "", "Type of the owner to definedOwners, derivedOwners, inheritedOwners, codeOwners, costOwners, securityOwners, opsOwners")
		ownerDesc      = flag.String("desc", "", "Description of the owner to add/remove")
		ownerTypeDBKey = flag.String("ownerTypeDBkey", "", "DB key of the owner to add/remove")
		identityID     = flag.String("identityID", "", "Identity ID of the owner to add/remove")
		collectedAts   = flag.String("collectedAts", "", "Comma separated list of collectedAts to run migration for")
		resourcesIds   = flag.String("resourcesIds", "", "Comma separated list of resourceIds to run migration for")
		modifyCR       = flag.Bool("modifyCR", true, "Whether to modify cloud resource as well")
		ops            = flag.String("ops", "", "Operations to perform. add/remove")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	} else if len(*ownerName) <= 0 || len(*ownerType) <= 0 || len(*ownerDesc) <= 0 || len(*ownerTypeDBKey) <= 0 || len(*ops) <= 0 {
		logger.Print(logger.ERROR, "Proper flag not specified")
		os.Exit(1)
	} else if *ops != "add" && *ops != "remove" {
		logger.Print(logger.ERROR, "Invalid operation specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	gracefullyShutDown()

	if *ops == "add" {
		AddOwnersToRctx(*tenantID, *ownerName, *ownerType, *ownerDesc, *ownerTypeDBKey, *collectedAts, *resourcesIds, *identityID, *modifyCR)
	} else {
		RemoveOwnersFromRctx(*tenantID, *ownerName, *ownerType, *ownerDesc, *ownerTypeDBKey, *collectedAts, *resourcesIds, *identityID, *modifyCR)
	}

	logger.Print(logger.INFO, "Migration completed successfully", []string{*tenantID})
	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
