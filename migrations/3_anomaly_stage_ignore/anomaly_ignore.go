package main

import (
	"encoding/json"
	"strings"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func SetAnomalyStage(category, tenantID, stage string) {

	mustClauses := []map[string]any{
		{
			"match": map[string]any{
				"category.keyword": category,
			},
		},
	}

	if tenantID != "" {
		mustClauses = append(mustClauses, map[string]any{
			"match": map[string]any{
				"tenantId.keyword": tenantID,
			},
		})

	}

	query := map[string]any{
		"_source": []string{"id"},
		"query": map[string]any{
			"bool": map[string]any{
				"must": mustClauses,
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
		return
	}

	var (
		bulkCIncidentsUpdate strings.Builder
		searchAfter          any
	)

	for {

		incidentDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_INCIDENTS_INDEX}, string(queryBytes), searchAfter)
		if err != nil {
			return
		}

		if len(incidentDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(incidentDocs))

		for incidentDocsID := range incidentDocs {
			incidentUpdateMetadata := `{"update": {"_id": "` + incidentDocsID + `"}}`
			incidentUpdateDoc := `{"doc":{"stage":"` + stage + `"}}`

			bulkCIncidentsUpdate.WriteString(incidentUpdateMetadata)
			bulkCIncidentsUpdate.WriteString("\n")
			bulkCIncidentsUpdate.WriteString(incidentUpdateDoc)
			bulkCIncidentsUpdate.WriteString("\n")
		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkCIncidentsUpdate.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud incident bulk API Successful", []string{tenantID})
		bulkCIncidentsUpdate.Reset()
	}

}
