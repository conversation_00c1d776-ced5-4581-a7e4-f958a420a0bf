package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 06/06/2025
// Target - Cloud Incidents
// This migration is to set stage=anomaly for older stale anomalies

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		tenantID      = flag.String("tenant", "", "TenantId to run migration for")
		category      = flag.String("category", "Precize Anomaly", "Category of text lookup to delete")
		stage         = flag.String("stage", "ignore", "Stage to set")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*category) <= 0 || len(*stage) <= 0 {
		logger.Print(logger.ERROR, "Flag 'category' and 'stage' has to be specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	gracefullyShutDown()

	SetAnomalyStage(*category, *tenantID, *stage)
	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
