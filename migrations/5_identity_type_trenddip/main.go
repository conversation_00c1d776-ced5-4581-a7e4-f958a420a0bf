package main

// write a migration script to accept tenantID, serviceCode, and type (eg, ACTIVITY_IDENTITY, TAGGED_IDENTITY, etc)

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 07/07/2025
// Target - IDENTITIES AND RCTX
// This migration is to deleted identities in a speicific collectedAt, get the resources linked to those identities
// and check if those are deleted or not

func main() {
	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		tenantID      = flag.String("tenant", "", "TenantId to run migration for")
		serviceCode   = flag.String("serviceCode", "", "ServiceCode to run migration for eg: aws, gcp, azure")
		identityType  = flag.String("identityType", "", "IdentityType to run migration for")
		collectedAt   = flag.String("collectedAt", "", "CollectedAt to run migration for eg: 2025-06-30T12:34:11.392Z")
	)

	flag.Parse()

	logger.InitializeLogs("migration", false)

	if len(*tenantID) <= 0 || len(*serviceCode) <= 0 || len(*identityType) <= 0 || len(*collectedAt) <= 0 {
		logger.Print(logger.ERROR, "TenantId, ServiceCode, and IdentityType not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	GetTrendForIdentityType(*tenantID, *serviceCode, *identityType, *collectedAt)

	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
