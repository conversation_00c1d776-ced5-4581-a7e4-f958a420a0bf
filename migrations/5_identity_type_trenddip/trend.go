package main

import (
	"encoding/json"
	"fmt"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetTrendForIdentityType(tenantID, serviceCode, identityType, collectedAt string) (err error) {

	logger.Print(logger.INFO, "Fetching trend for identity type", []string{tenantID, serviceCode, identityType, collectedAt})

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"tenantId.keyword": tenantID,
						},
					},
					{
						"term": map[string]any{
							"serviceCode.keyword": serviceCode,
						},
					},
					{
						"term": map[string]any{
							"type.keyword": identityType,
						},
					},
					{
						"term": map[string]any{
							"updatedDate": collectedAt,
						},
					},
					{
						"term": map[string]any{
							"deleted": true,
						},
					},
				},
			},
		},
	}

	queryBytes, err := json.Marshal(query)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
		return err
	}

	var (
		searchAfter            any
		deletedIdentitiesCount int
	)

	for {
		identitiesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, string(queryBytes), searchAfter)
		if err != nil {
			logger.Print(logger.ERROR, "Error executing search query", err)
			return err
		}

		if len(identitiesDocs) > 0 {
			searchAfter = sortResponse
			deletedIdentitiesCount += len(identitiesDocs)
		} else {
			break
		}

		for _, identityDoc := range identitiesDocs {

			additionalInfo := identityDoc["additionalInfo"].(string)
			var additionalInfoMap map[string]any
			if err := json.Unmarshal([]byte(additionalInfo), &additionalInfoMap); err != nil {
				continue
			}

			if resCtxId, ok := additionalInfoMap["resCtxId"].(string); ok {
				query := map[string]any{
					"query": map[string]any{
						"bool": map[string]any{
							"must": []map[string]any{
								{
									"match": map[string]any{
										"id.keyword": resCtxId,
									},
								},
								{
									"range": map[string]any{
										"updatedTime": map[string]any{
											"lt": collectedAt,
										},
									},
								},
							},
						},
					},
					"size": 1,
				}

				queryBytes, err := json.Marshal(query)
				if err != nil {
					logger.Print(logger.ERROR, "Error marshalling query to JSON", err)
					continue
				}

				rctxDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.RESOURCE_CONTEXT_INDEX}, string(queryBytes))
				if err != nil {
					logger.Print(logger.ERROR, "Error executing search query", err)
					continue
				}

				if len(rctxDocs) > 0 {
					logger.Print(logger.INFO, "Deleted Resource", identityDoc["identityId"].(string))
				} else {
					logger.Print(logger.INFO, "Active Resource", identityDoc["identityId"].(string))
				}
			}
		}
	}

	logger.Print(logger.INFO, fmt.Sprintf("Found %d deleted identities of type '%s' for service '%s' on %s", deletedIdentitiesCount, identityType, serviceCode, collectedAt))

	return nil

}
