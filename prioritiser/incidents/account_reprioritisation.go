package incidents

import (
	"github.com/precize/common"
)

func ReprioritiseBasedOnAccount(pData *PrioritisationData) {

	var (
		accountSizePercentage float64
		largeAccountThreshold float64 = 10
		moderateSizeThreshold float64 = 5
	)

	if pData.tenantValues.resourceCount > 0 {
		accountSizePercentage = float64(pData.accountValues.resourceCount) / float64(pData.tenantValues.resourceCount) * 100
	}

	if pData.tenantValues.resourceCount < 20000 {
		largeAccountThreshold = 20
		moderateSizeThreshold = 10
	}

	if accountSizePercentage >= largeAccountThreshold {
		// big account
		SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, ACCOUNTSIZE_CRITERIA, pData, true)
	} else if accountSizePercentage >= moderateSizeThreshold && !pData.IsResourceProd {
		// moderate account
		SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, MODERATEACCOUNTSIZE_CRITERIA, pData, false)
	} else if !pData.IsResourceProd {
		// small account
		SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, FEWACCOUNTSIZE_CRITERIA, pData, false)
	}

	var exEmployeePercentage float64

	if pData.accountValues.totalOwnersCount > 0 {
		exEmployeePercentage = float64(pData.accountValues.exEmployeesCount) / float64(pData.accountValues.totalOwnersCount) * 100
	}

	if exEmployeePercentage >= 5 {
		// account with significant ex-employee - more than 5% of resources have ex-employees
		SetLikelihood(pData.PrecizeLikelihood, common.VERY_LIKELY_LIKELIHOOD, EXEMPACC_CRITERIA, pData, true)
	} else if exEmployeePercentage >= 2 {
		SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, FEWEXEMPACC_CRITERIA, pData, false)
	}

	if pData.accountValues.uniqueOwnersCount > 15 {
		// account with significant users - more than 20 unique owners in the account
		SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, UNIQUEOWNERS_CRITERIA, pData, true)
	} else if pData.accountValues.uniqueOwnersCount < 5 && !pData.IsResourceProd {
		SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, FEWUNIQUEOWNERS_CRITERIA, pData, false)
	}

	// TODO: Get unique users who have access to account

	var impactResourcePercentage float64

	if pData.accountValues.resourceCount > 0 {
		impactResourcePercentage = float64(pData.accountValues.impactResourcesCount) / float64(pData.accountValues.resourceCount) * 100
	}

	if impactResourcePercentage > 30 && !pData.IsResourceProd {
		// More than 30% of resources in the account are impactful
		SetImpact(pData.PrecizeImpact, common.SIGNIFICANT_IMPACT, IMPACTFULRESOURCEACCOUNT_CRITERIA, pData, true)
	} else if impactResourcePercentage <= 30 && impactResourcePercentage > 20 {
		SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT, FEWIMPACTFULRESOURCEACCOUNT_CRITERIA, pData, false)
	} else if impactResourcePercentage <= 20 && impactResourcePercentage > 10 {
		if pData.IsResourceProd && (pData.OpenToInternet || pData.SensitiveData) {
			DecreaseImpactByOneLevel(pData.PrecizeImpact, FEWIMPACTFULRESOURCEACCOUNT_CRITERIA, pData, false)
		} else {
			SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT, FEWIMPACTFULRESOURCEACCOUNT_CRITERIA, pData, false)
		}
	} else if impactResourcePercentage <= 10 {
		if pData.IsResourceProd && (pData.OpenToInternet || pData.SensitiveData) {
			DecreaseImpactByOneLevel(pData.PrecizeImpact, FEWIMPACTFULRESOURCEACCOUNT_CRITERIA, pData, false)
		} else {
			SetImpact(pData.PrecizeImpact, common.NEGLIGIBLE_IMPACT, NOIMPACTFULRESOURCEACCOUNT_CRITERIA, pData, false)
		}
	}
}
