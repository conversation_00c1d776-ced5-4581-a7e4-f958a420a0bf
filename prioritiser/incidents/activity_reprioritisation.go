package incidents

import (
	"github.com/precize/common"
)

func ReprioritiseBasedOnActivity(pData *PrioritisationData) {

	if pData.accountValues.activitiesCount >= (pData.accountValues.resourceCount * 10) {
		SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, ACTIVEACC_CRITERIA, pData, true)
	} else if pData.accountValues.activitiesCount >= (pData.accountValues.resourceCount * 5) {
		SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, MODERATELYACTIVEACC_CRITERIA, pData, true)
	} else if !pData.IsResourceProd {
		SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, INACTIVEACC_CRITERIA, pData, false)
	}
}
