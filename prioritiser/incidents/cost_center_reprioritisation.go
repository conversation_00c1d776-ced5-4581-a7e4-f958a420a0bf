package incidents

import "github.com/precize/common"

func ReprioritiseBasedOnCostCenter(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	if len(pData.accountValues.costCenters) <= 0 {
		// no cost center attached to any child resources

		if riskScoreMap[pData.PrecizeRisk] < 8 {
			pData.PrecizeRisk = IncreaseRiskByOneLevel(pData.PrecizeRisk)
		}
		pData.PriorityCriteria[NO_COSTCENTER_CRITERIA] = true
	} else if riskScoreMap[pData.PrecizeRisk] == 10 {
		// if account resource has proper cost center defined

		pData.PrecizeRisk = DecreaseRiskByOneLevel(pData.PrecizeRisk)
		pData.PriorityCriteria[HAS_COSTCENTER_CRITERIA] = false
	}
}
