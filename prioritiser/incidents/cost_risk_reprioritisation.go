package incidents

import (
	"sort"

	"github.com/precize/common"
)

func ReprioritiseBasedOnRelativeCost(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {
	if len(pData.tenantValues.accountCosts) == 0 {
		return
	}

	currentAccountCost := pData.tenantValues.accountCosts[crsDoc.EntityID]

	var allCosts []float64
	for _, cost := range pData.tenantValues.accountCosts {
		allCosts = append(allCosts, cost)
	}

	sort.Sort(sort.Reverse(sort.Float64Slice(allCosts)))

	currentPosition := 0
	for i, cost := range allCosts {
		if cost == currentAccountCost {
			currentPosition = i
			break
		}
	}

	totalAccounts := float64(len(allCosts))
	percentile := (float64(currentPosition) / totalAccounts) * 100

	switch {
	case totalAccounts == 1:
		if currentAccountCost < 100 {
			pData.PrecizeRisk = common.LOW_RISK
		} else {
			pData.PrecizeRisk = common.MEDIUM_RISK
		}
		pData.PriorityCriteria[HIGH_COST_ACCOUNT_CRITERIA] = true
	case percentile <= 10:
		// Top 10% account
		if currentAccountCost < 50 {
			pData.PrecizeRisk = common.LOW_RISK
		} else if currentAccountCost < 100 {
			pData.PrecizeRisk = common.MEDIUM_RISK
		} else {
			pData.PrecizeRisk = common.HIGH_RISK
		}
		pData.PriorityCriteria[VERY_HIGH_ACCOUNT_COST_CRITERIA] = true
	case percentile <= 20:
		// Top 20% account
		if currentAccountCost < 100 {
			pData.PrecizeRisk = common.LOW_RISK
		} else {
			pData.PrecizeRisk = common.MEDIUM_RISK
		}
		pData.PriorityCriteria[HIGH_COST_ACCOUNT_CRITERIA] = true
	default:
		pData.PrecizeRisk = common.LOW_RISK
		pData.PriorityCriteria[LOW_COST_ACCOUNT_CRITERIA] = false
	}
}
