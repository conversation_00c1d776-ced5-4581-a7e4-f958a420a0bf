package incidents

import "github.com/precize/common"

func ReprioritiseBasedOnExternalIncidents(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	if count, ok := pData.resourceExternalIncidentsMap[crsDoc.EntityID]; ok {
		if count > 20 {
			SetLikelihood(pData.PrecizeLikelihood, common.VERY_LIKELY_LIKELIHOOD, SIG_EXT_INCIDENTS_CRITERIA, pData, true)
		} else if count > 10 {
			SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, SOME_EXT_INCIDENTS_CRITERIA, pData, true)
		} else if count > 5 {
			SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, MINOR_EXT_INCIDENTS_CRITERIA, pData, true)
		} else {
			SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, VERYFEW_EXT_INCIDENTS_CRITERIA, pData, false)
		}
	}
}
