package incidents

import (
	"encoding/json"
	"strings"

	"github.com/precize/common"
	"github.com/precize/enhancer/context"
)

func ReprioritiseBasedOnIdentity(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {
	identityID := crsDoc.EntityID

	switch crsDoc.EntityType {
	case common.AZURE_ADUSER_RESOURCE_TYPE:
		entityJSON := make(map[string]any)
		if err := json.Unmarshal([]byte(crsDoc.PriorityMapAsStr), &entityJSON); err != nil {
			return
		}
		if eId, ok := entityJSON["Principal Name"].(string); ok {
			identityID = eId
		}
	}
	if len(pData.tenantValues.identityHeroStats[identityID]) == 0 {
		pData.PrecizeRisk = common.LOW_RISK
		return
	}

	var (
		totalWeight                  float32
		weightedSum                  float32
		isExUser                     bool
		isActiveCloudUser            bool
		isPermissionRelatedHsPresent bool
		identityTypes                []string
		enhancerTypeMap              = map[string]struct{}{
			"BITBUCKET_IDENTITY": {},
			"GITLAB_IDENTITY":    {},
			"GITHUB_IDENTITY":    {},
			"TAGGED_IDENTITY":    {},
			"DEFINED_IDENTITY":   {},
			"ACTIVITY_IDENTITY":  {},
			"DERIVED_IDENTITY":   {},
		}
	)

	if identityMap, ok := pData.tenantValues.identityHeroStats[identityID]; ok {
		if heroStats, ok := identityMap["heroStats"].(map[string]struct{}); ok {
			for heroStat := range heroStats {
				if severity, exists := identityHsSeverity[heroStat]; exists {
					if score, ok := riskScoreMap[severity]; ok {
						weightedSum += score
						totalWeight += 10.0
					}
				}

				if !isPermissionRelatedHsPresent {
					if _, ok := permissionRelatedIdentityHs[heroStat]; ok {
						isPermissionRelatedHsPresent = true
					}
				}
			}
		}

		if exIdentity, ok := identityMap["isExUser"].(bool); ok {
			isExUser = exIdentity
		}

		if types, ok := identityMap["identityTypes"].([]string); ok {
			identityTypes = append(identityTypes, types...)
		}
	}

	if totalWeight > 0 {
		averageRisk := weightedSum / totalWeight * 10.0

		switch {
		case averageRisk >= 8.0:
			pData.PrecizeRisk = common.HIGH_RISK
			pData.PriorityCriteria[PRECIZE_HERO_STATS_CRITERIA] = true
		case averageRisk >= 6.0:
			pData.PrecizeRisk = common.MEDIUM_RISK
		case averageRisk >= 4.0:
			pData.PrecizeRisk = common.MEDIUM_RISK
		case averageRisk >= 2.0:
			pData.PrecizeRisk = common.LOW_RISK
			pData.PriorityCriteria[PRECIZE_HERO_STATS_CRITERIA] = false
		default:
			pData.PrecizeRisk = common.LOW_RISK
		}

		if isExUser {
			if pData.PrecizeRisk == common.LOW_RISK {
				pData.PrecizeRisk = SetRisk(pData.PrecizeRisk, common.HIGH_RISK)
			} else if riskScoreMap[pData.PrecizeRisk] <= 5 {
				pData.PrecizeRisk = IncreaseRiskByOneLevel(pData.PrecizeRisk)
			}
			pData.PriorityCriteria[EXUSER_IDENTITY_CRITERIA] = true
		}

		isActivityIdentity := false
		for _, t := range identityTypes {
			if _, ok := enhancerTypeMap[t]; ok {
				isActiveCloudUser = true
			}

			if t == "ACTIVITY_IDENTITY" {
				isActivityIdentity = true
			}
		}

		if !isActiveCloudUser {
			pData.PrecizeRisk = DecreaseRiskByOneLevel(pData.PrecizeRisk)
			pData.PriorityCriteria[INACTIVE_IDENTITY_CRITERIA] = false
		} else {
			// if there is any permission/access related hero stat present, although the user is not performing any activities
			if isPermissionRelatedHsPresent && !isActivityIdentity {
				pData.PrecizeRisk = DecreaseRiskByOneLevel(pData.PrecizeRisk)
				pData.PriorityCriteria[NOACTIVITY_IDENTITY_CRITERIA] = false
			} else if isPermissionRelatedHsPresent && riskScoreMap[pData.PrecizeRisk] <= 5 {
				pData.PrecizeRisk = IncreaseRiskByOneLevel(pData.PrecizeRisk)
				pData.PriorityCriteria[ACTIVITY_PERMHS_IDENTITY_CRITERIA] = true
			}
		}
	}

	if score, ok := riskScoreMap[pData.PrecizeRisk]; ok && score <= 5 {
		for _, owner := range crsDoc.Owner {
			if strings.Contains(owner, strings.Trim(context.EX_EMPLOYEE_PREFIX, " ")) {
				//  ex-employee
				pData.PrecizeRisk = IncreaseRiskByOneLevel(pData.PrecizeRisk)
				pData.PriorityCriteria[EXEMPLOYEE_CRITERIA] = true
				break
			}
		}
	}
}
