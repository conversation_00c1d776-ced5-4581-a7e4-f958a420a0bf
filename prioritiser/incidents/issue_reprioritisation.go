package incidents

import (
	"encoding/json"
	"fmt"
	"io"
	"math"
	"os"
	"path/filepath"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
)

const (
	ISSUE_RISK_DETAILS_PATH = "issue_risk_details.json"
)

type IncidentDetails struct {
	HighestRiskLevel     string
	LowestRiskLevel      string
	HighestRiskFactors   []string
	LowestRiskFactors    []string
	IncreaseRisk         bool
	IncreaseRiskCriteria []string
	Categories           []string
}

func ReprioritiseBasedOnFindings(pData *PrioritisationData, currentRisk string, incidentDataMap common.Incident) string {

	var (
		isVulnerable bool
		isBreached   bool
	)

	switch incidentDataMap.Source {
	case common.DEFENDER_SOURCE:
		if incidentDataMap.IsIncident {
			isBreached = true
		}

	case common.SECURITY_COMMAND_CENTER_SOURCE:
		if incidentDataMap.Category == "VULNERABILITY" || incidentDataMap.Category == "OS_VULNERABILITY" || incidentDataMap.Category == "THREAT" {
			isVulnerable = true
		}
	case common.SECURITYHUB_SOURCE:
		if incidentDataMap.Category == "Software and Configuration Checks/Vulnerabilities/CVE" || incidentDataMap.Category == "Software and Configuration Checks/Vulnerabilities/Code Vulnerabilities" || incidentDataMap.Category == "Software and Configuration Checks/AWS Security Best Practices/Network Reachability" || incidentDataMap.Category == "TTPs/Discovery/Recon:EC2-PortProbeUnprotectedPort" || incidentDataMap.Category == "TTPs/Initial Access/UnauthorizedAccess:EC2-SSHBruteForce" {
			isVulnerable = true
		}
	case common.ORCA_SOURCE:
		if incidentDataMap.IsIncident {
			isBreached = true
		} else {
			if incidentDataMap.Category == "Vulnerabilities" || incidentDataMap.Category == "Network misconfigurations" && !strings.Contains(incidentDataMap.Issue, "should be enabled") {
				isVulnerable = true
			}
		}
	case common.WIZ_SOURCE:
		if incidentDataMap.IsIncident {
			isBreached = true
		} else {
			if incidentDataMap.Category == "Vulnerability" {
				isVulnerable = true
			}
		}
	}

	if isVulnerable {

		pData.IncidentType = VULNERABILITY_TYPE

		if pData.OpenToInternet {
			if pData.PriorityCriteria[PRODENV_CRITERIA] {
				pData.PriorityCriteria[RSC_VULN_OPENTOINTERNET] = true
				SetLikelihoodWithoutAvg(common.VERY_LIKELY_LIKELIHOOD,
					OPENTOINTERNET_CRITERIA, pData, true)
			} else {
				pData.PriorityCriteria[RSC_VULN_OPENTOINTERNET] = true
				SetLikelihoodWithoutAvg(common.LIKELY_LIKELIHOOD,
					OPENTOINTERNET_CRITERIA, pData, true)
			}

			likelihood := likelihoodNumberToString[float32(math.Round(float64(pData.PrecizeLikelihood)))]
			impact := impactNumberToString[float32(math.Round(float64(pData.PrecizeImpact)))]

			currentRisk, _ = GetRiskDetails(likelihood, impact)
		} else {
			if pData.PriorityCriteria[PRODENV_CRITERIA] {
				if riskScoreMap[currentRisk] > riskScoreMap[common.HIGH_RISK] {
					pData.PriorityCriteria[RSC_VULN_NOTOPENTOINTERNET] = false
					currentRisk = SetRisk(currentRisk, common.HIGH_RISK)
				}
			} else {
				if riskScoreMap[currentRisk] > riskScoreMap[common.MEDIUM_RISK] {
					pData.PriorityCriteria[RSC_VULN_NOTOPENTOINTERNET] = false
					currentRisk = SetRisk(currentRisk, common.MEDIUM_RISK)
				}
			}

			if incidentDataMap.Source == common.DEFENDER_SOURCE || incidentDataMap.Source == common.ORCA_SOURCE {

				var additionalData = make(map[string]any)

				err := json.Unmarshal([]byte(incidentDataMap.AdditionalData), &additionalData)
				if err != nil {
					logger.Print(logger.ERROR, "Error unmarshalling JSON", err)
				} else {

					if findings, ok := additionalData["recommendationFindings"].([]any); ok {

						var (
							finalScore, count float64
							critical, cve     bool
						)

						for _, finding := range findings {

							if vulnerability, ok := finding.(map[string]any); ok {
								if id, ok := vulnerability["id"].(string); ok {
									count++
									if strings.HasPrefix(id, "CVE-") {
										if score, ok := vulnerability["score"].(float64); ok && score > 0 {
											cve = true
											finalScore += score
											if score >= 9 {
												critical = true
											}
										}
									}
								}
							}
						}

						if currentRisk == common.CRITICAL_RISK {
							if !critical || !cve {
								currentRisk = DecreaseRiskByOneLevel(currentRisk)
								if !critical {
									pData.PriorityCriteria[NO_CRITICAL_VULNERABILITIES] = false
								} else {
									pData.PriorityCriteria[NO_SEVERE_VULNERABILITIES] = false
								}
							}
						} else if currentRisk == common.HIGH_RISK || currentRisk == common.MEDIUM_RISK {

							if count > 0 {

								avgScore := finalScore / count
								if currentRisk == common.HIGH_RISK && (avgScore < 6 || !cve) {
									currentRisk = DecreaseRiskByOneLevel(currentRisk)
									pData.PriorityCriteria[NO_SEVERE_VULNERABILITIES] = true
								} else if avgScore >= 9 {
									currentRisk = IncreaseRiskByOneLevel(currentRisk)
									pData.PriorityCriteria[CRITICAL_VULNERABILITIES] = true
								}
							}
						}
					}
				}
			}
		}

	} else if isBreached {

		pData.IncidentType = MALWARE_TYPE

		// malware incidents
		if pData.PriorityCriteria[PRODENV_CRITERIA] {
			currentRisk = SetRisk(currentRisk, common.CRITICAL_RISK)
		} else {
			currentRisk = SetRisk(currentRisk, common.HIGH_RISK)
		}
	}

	return currentRisk
}

func ReprioritiseBasedOnIssue(pData *PrioritisationData, currentRisk string, incidentDataMap common.Incident) string {

	if incidentDataMap.Issue == "" {
		return currentRisk
	}

	ex, _ := os.Executable()
	exPath := filepath.Dir(ex)

	issueUtilsPath := exPath + "/" + ISSUE_RISK_DETAILS_PATH

	issueRiskDetailsJson, err := os.Open(issueUtilsPath)
	if err != nil {
		logger.Print(logger.INFO, "Got error opening json file: ", err)
		return currentRisk
	}

	defer issueRiskDetailsJson.Close()

	issueRiskDetailsBytes, err := io.ReadAll(issueRiskDetailsJson)
	if err != nil {
		logger.Print(logger.INFO, "Got error reading issue_risk_details.json: ", err)
		return currentRisk
	}

	var issueRiskLevels = make(map[string]IncidentDetails)
	if err = json.Unmarshal([]byte(issueRiskDetailsBytes), &issueRiskLevels); err != nil {
		logger.Print(logger.INFO, "Got error unmarshalling json file: ", err)
		return currentRisk
	}

	if issueDetails, ok := issueRiskLevels[incidentDataMap.Issue]; ok {

		pData.IncidentDetails = issueDetails

		if len(issueDetails.HighestRiskLevel) <= 0 {
			issueDetails.HighestRiskLevel = common.CRITICAL_RISK
		}

		if len(issueDetails.LowestRiskLevel) <= 0 {
			issueDetails.LowestRiskLevel = common.LOW_RISK
		}

		allHighestFactorsPresent := true
		if len(issueDetails.HighestRiskFactors) > 0 {
			for _, factor := range issueDetails.HighestRiskFactors {
				if _, ok := pData.PriorityCriteria[factor]; !ok {
					allHighestFactorsPresent = false
					break
				}
			}

			if allHighestFactorsPresent {
				currentRisk = SetRisk(currentRisk, issueDetails.HighestRiskLevel)
			}
		}

		allLowestFactorsPresent := true
		if len(issueDetails.LowestRiskFactors) > 0 {
			for _, factor := range issueDetails.LowestRiskFactors {
				if _, ok := pData.PriorityCriteria[factor]; !ok {
					allLowestFactorsPresent = false
					break
				}
			}

			if allLowestFactorsPresent {
				currentRisk = SetRisk(currentRisk, issueDetails.LowestRiskLevel)
			}
		}

		if len(issueDetails.IncreaseRiskCriteria) > 0 {
			for _, factor := range issueDetails.IncreaseRiskCriteria {
				if !pData.PriorityCriteria[factor] {
					break
				} else {
					if issueDetails.IncreaseRisk {
						currentRisk = IncreaseRiskByOneLevel(currentRisk)
					} else {
						currentRisk = DecreaseRiskByOneLevel(currentRisk)
					}
				}
			}
		}

		currentRiskIndex := indexOf(riskLevels, currentRisk)
		highestRiskIndex := indexOf(riskLevels, issueDetails.HighestRiskLevel)
		lowestRiskIndex := indexOf(riskLevels, issueDetails.LowestRiskLevel)

		if currentRiskIndex > highestRiskIndex {
			return SetRisk(currentRisk, issueDetails.HighestRiskLevel)
		} else if currentRiskIndex < lowestRiskIndex {
			return SetRisk(currentRisk, issueDetails.LowestRiskLevel)
		}
	} else {

		if incidentDataMap.Source == common.WIZ_SOURCE {
			return currentRisk
		}

		categories := categorizeIssueWithGPT(incidentDataMap.Issue, pData.TenantID)

		newIssueDetails := IncidentDetails{
			Categories: categories,
		}
		issueRiskLevels[incidentDataMap.Issue] = newIssueDetails
		pData.IncidentDetails = issueDetails

		updatedJson, err := json.MarshalIndent(issueRiskLevels, "", "  ")
		if err != nil {
			logger.Print(logger.INFO, "Error marshalling updated JSON: ", err)
			return currentRisk
		}

		if err := os.WriteFile(issueUtilsPath, updatedJson, 0644); err != nil {
			logger.Print(logger.INFO, "Error writing updated JSON file: ", err)
			return currentRisk
		}
	}

	// add missed out categories to issueDetails
	if len(pData.IncidentType) > 0 {
		pData.IncidentDetails.Categories = append(pData.IncidentDetails.Categories, pData.IncidentType)
	}

	if pData.OpenToInternet && pData.SensitiveData {
		pData.IncidentDetails.Categories = append(pData.IncidentDetails.Categories, SENSITIVEDATAEXPOSED_CATEGORY)
	} else if pData.OpenToInternet {
		pData.IncidentDetails.Categories = append(pData.IncidentDetails.Categories, OPENTOINTERNET_CATEGORY)
	} else if pData.SensitiveData {
		pData.IncidentDetails.Categories = append(pData.IncidentDetails.Categories, SENSITIVEDATA_CATEGORY)
	}

	// governance issues should not be of high or critical risk
	if slices.Contains(pData.IncidentDetails.Categories, GOVERNANCE_CATEGORY) {
		if riskScoreMap[currentRisk] >= riskScoreMap[common.HIGH_RISK] {
			currentRisk = common.MEDIUM_RISK
		}
	}

	return currentRisk
}

func ReprioritiseBasedOnStaticIssueConditions(pData *PrioritisationData, currentRisk string, incidentDataMap common.Incident) string {
	if strings.Contains(strings.ToLower(incidentDataMap.Issue), LOGGING) ||
		strings.Contains(strings.ToLower(incidentDataMap.Issue), MONITORING) ||
		strings.Contains(strings.ToLower(incidentDataMap.Issue), DEBUGGING) ||
		strings.Contains(strings.ToLower(incidentDataMap.Issue), ENABLE_IF_REQUIRED) ||
		strings.Contains(strings.ToLower(incidentDataMap.Issue), AUDIT) || strings.Contains(strings.ToLower(incidentDataMap.Issue), CMDCENTER_LOG) {
		if currentRisk == "CRITICAL_RISK" || currentRisk == "HIGH_RISK" {
			return SetRisk(currentRisk, common.MEDIUM_RISK)
		} else {
			return SetRisk(currentRisk, common.LOW_RISK)
		}
	} else if strings.Contains(strings.ToLower(incidentDataMap.Issue), ENABLE) && strings.Contains(strings.ToLower(incidentDataMap.Issue), ENCRYPTION) {
		currentRiskIndex := indexOf(riskLevels, currentRisk)
		sourceRiskIndex := indexOf(riskLevels, pData.SourceRisk)

		if currentRiskIndex > sourceRiskIndex {
			return pData.SourceRisk
		} else {
			return currentRisk
		}
	}
	return currentRisk
}

func categorizeIssueWithGPT(issueTitle, tenantID string) []string {
	categories := []string{
		GOVERNANCE_CATEGORY,
		SENSITIVEDATA_CATEGORY,
		OPENTOINTERNET_CATEGORY,
		SENSITIVEDATAEXPOSED_CATEGORY,
	}

	prompt := fmt.Sprintf("Classify the following incident into these categories: %s. \n\nIncident Title: %s",
		strings.Join(categories, ", "), issueTitle)

	prompt += `
		- "Governance": A safety measure that will help in the future and is not an active problem as of now.
		- "SensitiveData": The data in the resource is sensitive.
		- "OpenToInternet": The resource is already open to internet and can be accessed by everyone.
		- "SensitiveDataExposed": The data in the resource is sensitive and has been exposed.

	Return the categories which are strictly applicable, each categories by comma.
	`

	response := common.DeriveCategoryForIncident(prompt, tenantID)

	if len(response) == 0 {
		return []string{}
	}

	resCategories := strings.Split(response, ",")
	detectedCategories := make([]string, 0)

	for _, category := range resCategories {
		category = strings.TrimSpace(category)
		if slices.Contains(categories, category) {
			detectedCategories = append(detectedCategories, category)
		}
	}

	return detectedCategories
}
