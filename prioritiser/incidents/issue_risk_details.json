{"'Gamarue' malware was prevented": {"HighestRiskLevel": "", "LowestRiskLevel": "High", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Malware"]}, "'Multiverze' malware was detected (Agentless preview)": {"HighestRiskLevel": "", "LowestRiskLevel": "High", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Malware"]}, "'NanoCore' malware was detected": {"HighestRiskLevel": "", "LowestRiskLevel": "High", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Malware"]}, "A Storage account was accessed from a suspicious IP address": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": true, "IncreaseRiskCriteria": ["is owned by an ex employee"], "Categories": ["Vulnerability", "OpenToInternet"]}, "A maximum of 3 owners should be designated for subscriptions": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Access to kubelet kubeconfig file detected": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Access to kubelet kubeconfig file detected (Preview)": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Access to storage accounts with firewall and virtual network configurations should be restricted": {"HighestRiskLevel": "High", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["is very active"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["OpenToInternet"]}, "All network ports should be restricted on network security groups associated to your virtual machine": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "An activity log alert should exist for Create or Update Network Security Group": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for Create or Update Network Security Group Rule": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for Create or Update SQL Server Firewall Rule": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for Delete Network Security Group": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for Delete SQL Server Firewall Rule": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for specific Administrative operations": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for specific Policy operations": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for specific Security operations": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "An activity log alert should exist for the Delete Network Security Group Rule": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "AppServices_AnomalousPageAccess": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Audit Windows machines on which the Log Analytics agent is not connected as expected": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Audit diagnostic setting for selected resource types": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Audit flow logs configuration for every virtual network": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Authentication to Linux machines should require SSH keys": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Auto provisioning of the Log Analytics agent should be enabled on subscriptions": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure AI Services resources should have key access disabled (disable local authentication)": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure AI Services resources should restrict network access": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Azure AI Services resources should use Azure Private Link": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Azure API Management APIs should be onboarded to Defender for APIs": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure Backup should be enabled for virtual machines": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure DDoS Protection Standard should be enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Azure Key Vaults should use private link": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData", "OpenToInternet"]}, "Azure Kubernetes Service clusters should have Defender profile enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure Machine Learning Workspaces should disable public network access": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Azure Monitor log profile should collect logs for categories 'write,' 'delete,' and 'action'": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure Monitor should collect activity logs from all regions": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Azure MySQL flexible server should have Azure Active Directory Only Authentication enabled": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Azure SQL Database should have Azure Active Directory Only Authentication enabled": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["is significant, because it exists in a production environment with high activity and is being used by a wide range of users"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Centralize Root Access Management for Enhanced Security": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Connection to web page from anomalous IP address detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Container registries should not allow unrestricted network access": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Container registries should use private link": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Container with a sensitive volume mount detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["SensitiveData"]}, "Creation of admission webhook configuration detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Deploy Diagnostic Settings for Network Security Groups": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Deploy the Linux Guest Configuration extension to enable Guest Configuration assignments on Linux VMs": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Deploy the Windows Guest Configuration extension to enable Guest Configuration assignments on Windows VMs": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Deprecated Production resources": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Detected suspicious file download": {"HighestRiskLevel": "", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": true, "IncreaseRiskCriteria": ["is owned by an ex employee"], "Categories": ["Malware"]}, "Diagnostic logs in App Service should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Azure AI services resources should be enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Event Hub should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Key Vault should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Kubernetes services should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Logic Apps should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Diagnostic logs in Service Bus should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "EDR configuration issues should be resolved on virtual machines": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "EDR solution should be installed on Virtual Machines": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Email notification for high severity alerts should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Firewall should be enabled on Key Vault": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData", "OpenToInternet"]}, "Flow logs should be configured for every network security group": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Function apps should have Client Certificates (Incoming client certificates) enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Globally exposed Cognitive Services": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["OpenToInternet", "SensitiveDataExposed"]}, "Governance Lacking For Model Management And Tracking": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Guest Attestation extension should be installed on supported Linux virtual machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Guest Attestation extension should be installed on supported Windows virtual machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Guest Configuration extension should be installed on machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Guest accounts with owner permissions on Azure resources should be removed": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Guest accounts with read permissions on Azure resources should be removed": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Indirect Prompt Attack Filtering Not Present Or Working With Reduce Efficacy": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData"]}, "Insecure Unmonitored Model Assistant": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["SensitiveData", "OpenToInternet"]}, "Insecure User API Keys Used": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["SensitiveDataExposed", "OpenToInternet"]}, "Internet-facing virtual machines should be protected with network security groups": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["OpenToInternet"]}, "Jailbreak Filtering Is Not Present Or Working With Reduced Efficacy": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Key vaults should have purge protection enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Lack Of Access Control For Default Project Causes RBAC Issues": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Legacy Or Deprecated Model Is Being Enabled Or Used": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Linux virtual machines should enable Azure Disk Encryption or EncryptionAtHost.": {"HighestRiskLevel": "High", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["is externally expose"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Log Analytics agent should be installed on Linux-based Azure Arc-enabled machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Log Analytics agent should be installed on Windows-based Azure Arc-enabled machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Log Analytics agent should be installed on virtual machine scale sets": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Log Analytics agent should be installed on virtual machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Login from an unusual data center": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["is owned by an ex employee"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Login from an unusual location": {"HighestRiskLevel": "", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["is owned by an ex employee"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "ML Training And Fine-Tuning Accuracy Fluctuation": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "ML Training and Fine-Tuning Instability": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Machines should be configured securely": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Machines should be configured to periodically check for missing system updates": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Machines should have a vulnerability assessment solution": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Machines should have secrets findings resolved": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData"]}, "Machines should have vulnerability findings resolved": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Managed identity should be used in function apps": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Management Account Improperly Used": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveDataExposed"]}, "Management ports of virtual machines should be protected with just-in-time network access control": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Management ports should be closed on your virtual machines": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Microsoft Defender CSPM should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for APIs should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for App Service should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Azure Cosmos DB should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Azure SQL Database servers should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Containers should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Key Vault should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Resource Manager should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for SQL on machines should be enabled on workspaces": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for SQL servers on machines should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for SQL should be enabled for unprotected Azure SQL servers": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for SQL should be enabled for unprotected PostgreSQL flexible servers": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for SQL status should be protected for Arc-enabled SQL Servers": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for Storage plan should be enabled with Malware Scanning and Sensitive Data Threat Detection": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for open-source relational databases should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for servers should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Microsoft Defender for servers should be enabled on workspaces": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Network Watcher should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "New high privileges role detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "No Azure Policy To Control AI Services": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "No Guardrail Found In AI Agents": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Non-internet-facing virtual machines should be protected with network security groups": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment", "is very active"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Overfitting Due To Lack Of Validation Data": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Overfitting For Training Affects Trustworthy AI": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Possible data download via DNS tunnel": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["SensitiveDataExposed"]}, "Private endpoint connections on Azure SQL Database should be enabled": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability", "OpenToInternet"]}, "Private endpoint should be enabled for PostgreSQL servers": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["OpenToInternet"]}, "Privileged container detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Prompt Moderation Controls Are Removed Or Reduced In Efficacy": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Resource logs in Azure Databricks Workspaces should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Resource logs in Azure Machine Learning Workspaces should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Resource logs in Search services should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Role binding to the cluster-admin role detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Secure transfer to storage accounts should be enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Security incident detected on multiple resources": {"HighestRiskLevel": "Critical", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Malware"]}, "Storage account should use a private link connection": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Storage accounts should prevent shared key access": {"HighestRiskLevel": "High", "LowestRiskLevel": "Low", "HighestRiskFactors": ["is externally expose", "is owned by an ex employee"], "LowestRiskFactors": ["belongs to development environment"], "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Storage accounts should restrict network access using virtual network rules": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "OpenToInternet"]}, "Subnets should be associated with a network security group": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Subscriptions should have a contact email address for security issues": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Suspicious User Agent detected": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Suspicious authentication activity": {"HighestRiskLevel": "", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["is owned by an ex employee"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "System updates should be installed on your machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "System updates should be installed on your machines (powered by Azure Update Manager)": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "TLS should be updated to the latest version for function apps": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "The Log Analytics extension should be installed on Virtual Machine Scale Sets": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "There should be more than one owner assigned to subscriptions": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": true, "IncreaseRiskCriteria": ["belongs to production environment"], "Categories": ["Governance"]}, "Traffic detected from IP addresses recommended for blocking": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Unaddressed Failed ML Pipelines": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "Low", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": ["belongs to development environment"], "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Unauthenticated access to a storage blob container": {"HighestRiskLevel": "High", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability", "OpenToInternet"]}, "Unusual execution of custom script extension in your virtual machine": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Vulnerability"]}, "Unusual number of failed sign-in attempts": {"HighestRiskLevel": "", "LowestRiskLevel": "Medium", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Virtual machines and virtual machine scale sets should have encryption at host enabled": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Virtual machines should have the Log Analytics extension installed": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Vulnerabilities in security configuration on your Linux machines should be remediated (powered by Guest Configuration)": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Vulnerabilities in security configuration on your Windows machines should be remediated (powered by Guest Configuration)": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Web server configuration with insufficient monitoring": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Windows Defender Exploit Guard should be enabled on machines": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Windows Defender Exploit Gurd should be enabled on machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "Windows servers should be configured to use secure communication protocols": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData"]}, "Windows virtual machines should enable Azure Disk Encryption or EncryptionAtHost.": {"HighestRiskLevel": "", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance", "SensitiveData"]}, "[Deprecated]: Azure Defender for DNS should be enabled": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "[Enable if required] Azure Cosmos DB accounts should use customer-managed keys to encrypt data at rest": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "[Enable if required] Storage accounts should use customer-managed key (CMK) for encryption": {"HighestRiskLevel": "Medium", "LowestRiskLevel": "", "HighestRiskFactors": ["belongs to production environment"], "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "[Preview]: Log Analytics extension should be installed on your Linux Azure Arc machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}, "[Preview]: Log Analytics extension should be installed on your Windows Azure Arc machines": {"HighestRiskLevel": "Low", "LowestRiskLevel": "", "HighestRiskFactors": null, "LowestRiskFactors": null, "IncreaseRisk": false, "IncreaseRiskCriteria": null, "Categories": ["Governance"]}}