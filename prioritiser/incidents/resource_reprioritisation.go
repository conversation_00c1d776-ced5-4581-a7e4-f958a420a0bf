package incidents

import (
	"slices"

	"github.com/precize/common"
)

func ReprioritiseBasedOnResource(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {

	var (
		serviceID                              string
		impactResourceTypes                    []string
		impactResourceTypeWithModerateActivity []string
	)
	// Prioritise Based on Encryption
	reprioritiseBasedOnEncryption(pData)

	// Prioritise Based on Hero Stats
	ReprioritiseBasedOnHeroStats(pData, crsDoc)

	if !pData.IsSignificantResource {

		for _, serviceIDInt := range crsDoc.ServiceID {

			switch serviceIDInt {
			case common.AWS_SERVICE_ID_INT:
				impactResourceTypes = common.AWSImpactResource
				impactResourceTypeWithModerateActivity = AWSImpactResourceWithLessActivity
				serviceID = common.AWS_SERVICE_ID
			case common.AZURE_SERVICE_ID_INT:
				impactResourceTypes = common.AZUREImpactResource
				impactResourceTypeWithModerateActivity = AZUREImpactResourceWithLessActivity
				serviceID = common.AZURE_SERVICE_ID
			case common.GCP_SERVICE_ID_INT:
				impactResourceTypes = common.GCPImpactResource
				impactResourceTypeWithModerateActivity = GCPImpactResourceWithLessActivity
				serviceID = common.GCP_SERVICE_ID
			}

			if len(serviceID) > 0 {
				break
			}
		}
	}

	if slices.Contains(impactResourceTypes, pData.EntityType) {
		// Impactful resource
		if pData.IsResourceProd {
			if (pData.resourceValues.activitiesCount >= 50 && pData.OwnerCount > 3) || slices.Contains(impactResourceTypeWithModerateActivity, pData.EntityType) {
				// Impactful resource with high activity, owner count
				SetImpact(pData.PrecizeImpact, common.SIGNIFICANT_IMPACT, IMPACTFULRESOURCE_CRITERIA, pData, true)
			} else {
				SetImpact(pData.PrecizeImpact, common.MODERATE_IMPACT, MODERATEIMPACTFULRESOURCE_CRITERIA, pData, true)
			}
		} else {
			SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT, NOTIMPACTFULRESOURCE_CRITERIA, pData, false)
		}

		pData.IsSignificantResource = true
	} else {
		// Not an Impactful resource
		if pData.IsResourceProd && (pData.OpenToInternet || pData.SensitiveData) {
			DecreaseImpactByOneLevel(pData.PrecizeImpact, NOTIMPACTFULRESOURCE_CRITERIA, pData, false)
		} else {
			SetImpact(pData.PrecizeImpact, common.MINOR_IMPACT, NOTIMPACTFULRESOURCE_CRITERIA, pData, false)
		}
	}

}

func reprioritiseBasedOnEncryption(pData *PrioritisationData) {

	encryptionHeroStats := map[string]any{
		"dataIssues":                   struct{}{},
		"EncryptionStatus":             struct{}{},
		"encryptionDisabledAIService":  struct{}{},
		"insecureData":                 struct{}{},
		"cloudStorageEncryptionStatus": struct{}{},
	}

	for _, hsKey := range pData.heroStats {
		if _, ok := encryptionHeroStats[hsKey]; ok && pData.OpenToInternet {
			// Data is not encrypted and open to internet
			SetImpact(pData.PrecizeImpact, common.SIGNIFICANT_IMPACT, OPENTOINTERNET_NOT_ENCRYPTED_CRITERIA, pData, true)
			return
		}
	}

}

func ReprioritiseBasedOnHeroStats(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc) {
	// reprioritise based on precize identified critical hero stats
	criticalHeroStatsCount := 0
	for _, hsKey := range pData.heroStats {
		if _, ok := pData.criticalHeroStats[hsKey]; ok {
			criticalHeroStatsCount++
		}
	}

	if criticalHeroStatsCount > 4 {
		IncreaseLikelihoodByOneLevel(pData.PrecizeLikelihood, PRECIZE_HERO_STATS_CRITERIA, pData, true)
	} else if _, ok := parentRscEntityType[crsDoc.EntityType]; ok {
		var (
			totalWeight float32
			weightedSum float32
		)

		for _, heroStat := range crsDoc.Herostat {
			if severity, exists := parentHsSeverity[heroStat]; exists {
				if score, ok := riskScoreMap[severity]; ok {
					weightedSum += score
					totalWeight += 10.0
				}
			}
		}

		if totalWeight > 0 {
			averageRisk := weightedSum / totalWeight * 10.0

			switch {
			case averageRisk >= 8.0:
				SetLikelihood(pData.PrecizeLikelihood, common.VERY_LIKELY_LIKELIHOOD, PRECIZE_HERO_STATS_CRITERIA, pData, true)
			case averageRisk >= 6.0:
				SetLikelihood(pData.PrecizeLikelihood, common.LIKELY_LIKELIHOOD, PRECIZE_HERO_STATS_CRITERIA, pData, true)
			case averageRisk >= 4.0:
				SetLikelihood(pData.PrecizeLikelihood, common.POSSIBLE_LIKELIHOOD, PRECIZE_HERO_STATS_CRITERIA, pData, true)
			case averageRisk >= 2.0:
				SetLikelihood(pData.PrecizeLikelihood, common.UNLIKELY_LIKELIHOOD, PRECIZE_HERO_STATS_CRITERIA, pData, true)
			default:
				SetLikelihood(pData.PrecizeLikelihood, common.VERY_UNLIKELY_LIKELIHOOD, PRECIZE_HERO_STATS_CRITERIA, pData, true)
			}
		}
	}
}
