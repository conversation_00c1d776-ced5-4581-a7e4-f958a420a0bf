package incidents

import (
	"encoding/json"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/logger"
)

type AccountRiskMap struct {
	mu   sync.Mutex
	data map[string]float32
}

func (s *AccountRiskMap) Update(key string, newScore float32) {
	s.mu.Lock()
	defer s.mu.Unlock()

	weight := getWeight(newScore)
	if currentScore, exists := s.data[key]; exists {
		currentWeight := getWeight(currentScore)
		totalWeight := currentWeight + weight

		s.data[key] = ((currentScore * currentWeight) + (newScore * weight)) / totalWeight
	} else {
		s.data[key] = newScore
	}
}

func getWeight(score float32) float32 {
	switch score {
	case riskScoreMap[common.LOW_RISK]:
		return 1.0
	case riskScoreMap[common.MEDIUM_RISK]:
		return 2.5
	case riskScoreMap[common.HIGH_RISK]:
		return 4.0
	case riskScoreMap[common.CRITICAL_RISK]:
		return 5.0
	default:
		return 1.0
	}
}

func (s *AccountRiskMap) Get(key string) (float32, bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	val, exists := s.data[key]
	return val, exists
}

func EvaluateResourcesRisk(tenantID, lastCollectedAt, serviceID string, criticalHeroStats map[string]any) {

	var (
		criteria Criteria
	)

	accountIds, err := GetCrsChildResourcesAccountIds(tenantID, serviceID)
	if err != nil {
		return
	}

	limitConcurrency := make(chan struct{}, 8)
	accountRiskMap := &AccountRiskMap{
		data: make(map[string]float32),
	}

	var wg sync.WaitGroup

	for _, accountID := range accountIds {
		wg.Add(1)
		limitConcurrency <- struct{}{}

		go func(accountID string) {
			defer func() {
				<-limitConcurrency
				wg.Done()
			}()

			EvaluateAccountResourcesRisk(accountID, tenantID, lastCollectedAt, serviceID, criticalHeroStats, &criteria, accountRiskMap)

		}(accountID)
	}

	wg.Wait()

	logger.Print(logger.INFO, "Starting parent risk evaluation", []string{tenantID})

	switch serviceID {
	case "1000":
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "account", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "OrganisationUnit", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "organizations", &criteria, accountRiskMap)
	case "2000":
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "ResourceGroup", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "Subscription", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "MgtGroup", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "Tenant", &criteria, accountRiskMap)
	case "3000":
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "Project", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "Folder", &criteria, accountRiskMap)
		EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, "Organization", &criteria, accountRiskMap)
	}

	logger.Print(logger.INFO, "Finished parent risk evaluation", []string{tenantID})

}

func GetCrsChildResourcesAccountIds(tenantID, serviceID string) (accountIds []string, err error) {

	var entityType string
	switch serviceID {
	case "1000":
		entityType = "account"
	case "3000":
		entityType = "Project"
	}

	if serviceID == "2000" {

		// azure will have lower case in entityId so extracting from resourceGroup field
		crsQuery := `{"query":{"bool":{"must_not":[],"must":[{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"match":{"serviceId":"` + serviceID + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}}]}},"size":"0","from":0,"aggs":{"rg_aggregation":{"terms":{"field":"resourceGroup.keyword","size":10000}}}}`
		rgAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery)
		if err != nil {
			return nil, err
		}

		if rgAggs, ok := rgAggsResp["rg_aggregation"].(map[string]any); ok {
			if rgBuckets, ok := rgAggs["buckets"].([]any); ok {
				for _, rgBucket := range rgBuckets {
					if rgBucketMap, ok := rgBucket.(map[string]any); ok {
						if rg, ok := rgBucketMap["key"].(string); ok {
							accountIds = append(accountIds, rg)
						}
					}
				}
			}
		}

	} else {

		var (
			searchAfter       any
			cloudAccountQuery = `{"_source":["entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"term":{"serviceId":"` + serviceID + `"}},{"match":{"entityType.keyword":"` + entityType + `"}}]}},"size":"10000","from":0}`
		)

		for {
			cloudStoreDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, cloudAccountQuery, searchAfter)
			if err != nil {
				return nil, err
			}

			if len(cloudStoreDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for _, cloudStoreDoc := range cloudStoreDocs {
				if aId, ok := cloudStoreDoc["entityId"].(string); ok {
					accountIds = append(accountIds, aId)
				}
			}
		}
	}

	return
}

func EvaluateAccountResourcesRisk(accountID, tenantID, lastCollectedAt, serviceID string, criticalHeroStats map[string]any, criteria *Criteria, accountRiskMap *AccountRiskMap) {

	var (
		searchAfter                      any
		resourceToExternalIncidentsCount = make(map[string]int64)
		crsQuery                         = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + accountID + `"}}]` + excludeVirtualResourcesQuery + `}}}`
		incidentQuery                    = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + accountID + `"}},{"bool":{"should":[{"match":{"precizeRisk.keyword":"High"}},{"match":{"precizeRisk.keyword":"Critical"}}],"must_not":{"term":{"precizeRisk.keyword":""}}}}]}},"size":"0","aggs":{"rsc_aggregation":{"terms":{"field":"entityId.keyword","size":10000}}}}`
	)

	if serviceID == "2000" {
		// to resolve case sensitivity issue for resource groups
		crsQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"resourceGroup.keyword":"` + accountID + `"}}]` + excludeVirtualResourcesQuery + `}}}`
	}

	logger.Print(logger.INFO, "Starting resource risk evaluation for account", []string{tenantID}, accountID)

	cloudIncidentsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_INCIDENTS_INDEX}, incidentQuery)
	if err != nil {
		return
	}

	if incidentsAggs, ok := cloudIncidentsResp["rsc_aggregation"].(map[string]any); ok {
		if incidentsBuckets, ok := incidentsAggs["buckets"].([]any); ok {
			for _, incidentsBucket := range incidentsBuckets {
				if incidentsBucketMap, ok := incidentsBucket.(map[string]any); ok {
					if entID, ok := incidentsBucketMap["key"].(string); ok {
						if count, ok := incidentsBucketMap["doc_count"].(float64); ok {
							resourceToExternalIncidentsCount[entID] = int64(count)
						}
					}
				}
			}
		}
	}

	var (
		bulkUpdateCRSQuery, bulkUpdateCRQuery string
		currentCount                          int
		crsToCRDocIDMap                       = make(map[string]string)
	)

	for {
		cloudStoreDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(cloudStoreDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, cloudStoreDoc := range cloudStoreDocs {
			var crsDoc common.CloudResourceStoreDoc
			b, err := json.Marshal(cloudStoreDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				continue
			}

			if err = json.Unmarshal(b, &crsDoc); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				continue
			}

			// Add escape characters if '\' are present
			if strings.Contains(crsDoc.EntityID, `\`) {
				crsDoc.EntityID = common.EscapeString(crsDoc.EntityID)
			}

			// ResourceGroup should not be evaluated during account resources risk eval
			if crsDoc.EntityType == "ResourceGroup" {
				continue
			}

			var pData = PrioritisationData{
				PrecizeLikelihood:            likelihoodStringToNumber[common.VERY_UNLIKELY_LIKELIHOOD],
				PrecizeImpact:                impactStringToNumber[common.NEGLIGIBLE_IMPACT],
				OpenToInternet:               false,
				TenantID:                     tenantID,
				PriorityCriteria:             make(map[string]bool),
				IsResourceProd:               false,
				EntityType:                   crsDoc.EntityType,
				criticalHeroStats:            criticalHeroStats,
				resourceExternalIncidentsMap: resourceToExternalIncidentsCount,
				IsResourceReprioritisation:   true,
			}

			var impactResourceTypes []string

			for _, serviceIDInt := range crsDoc.ServiceID {

				switch serviceIDInt {
				case common.AWS_SERVICE_ID_INT:
					impactResourceTypes = common.AWSImpactResource
					serviceID = common.AWS_SERVICE_ID
				case common.AZURE_SERVICE_ID_INT:
					impactResourceTypes = common.AZUREImpactResource
					serviceID = common.AZURE_SERVICE_ID
				case common.GCP_SERVICE_ID_INT:
					impactResourceTypes = common.GCPImpactResource
					serviceID = common.GCP_SERVICE_ID
				}

				if len(serviceID) > 0 {
					break
				}
			}

			if slices.Contains(impactResourceTypes, pData.EntityType) {
				pData.IsSignificantResource = true
				pData.PrecizeLikelihood = likelihoodStringToNumber[common.POSSIBLE_LIKELIHOOD]
				pData.PrecizeImpact = impactStringToNumber[common.MODERATE_IMPACT]
			}

			EvaluateResourceRisk(tenantID, accountID, lastCollectedAt, crsDoc, criteria, pData, &bulkUpdateCRSQuery, &bulkUpdateCRQuery, &currentCount, accountRiskMap, crsToCRDocIDMap)
		}
	}

	if currentCount > 0 {

		if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, bulkUpdateCRSQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})

		if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkUpdateCRQuery); err != nil {
			logger.Print(logger.INFO, "Crs to cr Doc map", crsToCRDocIDMap)
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	logger.Print(logger.INFO, "Finished resource risk evaluation for account", []string{tenantID}, accountID)

}

func EvaluateResourceRisk(tenantID, accountID, lastCollectedAt string, crsDoc common.CloudResourceStoreDoc, criteria *Criteria, pData PrioritisationData, bulkUpdateCRSQuery *string, bulkUpdateCRQuery *string, currentCount *int, accountRiskMap *AccountRiskMap, crsToCRDocIDMap map[string]string) (err error) {

	err = ResourceRiskCriterias(&pData, crsDoc, criteria)
	if err != nil {
		return
	}

	priorityCriteria := make([]string, 0)
	riskLevel := ""

	for criteria := range pData.PriorityCriteria {
		if len(criteria) > 0 {
			priorityCriteria = append(priorityCriteria, criteria)
		}
	}

	likelihood := likelihoodNumberToString[float32(CustomRound(float64(pData.PrecizeLikelihood), 0.65))]
	impact := impactNumberToString[float32(CustomRound(float64(pData.PrecizeImpact), 0.65))]

	if len(pData.PrecizeRisk) <= 0 {
		riskLevel, _ = GetRiskDetails(likelihood, impact)
	} else {
		riskLevel = pData.PrecizeRisk
	}

	if riskStatus, ok := riskLevelMap[riskLevel]; ok {
		riskScore := riskScoreMap[riskLevel]

		cloudResourceStoreUpdateMetadata := `{"update": {"_id": "` + crsDoc.ID + `"}}`
		cloudResourceStoreUpdateDoc := `{"doc":{"riskScore":` + fmt.Sprintf("%.2f", riskScore) + `, "riskStatus":` + fmt.Sprint(riskStatus) + `,"priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
			`"]}}`
		*bulkUpdateCRSQuery = *bulkUpdateCRSQuery + cloudResourceStoreUpdateMetadata + "\n" + cloudResourceStoreUpdateDoc + "\n"

		switch crsDoc.EntityType {
		case common.IPDETAILS_RESOURCE_TYPE, common.APP_RESOURCE_TYPE, common.AWS_AISAGEMAKERMODELTYPE_RESOURCE_TYPE:
		default:
			crDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, lastCollectedAt, strings.ToLower(crsDoc.AccountID), strings.ToLower(crsDoc.EntityID), crsDoc.EntityType)
			logger.Print(logger.INFO, "Updating cloud resource", []string{tenantID}, crDocID, crsDoc.ID)

			crsToCRDocIDMap[crsDoc.ID] = crDocID
			cloudResourceUpdateMetadata := `{"update": {"_id": "` + crDocID + `"}}`

			cloudResourceUpdateDoc := `{"doc":{"riskScore":` + fmt.Sprintf("%.2f", riskScore) + `, "riskStatus":` + fmt.Sprint(riskStatus) + `,"priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
				`"]}}`
			*bulkUpdateCRQuery = *bulkUpdateCRQuery + cloudResourceUpdateMetadata + "\n" + cloudResourceUpdateDoc + "\n"
		}

		*currentCount++

		if pData.IsSignificantResource {
			// contribute to account risk only if it is a significant resource
			accountRiskMap.Update(accountID, riskScore)
		}

		if *currentCount >= MAX_RECORDS {

			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, *bulkUpdateCRSQuery); err != nil {
				return
			}

			logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})

			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, *bulkUpdateCRQuery); err != nil {
				logger.Print(logger.INFO, "Crs to cr Doc map", crsToCRDocIDMap)
				return
			}

			logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})
			*currentCount = 0
			*bulkUpdateCRSQuery = ""
			*bulkUpdateCRQuery = ""
		}
	}

	return
}

func ResourceRiskCriterias(pData *PrioritisationData, crsDoc common.CloudResourceStoreDoc, criteria *Criteria) (err error) {
	pData.tenantValues, pData.accountValues, pData.resourceValues, err = fetchCriteriaValues(criteria, crsDoc)
	if err != nil {
		return
	}

	if _, ok := identityEntityType[crsDoc.EntityType]; ok {
		ReprioritiseBasedOnIdentity(pData, crsDoc)
	} else {

		for _, sensitivity := range crsDoc.Sensitivity {
			if strings.Contains(sensitivity, context.PII_SENSITIVITY) || strings.Contains(sensitivity, context.PCI_SENSITIVITY) ||
				strings.Contains(sensitivity, context.PHI_SENSITIVITY) || strings.Contains(sensitivity, context.CONFIDENTIAL_SENSITIVITY) || strings.Contains(sensitivity, context.RESTRICTED_SENSITIVITY) {
				pData.SensitiveData = true
			}
		}

		IsResourceOpenToInternet(pData)

		ReprioritiseBasedOnEnv(pData, crsDoc)

		if pData.OpenToInternet {
			if pData.IsResourceProd {
				SetLikelihoodWithoutAvg(common.VERY_LIKELY_LIKELIHOOD, OPENTOINTERNET_CRITERIA, pData, true)
			} else {
				SetLikelihoodWithoutAvg(common.LIKELY_LIKELIHOOD, OPENTOINTERNET_CRITERIA, pData, true)
			}
		}
		ReprioritiseBasedOnOwner(pData, crsDoc)

		ReprioritiseBasedOnResource(pData, crsDoc)

		if pData.SensitiveData {
			if pData.OpenToInternet || (pData.IsResourceProd && pData.PriorityCriteria[EXEMPLOYEE_CRITERIA]) {
				SetImpactWithoutAvg(common.SEVERE_IMPACT, SENSITIVEDATA_OPENTOINTERNET_CRITERIA, pData, true)
			} else {
				SetImpactWithoutAvg(common.SIGNIFICANT_IMPACT, SENSITIVEDATA_CRITERIA, pData, true)
			}
		}

		criticalHeroStatsCount := 0
		for _, hsKey := range pData.heroStats {
			if _, ok := pData.criticalHeroStats[hsKey]; ok {
				criticalHeroStatsCount++
			}
		}

		//if less precize critical herostats decrease likelihood
		if criticalHeroStatsCount < 2 && pData.PrecizeLikelihood > 3 {
			DecreaseLikelihoodByOneLevel(pData.PrecizeLikelihood, PRECIZE_HERO_STATS_CRITERIA, pData, false)
		}

		ReprioritiseBasedOnExternalIncidents(pData, crsDoc)
	}

	return
}

func EvaluateParentRisk(tenantID, serviceID, lastCollectedAt, entityType string, criteria *Criteria, accountRiskMap *AccountRiskMap) {

	var (
		crsQuery                         = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"entityType.keyword":"` + entityType + `"}}]}},"size":"10000","from":0}`
		bulkUpdateCRSQuery               string
		bulkUpdateCRQuery                string
		currentCount                     int
		searchAfter                      any
		resourceToExternalIncidentsCount = make(map[string]int64)
		incidentQuery                    = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"entityType.keyword":"` + entityType + `"}},{"bool":{"should":[{"match":{"precizeRisk.keyword":"High"}},{"match":{"precizeRisk.keyword":"Critical"}}],"must_not":{"term":{"precizeRisk.keyword":""}}}}]}},"size":"0","aggs":{"rsc_aggregation":{"terms":{"field":"entityId.keyword","size":10000}}}}`
	)

	cloudIncidentsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_INCIDENTS_INDEX}, incidentQuery)
	if err != nil {
		return
	}

	if incidentsAggs, ok := cloudIncidentsResp["rsc_aggregation"].(map[string]any); ok {
		if incidentsBuckets, ok := incidentsAggs["buckets"].([]any); ok {
			for _, incidentsBucket := range incidentsBuckets {
				if incidentsBucketMap, ok := incidentsBucket.(map[string]any); ok {
					if entID, ok := incidentsBucketMap["key"].(string); ok {
						if count, ok := incidentsBucketMap["doc_count"].(float64); ok {
							resourceToExternalIncidentsCount[entID] = int64(count)
						}
					}
				}
			}
		}
	}

	for {
		cloudStoreDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(cloudStoreDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, cloudStoreDoc := range cloudStoreDocs {
			var crsDoc common.CloudResourceStoreDoc
			b, err := json.Marshal(cloudStoreDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				continue
			}

			if err = json.Unmarshal(b, &crsDoc); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				continue
			}

			var pData = PrioritisationData{
				PrecizeLikelihood:            likelihoodStringToNumber[common.VERY_UNLIKELY_LIKELIHOOD],
				PrecizeImpact:                impactStringToNumber[common.NEGLIGIBLE_IMPACT],
				OpenToInternet:               false,
				TenantID:                     tenantID,
				PriorityCriteria:             make(map[string]bool),
				IsResourceProd:               false,
				EntityType:                   crsDoc.EntityType,
				resourceExternalIncidentsMap: resourceToExternalIncidentsCount,
				IsResourceReprioritisation:   true,
			}

			AccountRiskCriteria(tenantID, crsDoc.AccountID, lastCollectedAt, crsDoc, criteria, pData, &bulkUpdateCRSQuery, &bulkUpdateCRQuery, &currentCount, accountRiskMap)

			var (
				searchAfter                any
				crsQueryForParentResources string
			)

			switch crsDoc.EntityType {
			case common.AZURE_TENANT_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.AZURE_TENANT_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.AZURE_MGMTGRP_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.AZURE_RG_RESOURCE_TYPE + `"}},{"wildcard":{"resourceGroup.keyword":"*"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.GCP_ORG_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.GCP_ORG_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.GCP_FOLDER_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.GCP_PROJECT_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.GCP_FOLDER_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.GCP_FOLDER_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.GCP_PROJECT_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.AWS_ORG_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.AWS_ORG_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AWS_ACCOUNT_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			case common.AWS_ORGUNIT_RESOURCE_TYPE:
				crsQueryForParentResources = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"isDeleted":"false"}},{"match":{"accountId.keyword":"` + crsDoc.EntityID + `"}}],"must_not":[{"match":{"entityType.keyword":"` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"}},{"match":{"entityType.keyword":"` + common.AWS_ACCOUNT_RESOURCE_TYPE + `"}},` + excludeVirtualResourcesEntTypeQuery + `]}}}`
			}

			if len(crsQueryForParentResources) > 0 {

				for {
					cloudStoreDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQueryForParentResources, searchAfter)
					if err != nil {
						return
					}

					if len(cloudStoreDocs) > 0 {
						searchAfter = sortResponse
					} else {
						break
					}

					for _, cloudStoreDoc := range cloudStoreDocs {
						var crsDoc common.CloudResourceStoreDoc
						b, err := json.Marshal(cloudStoreDoc)
						if err != nil {
							logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
							continue
						}

						if err = json.Unmarshal(b, &crsDoc); err != nil {
							logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
							continue
						}

						var pData = PrioritisationData{
							PrecizeLikelihood:            likelihoodStringToNumber[common.VERY_UNLIKELY_LIKELIHOOD],
							PrecizeImpact:                impactStringToNumber[common.NEGLIGIBLE_IMPACT],
							OpenToInternet:               false,
							TenantID:                     tenantID,
							PriorityCriteria:             make(map[string]bool),
							IsResourceProd:               false,
							EntityType:                   crsDoc.EntityType,
							resourceExternalIncidentsMap: resourceToExternalIncidentsCount,
							IsResourceReprioritisation:   true,
						}

						AccountRiskCriteria(tenantID, crsDoc.AccountID, lastCollectedAt, crsDoc, criteria, pData, &bulkUpdateCRSQuery, &bulkUpdateCRQuery, &currentCount, accountRiskMap)
					}
				}
			}
		}
	}

	if currentCount > 0 {

		if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, bulkUpdateCRSQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})

		if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkUpdateCRQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}
}

func AccountRiskCriteria(tenantID, accountID, lastCollectedAt string, crsDoc common.CloudResourceStoreDoc, criteria *Criteria, pData PrioritisationData, bulkUpdateCRSQuery *string, bulkUpdateCRQuery *string, currentCount *int, accountRiskMap *AccountRiskMap) (err error) {

	err = ResourceRiskCriterias(&pData, crsDoc, criteria)
	if err != nil {
		return
	}

	priorityCriteria := make([]string, 0)
	riskLevel := ""

	for criteria := range pData.PriorityCriteria {
		priorityCriteria = append(priorityCriteria, criteria)
	}

	likelihood := likelihoodNumberToString[float32(CustomRound(float64(pData.PrecizeLikelihood), 0.65))]
	impact := impactNumberToString[float32(CustomRound(float64(pData.PrecizeImpact), 0.65))]

	if len(pData.PrecizeRisk) <= 0 {
		riskLevel, _ = GetRiskDetails(likelihood, impact)
	} else {
		riskLevel = pData.PrecizeRisk
	}

	riskScore := riskScoreMap[riskLevel]

	switch crsDoc.EntityType {
	case common.AWS_ACCOUNT_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE, common.AWS_ORG_RESOURCE_TYPE, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE:
		if accountRisk, ok := accountRiskMap.Get(crsDoc.EntityID); ok {
			riskScore = float32(math.Ceil(float64(riskScore+accountRisk) / 2.0))
		} else if crsDoc.EntityType == "ResourceGroup" {
			tempEntID := strings.Replace(crsDoc.EntityID, "/resourcegroups/", "/resourceGroups/", 1)
			if accountRisk, ok := accountRiskMap.Get(tempEntID); ok {
				riskScore = float32(math.Ceil(float64(riskScore+accountRisk) / 2.0))
			}
		}

		if crsDoc.EntityType == "account" || crsDoc.EntityType == "Project" || crsDoc.EntityType == "ResourceGroup" {
			// prod env account can have minimum medium risk
			if pData.PriorityCriteria[PRODENV_CRITERIA] && riskScore < 5 {
				riskScore = 5
			}
		} else {
			// for parent resources minimum risk be medium
			if riskScore < 5 {
				riskScore = 5
			}
		}
	}

	riskLevel = GetRiskLevel(riskScore)
	if riskStatus, ok := riskLevelMap[riskLevel]; ok {
		riskScore := riskScoreMap[riskLevel]

		cloudResourceStoreUpdateMetadata := `{"update": {"_id": "` + crsDoc.ID + `"}}`
		cloudResourfceStoreUpdateDoc := `{"doc":{"riskScore":` + fmt.Sprintf("%.2f", riskScore) + `, "riskStatus":` + fmt.Sprint(riskStatus) + `,"priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
			`"]}}`
		*bulkUpdateCRSQuery = *bulkUpdateCRSQuery + cloudResourceStoreUpdateMetadata + "\n" + cloudResourfceStoreUpdateDoc + "\n"

		crDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, lastCollectedAt, strings.ToLower(crsDoc.AccountID), strings.ToLower(crsDoc.EntityID), crsDoc.EntityType)
		cloudResourceUpdateMetadata := `{"update": {"_id": "` + crDocID + `"}}`
		cloudResourceUpdateDoc := `{"doc":{"riskScore":` + fmt.Sprintf("%.2f", riskScore) + `, "riskStatus":` + fmt.Sprint(riskStatus) + `,"priorityCriteria":["` + strings.Join(priorityCriteria, "\",\"") +
			`"]}}`
		*bulkUpdateCRQuery = *bulkUpdateCRQuery + cloudResourceUpdateMetadata + "\n" + cloudResourceUpdateDoc + "\n"

		*currentCount++

		accountRiskMap.Update(accountID, riskScore)

		if *currentCount >= MAX_RECORDS {

			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCE_STORE_INDEX, *bulkUpdateCRSQuery); err != nil {
				return
			}

			logger.Print(logger.INFO, "Cloud resource store bulk API Successful for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})

			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, *bulkUpdateCRQuery); err != nil {
				return
			}

			logger.Print(logger.INFO, "Cloud resources bulk API Successful for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})
			*bulkUpdateCRSQuery = ""
			*bulkUpdateCRQuery = ""
			*currentCount = 0
		}
	}

	return
}
