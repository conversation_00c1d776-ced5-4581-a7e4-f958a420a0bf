package aws

import (
	"context"
	"errors"
	"strconv"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/cloudformation"
	"github.com/google/uuid"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/logger"
)

type StackTemplateEvent struct {
	StackID   string `json:"stackId"`
	EventTime string `json:"eventTime"`
	Region    string `json:"region"`
	Account   string `json:"account"`
	TenantID  string `json:"tenantId"`
}

type StackTemplateDoc struct {
	StackTemplateEvent
	TemplateID        string                 `json:"templateId"`
	StackName         string                 `json:"stackName"`
	TemplateGitHash   string                 `json:"templateGithash"`
	StackCreationTime string                 `json:"stackCreationTime"`
	ResourceEvents    []common.ResourceEvent `json:"resourceEvents"`
}

func processStackTemplate(stackTemplateEvent StackTemplateEvent, cloudFormationClient *cloudformation.Client) error {

	describeStacksInput := cloudformation.DescribeStacksInput{
		StackName: &stackTemplateEvent.StackID,
	}

	describeStacks, err := cloudFormationClient.DescribeStacks(context.TODO(), &describeStacksInput, func(o *cloudformation.Options) {
		o.Region = stackTemplateEvent.Region
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error calling DescribeStacks", []string{
			stackTemplateEvent.TenantID, stackTemplateEvent.Account}, err)
		return err
	}

	if len(describeStacks.Stacks) != 1 {
		err = errors.New("DescribeStacks returned " + strconv.Itoa(len(describeStacks.Stacks)) + " stacks")
		logger.Print(logger.ERROR, "Got error getting stack", []string{
			stackTemplateEvent.TenantID, stackTemplateEvent.Account}, err)
		return err
	}

	stack := describeStacks.Stacks[0]
	// Some cloudtrail events give stack name instead of stack id, so making sure we use id.
	stackTemplateEvent.StackID = *stack.StackId

	gitHash, err := getTemplateGitHashOfStack(stackTemplateEvent, cloudFormationClient)
	if err != nil {
		return err
	}

	templateID := uuid.New().String()

	stackTemplateDoc := StackTemplateDoc{
		StackTemplateEvent: stackTemplateEvent,
		TemplateID:         templateID,
		StackName:          aws.ToString(stack.StackName),
		TemplateGitHash:    gitHash,
		StackCreationTime:  elastic.DateTime(aws.ToTime(stack.CreationTime)),
	}

	gitUsers, err := common.FetchGitFileAuthorsWithGitHash(stackTemplateEvent.TenantID, gitHash)
	if err != nil {
		logger.Print(logger.ERROR, "Got error fetching git file authors", err)
		return err
	}

	go func(stackTemplateDoc StackTemplateDoc, gitUsers []common.GitUser) {

		defer func() {
			if r := recover(); r != nil {
				logger.Print(logger.ERROR, "Panic occured", []string{
					stackTemplateDoc.TenantID, stackTemplateDoc.Account}, r)
				email.SendPanicEmail("provider")
			}
		}()

		processStackEvents(stackTemplateDoc, gitUsers, cloudFormationClient)

	}(stackTemplateDoc, gitUsers)

	return nil
}

func getTemplateGitHashOfStack(stackTemplateEvent StackTemplateEvent, cloudFormationClient *cloudformation.Client) (gitHash string, err error) {

	getTemplateInput := cloudformation.GetTemplateInput{
		StackName: &stackTemplateEvent.StackID,
	}

	template, err := cloudFormationClient.GetTemplate(context.TODO(), &getTemplateInput, func(o *cloudformation.Options) {
		o.Region = stackTemplateEvent.Region
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error calling GetTemplate", []string{
			stackTemplateEvent.TenantID, stackTemplateEvent.Account}, err)
		return
	}

	d1 := []byte(*template.TemplateBody)

	gitHash, err = common.GetFileGitHash(d1)
	if err != nil {
		logger.Print(logger.ERROR, "Got error generating file githash", err)
		return
	}

	return
}
