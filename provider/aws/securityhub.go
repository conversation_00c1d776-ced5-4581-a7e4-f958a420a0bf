package aws

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	provCommon "github.com/precize/provider/common"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	bufferTime = 24 * time.Hour
)

type secHubFindingsRequest struct {
	AccountID string             `json:"accountId"`
	Region    string             `json:"region"`
	NextToken string             `json:"nextToken"`
	Filters   securityHubFilters `json:"filters"`
}

type securityHubFilters struct {
	UpdatedAt   []rangeFilter `json:"updatedAt"`
	RecordState []valueFilter `json:"recordState"`
	IssueLabel  []valueFilter `json:"severityLabel"`
}

type valueFilter struct {
	Value      string `json:"value"`
	Comparison string `json:"comparison"`
}

type rangeFilter struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

type secHubFindingsResp struct {
	Status int        `json:"status"`
	Data   secHubData `json:"data"`
}

type secHubData struct {
	Findings  []secHubFinding `json:"findings"`
	NextToken string          `json:"nextToken"`
}

type secHubFinding struct {
	ID              string   `json:"id"`
	Region          string   `json:"region"`
	AWSAccountID    string   `json:"awsAccountId"`
	Types           []string `json:"types"`
	FirstObservedAt string   `json:"firstObservedAt"`
	LastObservedAt  string   `json:"lastObservedAt"`
	CreatedAt       string   `json:"createdAt"`
	UpdatedAt       string   `json:"updatedAt"`
	Severity        struct {
		Label string `json:"label"`
	} `json:"severity"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Workflow    struct {
		Status string `json:"status"`
	} `json:"workflow"`
	Remediation struct {
		Recommendation struct {
			Text string `json:"text"`
			URL  string `json:"url"`
		} `json:"recommendation"`
	} `json:"remediation"`
	SourceUrl   string           `json:"sourceUrl"`
	Resources   []secHubResource `json:"resources"`
	RecordState string           `json:"recordState"`

	//	Vulnerabilities
	//  Criticality
	//	Compliance
	//	Malware
	//	Threats
}

type secHubResource struct {
	Type string `json:"type"`
	ID   string `json:"id"`
}

func ProcessSecurityHubEvents(tenantID, envID string, awsAccountDetails tenant.AWSAccountDetails, secHubStartTime, secHubEndTime, tenantStartTime time.Time) {

	var (
		nextToken          string
		firstEverIteration bool
		unsupportedData    = provCommon.NewUnsupportedData()
	)

	if secHubEndTime.Sub(secHubStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.SECHUB_FINDINGS, tenantStartTime)

	if (secHubEndTime.Sub(secHubStartTime)) >= secHubEndTime.Sub(defaultTime) {
		secHubStartTime = defaultTime
		firstEverIteration = true
	}

	logger.Print(logger.INFO, "Fetching Security hub findings from "+common.DateTime(secHubStartTime)+" to "+common.DateTime(secHubEndTime), []string{tenantID, awsAccountDetails.AccountID})

	for _, region := range common.AWS_REGIONS {

		for {

			awsSecHubUrl := `/precize/private/aws/securityHubFindings/tenant/` + tenantID + `/environment/` + envID

			secHubReq := secHubFindingsRequest{
				AccountID: awsAccountDetails.AccountID,
				Region:    region,
				Filters: securityHubFilters{
					UpdatedAt: []rangeFilter{
						{
							Start: elastic.DateTime(secHubStartTime),
							End:   elastic.DateTime(secHubEndTime),
						},
					},
					IssueLabel: []valueFilter{
						{
							Comparison: "NOT_EQUALS",
							Value:      "INFORMATIONAL",
						},
						{
							Comparison: "NOT_EQUALS",
							Value:      "LOW",
						},
					},
				},
				NextToken: nextToken,
			}

			if firstEverIteration {
				secHubReq.Filters.RecordState = []valueFilter{
					{
						Comparison: "NOT_EQUALS",
						Value:      "ARCHIVED",
					},
				}
			}

			var (
				secHubFindingsBuf bytes.Buffer
				secHubResp        secHubFindingsResp
			)

			err := json.NewEncoder(&secHubFindingsBuf).Encode(secHubReq)
			if err != nil {
				logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
				break
			}

			resp, err := transport.SendRequestToServer("POST", awsSecHubUrl, nil, &secHubFindingsBuf)
			if err != nil {
				break
			}

			if err = json.Unmarshal(resp, &secHubResp); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				break
			}

			if err := insertSecHubEvents(secHubResp.Data.Findings, tenantID, secHubEndTime, firstEverIteration, unsupportedData); err != nil {
				break
			}

			if len(secHubResp.Data.NextToken) > 0 {
				nextToken = secHubResp.Data.NextToken
			} else {
				break
			}
		}
	}

	if len(unsupportedData.AssetTypes) > 0 {
		logger.Print(logger.ERROR, "Unsupported asset types", []string{tenantID}, unsupportedData.AssetTypes)
	}

	firstEverIteration = false

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.SECHUB_FINDINGS, secHubEndTime)
}

func insertSecHubEvents(secHubFindings []secHubFinding, tenantID string, insertTime time.Time, firstEverIteration bool, unsupportedData *provCommon.UnsupportedData) error {

	var (
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 10000
	)

	for _, finding := range secHubFindings {

		if firstEverIteration && finding.Workflow.Status == "RESOLVED" {
			continue
		}

		createdAt, err := time.Parse(time.RFC3339, finding.CreatedAt)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		updatedAt, err := time.Parse(time.RFC3339, finding.UpdatedAt)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		jsonBytes, err := json.Marshal(finding)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		var (
			serviceID          = common.AWS_SERVICE_ID_INT
			sourceRisk, status string
			additionalData     = map[string]any{}
		)

		additionalDataBytes, err := json.Marshal(additionalData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if sourceRisk = finding.Severity.Label; len(sourceRisk) > 0 {
			sourceRisk = common.ConvertToTitleCase(sourceRisk)
		} else {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		if _, ok := common.ValidRiskLevel[sourceRisk]; !ok {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		status = common.INCIDENT_STATUS_OPEN
		if finding.RecordState == "ARCHIVED" || finding.Workflow.Status == "RESOLVED" {
			status = common.INCIDENT_STATUS_RESOLVED
		}

		incidentDocID := common.GenerateCombinedHashID(tenantID, finding.ID)

		incident := common.Incident{
			ID:             incidentDocID,
			AlertID:        finding.ID,
			Issue:          finding.Title,
			AccountID:      finding.AWSAccountID,
			Source:         common.SECURITYHUB_SOURCE,
			IssueSeverity:  sourceRisk,
			SourceRisk:     sourceRisk,
			CreatedAt:      elastic.DateTime(createdAt),
			UpdatedAt:      elastic.DateTime(updatedAt),
			ServiceID:      serviceID,
			Category:       strings.Join(finding.Types, ", "),
			Description:    finding.Description,
			Status:         status,
			Stage:          "dc",
			TenantID:       tenantID,
			SourceJson:     string(jsonBytes),
			InsertTime:     elastic.DateTime(insertTime),
			AdditionalData: string(additionalDataBytes),
			IsIncident:     false,
		}

		incident.EntityID, incident.EntityType = getEntityIDAndTypeFromSecurityHubType(finding, tenantID, unsupportedData)

		if len(incident.EntityType) <= 0 {
			continue
		}

		incident.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), incident.AccountID, incident.EntityID, incident.EntityType)

		crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, incident.CrsID)
		if err != nil {
			continue
		}

		if len(crsDoc) > 0 {

			b, err := json.Marshal(crsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
				continue
			}

			var crs common.CloudResourceStoreDoc

			if err = json.Unmarshal(b, &crs); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				continue
			}

			incident.Environment = crs.Environment
			incident.Owner = crs.Owner
			incident.ResourceName = crs.ResourceName
			incident.AccountName = crs.AccountName
		}

		incidentBytes, _ := json.Marshal(incident)
		bulkInsertQuery = bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		bulkInsertQuery += string(incidentBytes) + "\n"

		currentCount++

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for AWS SecurityHub Findings for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for AWS SecurityHub Findings for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	return nil
}

func getEntityIDAndTypeFromSecurityHubType(finding secHubFinding, tenantID string, unsupportedData *provCommon.UnsupportedData) (entityID, entityType string) {

	var (
		secHubResourceID, secHubResourceType string
		entityIDChanged                      bool
	)

	if len(finding.Resources) > 0 {
		secHubResourceID = finding.Resources[0].ID
		secHubResourceType = finding.Resources[0].Type
	} else {
		return
	}

	switch secHubResourceType {
	case "AwsAccount":
		entityType = common.AWS_ACCOUNT_RESOURCE_TYPE
		idSplit := strings.Split(secHubResourceID, ":")
		if len(idSplit) > 0 {
			entityID = strings.ToLower(idSplit[len(idSplit)-1])
			entityIDChanged = true
		}
	case "AwsEc2Instance", "AwsSsmAssociationCompliance", "AwsSsmPatchCompliance":
		entityType = common.AWS_EC2_RESOURCE_TYPE
	case "AwsEc2Subnet":
		entityType = common.AWS_SUBNET_RESOURCE_TYPE
	case "AwsEc2Volume":
		entityType = common.AWS_EBSVOLUME_RESOURCE_TYPE
	case "AwsIamAccessKey":
		entityType = common.AWS_IAM_USER_RESOURCE_TYPE
	case "AwsEcrContainerImage":
		entityType = common.AWS_CONTAINERIMAGE_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsLambdaFunction":
		entityType = common.AWS_LAMBDA_RESOURCE_TYPE
		idSplit := strings.Split(secHubResourceID, ":")
		if len(idSplit) > 1 {
			entityID = strings.ToLower(idSplit[len(idSplit)-2])
			entityIDChanged = true
		}
	case "AwsAthenaWorkGroup":
		entityType = common.AWS_ATHENA_WORKGROUP_RESOURCE_TYPE
	case "AwsCertificateManagerCertificate":
		entityType = common.AWS_ACM_CERTIFICATE_RESOURCE_TYPE
	case "AwsCloudFrontDistribution":
		entityType = common.AWS_CLOUDFRONT_RESOURCE_TYPE
	case "AwsCloudTrailTrail":
		entityType = common.AWS_CLOUDTRAIL_TRAIL_RESOURCE_TYPE
	case "AwsDynamoDbTable":
		entityType = common.AWS_DYNAMODB_RESOURCE_TYPE
	case "AwsEc2LaunchTemplate":
		entityType = common.AWS_LAUNCHTEMPLATE_RESOURCE_TYPE
	case "AwsEc2NetworkAcl":
		entityType = common.AWS_NETWORKACL_RESOURCE_TYPE
	case "AwsEc2SecurityGroup":
		entityType = common.AWS_SG_RESOURCE_TYPE
	case "AwsEc2Vpc":
		entityType = common.AWS_VPC_RESOURCE_TYPE
	case "AwsEcsCluster":
		entityType = common.AWS_ECSCLUSTER_RESOURCE_TYPE
	case "AwsEcrRepository":
		entityType = common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsEcsService":
		entityType = common.AWS_ECSSERVICE_RESOURCE_TYPE
	case "AwsEcsTaskDefinition":
		entityType = common.AWS_ECSTASKDEFINITION_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsEksCluster":
		entityType = common.AWS_EKSCLUSTER_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsElbv2LoadBalancer":
		entityType = common.AWS_ELBAPP_RESOURCE_TYPE
	case "AwsIamPolicy":
		entityType = common.AWS_IAMPOLICY_RESOURCE_TYPE
	case "AwsIamRole":
		entityType = common.AWS_IAM_ROLE_RESOURCE_TYPE
	case "AwsIamUser":
		entityType = common.AWS_IAM_USER_RESOURCE_TYPE
	case "AwsKmsKey":
		entityType = common.AWS_KMS_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsElbLoadBalancer":
		entityType = common.AWS_ELB_RESOURCE_TYPE
	case "AwsRdsDbInstance":
		entityType = common.AWS_RDS_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsRdsDbSnapshot":
		entityType = common.AWS_RDSSNAPSHOT_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsRoute53HostedZone":
		entityType = common.AWS_ROUTE53_RESOURCE_TYPE
	case "AwsS3Bucket":
		entityType = common.AWS_S3_RESOURCE_TYPE
	case "AwsSecretsManagerSecret":
		entityType = common.AWS_SECRETSMANAGER_RESOURCE_TYPE
	case "AwsOpenSearchServiceDomain":
		entityType = common.AWS_ELASTICSEARCH_RESOURCE_TYPE
		arn := strings.ToLower(secHubResourceID)
		parts := strings.Split(arn, ":")
		if len(parts) >= 6 {
			domainParts := strings.Split(parts[5], "/")
			if len(domainParts) >= 2 {
				entityID = parts[4] + "/" + domainParts[1]
			}
		}
	case "AwsSnsTopic":
		entityType = common.AWS_SNS_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsBackupRecoveryPoint":
		entityType = common.AWS_AMI_RESOURCE_TYPE
	case "AwsEc2NetworkInterface":
		entityType = common.NETWORKINTERFACE_RESOURCE_TYPE
	case "AwsRdsDbCluster":
		entityType = common.AWS_RDSCLUSTER_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsApiGatewayV2Route", "AwsApiGatewayV2Stage":
		entityType = common.AWS_APIGATEWAY_RESOURCE_TYPE
		arn := strings.ToLower(secHubResourceID)
		parts := strings.Split(arn, "/apis/")
		if len(parts) >= 2 {
			apiParts := strings.Split(parts[1], "/")
			entityID = apiParts[0]
		}
	case "AwsCloudFormationStack":
		entityType = common.AWS_CFTSTACK_RESOURCE_TYPE
		entityID = strings.ToLower(secHubResourceID)
		entityIDChanged = true
	case "AwsEc2RouteTable":
		entityType = common.AWS_ROUTETABLE_RESOURCE_TYPE
	case "AwsCognitoUserPool":
		entityType = common.AWS_COGNITOUSERPOOL_RESOURCE_TYPE
	case "AwsSageMakerModel":
		entityType = common.AWS_AISAGEMAKERMODELTYPE_RESOURCE_TYPE
		arn := strings.ToLower(secHubResourceID)
		parts := strings.Split(arn, ":")
		if len(parts) >= 6 {
			accountID := parts[4]
			modelName := strings.Split(parts[5], "/")[1]
			entityID = fmt.Sprintf("/accounts/%s/models/%s", accountID, modelName)
		}
	case "AwsSageMakerNotebookInstance":
		entityType = common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE
	case "Other", "AwsApiGatewayStage", "AwsStepFunctionsStateMachine", "AwsCloudWatchAlarm", "AwsLogsLogGroup", "AwsEc2VPCBlockPublicAccessOptions", "AwsEfsFileSystem", "AwsElasticLoadBalancingV2Listener", "AwsAutoScalingAutoScalingGroup":
		// normalization info not available or forcefully skip alert collection
		return

	default:
		logger.Print(logger.INFO, "Unsupported type for AWS Security Hub", []string{tenantID}, secHubResourceType, secHubResourceID)
		unsupportedData.AssetTypes[secHubResourceType] = struct{}{}
		return
	}

	if !entityIDChanged {
		idSplit := strings.Split(secHubResourceID, "/")
		if len(idSplit) > 0 {
			entityID = strings.ToLower(idSplit[len(idSplit)-1])
		}
	}

	return
}
