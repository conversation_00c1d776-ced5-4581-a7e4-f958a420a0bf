package azure

import (
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const (
	API_VERSION = "2022-09-01"
	DATE_FORMAT = "2006-01-02T15:04:05.000Z"
)

type ARMTemplateEvent struct {
	DeploymentID  string `json:"deploymentId"`
	Location      string `json:"location"`
	Subscription  string `json:"subscription"`
	TenantID      string `json:"tenantId"`
	ResourceGroup string `json:"resourceGroup"`
	EventTime     string `json:"eventTime"`
}

type ARMTemplateDoc struct {
	ARMTemplateEvent
	DeploymentName string                 `json:"deploymentName"`
	TemplateHash   string                 `json:"templateHash"`
	DeploymentTime string                 `json:"deploymentTime"`
	ResourceEvents []common.ResourceEvent `json:"resourceEvents"`
}

func ProcessARMTemplates(tenantID, envID, subscriptionID string, eventStartTime, eventEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.ARM_TEMPLATE, tenantStartTime)

	if (eventEndTime.Sub(eventStartTime)) > eventEndTime.Sub(defaultTime) {
		eventStartTime = defaultTime
	}

	lastEventTime := eventStartTime

	startTime := elastic.DateTime(eventStartTime)
	endTime := elastic.DateTime(eventEndTime)

	logger.Print(logger.INFO, "Fetching arm templates from "+startTime+" to "+endTime, []string{tenantID, subscriptionID})

	// Change eventName Microsoft.Resources/deployments/write to Create Deployment based on what is in events table
	searchQuery := `{"query":{"bool":{"filter":[{"terms":{"eventName.keyword":["Microsoft.Resources/deployments/write"]}},{"range":{"eventTime":{"gt":"` + startTime + `","lte":"` + endTime + `"}}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + subscriptionID + `"}}]}}}`

	armTemplateDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_ACTIVITY_INDEX}, searchQuery)
	if err != nil {
		return
	}

	var armTemplateEvents []ARMTemplateEvent

	for _, armTemplateDoc := range armTemplateDocs {

		var armTemplateEvent = ARMTemplateEvent{
			Subscription: subscriptionID,
		}

		if resourceInterfaces, ok := armTemplateDoc["resources"].([]any); ok {

			for _, resourceInterface := range resourceInterfaces {

				if resourceMap, ok := resourceInterface.(map[string]any); ok {

					if resourceType, ok := resourceMap["resourceType"].(string); ok {

						if resourceType == "Microsoft.Resources/deployments" {

							if resourceName, ok := resourceMap["resourceName"].(string); ok {

								armTemplateEvent.DeploymentID = resourceName

								if region, ok := armTemplateDoc["region"].(string); ok {
									armTemplateEvent.Location = region
								}

								if resourceGroup, ok := armTemplateDoc["resourceGroup"].(string); ok {
									armTemplateEvent.ResourceGroup = resourceGroup
								}

								if eventTimeString, ok := armTemplateDoc["eventTime"].(string); ok {

									armTemplateEvent.EventTime = eventTimeString

									eventTime, err := time.Parse(elastic.DATE_FORMAT, eventTimeString)
									if err != nil {
										logger.Print(logger.ERROR, "Got error parsing event time", []string{tenantID, subscriptionID}, err)
										// return
									}

									if eventTime.After(lastEventTime) {
										lastEventTime = eventTime
									}
								}

								armTemplateEvent.TenantID = tenantID

								armTemplateEvents = append(armTemplateEvents, armTemplateEvent)
							}
						}
					}
				}
			}
		}
	}

	for _, armTemplateEvent := range armTemplateEvents {

		go func(armTemplateEvent ARMTemplateEvent, envID string) {

			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occured", []string{tenantID, subscriptionID}, r)
					email.SendPanicEmail("provider")
				}
			}()

			processARMTemplate(armTemplateEvent, envID)

		}(armTemplateEvent, envID)
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.ARM_TEMPLATE, lastEventTime)
}

// Not being used
// func azureToString(strPtr *string) (str string) {

// 	if strPtr != nil {
// 		str = *strPtr
// 	}

// 	return
// }

// Not being used
// func azureToTime(timePtr *time.Time) (time time.Time) {

// 	if timePtr != nil {
// 		time = *timePtr
// 	}

// 	return
// }
