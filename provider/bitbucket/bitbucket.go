package bitbucket

import (
	"encoding/json"
	"time"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

func ValidateAuth(bitbucketEnv tenant.BitbucketEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)

	accessToken, err := getBitbucketAccessToken(bitbucketEnv.Token)
	if err != nil {
		return
	}

	workspacesUrl := BITBUCKET_DOMAIN + "/2.0/workspaces"
	if _, err = transport.SendRequest(
		"GET",
		workspacesUrl,
		nil,
		map[string]string{"Authorization": "Bearer " + accessToken},
		nil,
	); err != nil {
		authValidation[accessToken] = false
		return
	}

	authValidation[accessToken] = true
	return
}

func ProcessBitbucketData(tenantID string, bitbucketEnv tenant.BitbucketEnvironment, gitStartTime, gitEndTime, tenantStartTime time.Time, preCommitCron bool) {

	logger.Print(logger.INFO, "Fetching bitbucket files from "+common.DateTime(gitStartTime)+" to "+common.DateTime(gitEndTime), []string{tenantID})

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.BITBUCKET_COMMIT, tenantStartTime)

	if !preCommitCron && (gitEndTime.Sub(gitStartTime)) > gitEndTime.Sub(defaultTime) {
		gitStartTime = defaultTime
	}

	accessToken, err := getBitbucketAccessToken(bitbucketEnv.Token)
	if err != nil {
		return
	}

	// TODO: Terragrunt for bitbucket

	repoMap := make(map[string]struct{})

	for _, r := range bitbucketEnv.Repositories {
		repoMap[r] = struct{}{}
	}

	workspacesUrl := BITBUCKET_DOMAIN + "/2.0/workspaces"

	for {

		wsResp, err := transport.SendRequest(
			"GET",
			workspacesUrl,
			nil,
			map[string]string{"Authorization": "Bearer " + accessToken},
			nil,
		)
		if err != nil {
			return
		}

		var workspacesResp WorkspacesResp

		if err = json.Unmarshal(wsResp, &workspacesResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", err)
			return
		}

		for _, workspace := range workspacesResp.Values {

			reposUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + workspace.Slug

			for {

				reposResp, err := transport.SendRequest(
					"GET",
					reposUrl,
					nil,
					map[string]string{"Authorization": "Bearer " + accessToken},
					nil,
				)
				if err != nil {
					return
				}

				var repositoriesResp RepositoriesResp

				if err = json.Unmarshal(reposResp, &repositoriesResp); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", err)
					return
				}

				for _, repository := range repositoriesResp.Values {

					if _, ok := repoMap[repository.FullName]; !ok {
						if _, ok := repoMap[repository.Slug]; !ok {
							if _, ok := repoMap[repository.Name]; !ok {
								continue
							}
						}
					}

					branchesUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + workspace.Slug + "/" + repository.Slug + "/refs/branches"

					for {

						bResp, err := transport.SendRequest(
							"GET",
							branchesUrl,
							nil,
							map[string]string{"Authorization": "Bearer " + accessToken},
							nil,
						)
						if err != nil {
							return
						}

						var branchesResp BranchesResp

						if err = json.Unmarshal(bResp, &branchesResp); err != nil {
							logger.Print(logger.ERROR, "Got error unmarshalling", err)
							return
						}

						for _, branch := range branchesResp.Values {

							commitsUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + workspace.Slug + "/" + repository.Slug + "/commits/" + branch.Name

							for {

								cResp, err := transport.SendRequest(
									"GET",
									commitsUrl,
									nil,
									map[string]string{"Authorization": "Bearer " + accessToken},
									nil,
								)
								if err != nil {
									return
								}

								var commitsResp CommitsResp

								if err = json.Unmarshal(cResp, &commitsResp); err != nil {
									logger.Print(logger.ERROR, "Got error unmarshalling", err)
									return
								}

								for _, commit := range commitsResp.Values {

									if commit.Date.Before(gitStartTime) {
										break
									} else if commit.Date.After(gitEndTime) {
										continue
									}

									if preCommitCron && common.CommitCollected(tenantID, commit.Hash) {
										continue
									}

									commit.Workspace = workspace
									commit.Repository = repository
									commit.Branch = branch.Name

									if err = processBitbucketCommit(commit, accessToken, tenantID, preCommitCron); err != nil {
										continue
									}
								}

								if len(commitsResp.Next) <= 0 {
									break
								}

								commitsUrl = commitsResp.Next
							}
						}

						if len(branchesResp.Next) <= 0 {
							break
						}

						branchesUrl = branchesResp.Next
					}
				}

				if len(repositoriesResp.Next) <= 0 {
					break
				}

				reposUrl = repositoriesResp.Next
			}

		}

		if len(workspacesResp.Next) <= 0 {
			break
		}

		workspacesUrl = workspacesResp.Next
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.BITBUCKET_COMMIT, gitEndTime)
}
