package bitbucket

import (
	"encoding/json"
	"regexp"
	"strings"

	sanathyaml "github.com/sanathkr/yaml"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func processBitbucketCommit(commit Commit, accessToken, tenantID string, preCommitCron bool) error {

	diffUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + commit.Workspace.Slug + "/" + commit.Repository.Slug +
		"/diffstat/" + commit.Hash

	for {

		dResp, err := transport.SendRequest(
			"GET",
			diffUrl,
			nil,
			map[string]string{"Authorization": "Bearer " + accessToken},
			nil,
		)
		if err != nil {
			return err
		}

		var diffResp DiffResp

		if err = json.Unmarshal(dResp, &diffResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", err)
			return err
		}

		for _, diffVal := range diffResp.Values {
			var (
				filePath        string
				gitFileHash     string
				authorEmail     string
				fileContentResp []byte
			)
			if diffVal.Status == "removed" {
				filePath = diffVal.Old.Path
			} else {
				filePath = diffVal.New.Path

				fileContentUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + commit.Workspace.Slug + "/" +
					commit.Repository.Slug + "/src/" + commit.Hash + "/" + filePath

				fileContentResp, err := transport.SendRequest(
					"GET",
					fileContentUrl,
					nil,
					map[string]string{"Authorization": "Bearer " + accessToken},
					nil,
				)
				if err != nil {
					return err
				}

				gitFileHash, err = common.GetFileGitHash(fileContentResp)
				if err != nil {
					logger.Print(logger.ERROR, "Got error generating file git hash", []string{tenantID, commit.Repository.Name}, err)
					// continue
				}

				authorEmail = commit.Author.User.DisplayName

				if strings.Contains(commit.Author.Raw, "@") {

					regex := regexp.MustCompile(`<([^>]*)>`)
					match := regex.FindStringSubmatch(commit.Author.Raw)

					if match != nil {
						authorEmail = match[1]
					}
				}
			}

			pathSlice := strings.Split(filePath, "/")
			fileName := pathSlice[len(pathSlice)-1]

			if strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json") || strings.HasSuffix(filePath, ".sh") {
				// Terraform

				var (
					docID string
					csp   string
				)

				if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
					CommitID:      commit.Hash,
					CommitSummary: commit.Message,
					Author:        commit.Author.User.DisplayName,
					AuthorEmail:   authorEmail,
					Filename:      filePath,
					GitFileHash:   gitFileHash,
					CommitTime:    elastic.DateTime(commit.Date),
					FileStatus:    diffVal.Status,
					RepoName:      commit.Repository.Name,
					Branch:        commit.Branch,
					IACType:       common.TERRAFORM,
					GitClient:     common.BITBUCKET,
					TenantID:      tenantID,
					FileContent:   string(fileContentResp),
				}); err != nil {
					continue
				}

				fileDiffUrl := BITBUCKET_DOMAIN + "/2.0/repositories/" + commit.Workspace.Slug + "/" +
					commit.Repository.Slug + "/diff/" + commit.Hash

				fileDiffResp, err := transport.SendRequest(
					"GET",
					fileDiffUrl,
					nil,
					map[string]string{"Authorization": "Bearer " + accessToken},
					nil,
				)
				if err != nil {
					return err
				}

				csp, err = common.ProcessTerraformCommitResources(string(fileContentResp), string(fileDiffResp), docID, tenantID, elastic.DateTime(commit.Date), common.BITBUCKET, &common.BitbucketApiClient{
					Token:         accessToken,
					Domain:        BITBUCKET_DOMAIN,
					WorkspaceSlug: commit.Workspace.Slug,
					RepoSlug:      commit.Repository.Slug,
					CommitHash:    commit.Hash,
					FilePath:      filePath,
					TenantID:      tenantID,
					RepoName:      commit.Repository.Name,
					FileStatus:    diffVal.Status,
					Branch:        commit.Branch,
				})
				if err != nil {
					logger.Print(logger.ERROR, "Error while parsing tf block to bitbucket commit user", []string{tenantID, commit.Repository.Name, gitFileHash}, err)
					continue
				}

				if strings.HasSuffix(fileName, "variables.tf") || strings.HasSuffix(fileName, "var.tf") || strings.HasSuffix(fileName, ".tfvars") || strings.HasSuffix(fileName, ".tfvars.json") || strings.HasSuffix(fileName, "variable.tf") || strings.HasSuffix(fileName, "vars.tf") || strings.HasSuffix(fileName, ".sh") {
					continue
				}

				if preCommitCron {

					commitTime := commit.Date
					err := common.ProcessPreCommitEvents(tenantID, csp, commitTime)
					if err != nil {
						logger.Print(logger.ERROR, "Error while Processing Pre Commit Cron TF Commits", []string{tenantID, commit.Repository.Name, commit.Hash, gitFileHash}, err)
						continue
					}
				}

			} else {

				// Cloudformation/ARM

				if !strings.HasSuffix(filePath, ".json") {

					fileContentResp, err = sanathyaml.YAMLToJSON(fileContentResp)
					if err != nil {
						logger.Print(logger.ERROR, "Got error converting to json", []string{tenantID, commit.Repository.Name}, err)
						// continue
					}
				}

				minifiedJSONHash, err := common.GetMinifiedJSONFileHash(fileContentResp)
				if err != nil {
					logger.Print(logger.ERROR, "Got error generating minified json hash", []string{tenantID, commit.Repository.Name}, err)
					// continue
				}

				if len(fileContentResp) > 0 && strings.HasPrefix(string(fileContentResp), "{") {
					var r map[string]any
					err = json.Unmarshal(fileContentResp, &r)
					if err != nil {
						logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, commit.Repository.Name}, err)
						continue
					}
					if _, ok := r["AWSTemplateFormatVersion"]; ok {

						priorityConfigs, err := common.ProcessCftTemplate(r, tenantID)
						if err != nil {
							logger.Print(logger.INFO, "Error parsing cft tempalte", []string{tenantID, commit.Repository.Name}, err)
							continue
						}

						var docID string

						if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
							CommitID:         commit.Hash,
							CommitSummary:    commit.Message,
							Author:           commit.Author.User.DisplayName,
							AuthorEmail:      authorEmail,
							Filename:         filePath,
							GitFileHash:      gitFileHash,
							MinifiedJSONHash: minifiedJSONHash,
							CommitTime:       elastic.DateTime(commit.Date),
							FileStatus:       diffVal.Status,
							RepoName:         commit.Repository.Name,
							Branch:           commit.Branch,
							IACType:          "aws_cft",
							GitClient:        "bitbucket",
							TenantID:         tenantID,
							FileContent:      string(fileContentResp),
							PriorityConfigs:  priorityConfigs,
						}); err != nil {
							continue
						}

						if err = common.MapCommitToCFTResources(tenantID, gitFileHash, common.GitUser{
							Name: common.GitIdentity(authorEmail, commit.Author.User.DisplayName, tenantID), Client: "bitbucket",
							Action: diffVal.Status, CommitTime: elastic.DateTime(commit.Date), DocID: docID},
						); err != nil {
							continue
						}

					} else if _, ok = r["$schema"]; ok {

						var docID string

						if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
							CommitID:         commit.Hash,
							CommitSummary:    commit.Message,
							Author:           commit.Author.User.DisplayName,
							AuthorEmail:      authorEmail,
							Filename:         filePath,
							GitFileHash:      gitFileHash,
							MinifiedJSONHash: minifiedJSONHash,
							CommitTime:       elastic.DateTime(commit.Date),
							FileStatus:       diffVal.Status,
							RepoName:         commit.Repository.Name,
							Branch:           commit.Branch,
							IACType:          "azure_arm",
							GitClient:        "bitbucket",
							TenantID:         tenantID,
							FileContent:      string(fileContentResp),
						}); err != nil {
							continue
						}

						if err = common.MapCommitToARMResources(tenantID, minifiedJSONHash, common.GitUser{
							Name: common.GitIdentity(authorEmail, commit.Author.User.DisplayName, tenantID), Client: "bitbucket",
							Action: diffVal.Status, CommitTime: elastic.DateTime(commit.Date), DocID: docID},
						); err != nil {
							continue
						}

					}
				}
			}

		}

		if len(diffResp.Next) <= 0 {
			break
		}

		diffUrl = diffResp.Next
	}

	return nil
}
