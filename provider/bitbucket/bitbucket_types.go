package bitbucket

import (
	"encoding/json"
	"time"
)

const BITBUCKET_DOMAIN = "https://api.bitbucket.org"

type RefreshTokenRequest struct {
	GrantType    string `json:"grant_type"`
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

type WorkspacesResp struct {
	Values []Workspace `json:"values"`
	Next   string      `json:"next"`
}

type Workspace struct {
	Slug string `json:"slug"`
}

type RepositoriesResp struct {
	Values []Repository `json:"values"`
	Next   string       `json:"next"`
}

type Repository struct {
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	Slug     string `json:"slug"`
}

type BranchesResp struct {
	Values []Branch `json:"values"`
	Next   string   `json:"next"`
}

type Branch struct {
	Name string `json:"name"`
}

type CommitsResp struct {
	Values []Commit `json:"values"`
	Next   string   `json:"next"`
}

type Commit struct {
	Hash    string       `json:"hash"`
	Date    time.Time    `json:"date"`
	Message string       `json:"message"`
	Author  CommitAuthor `json:"author"`

	Workspace  Workspace  `json:"-"`
	Repository Repository `json:"-"`
	Branch     string     `json:"-"`
}

type CommitAuthor struct {
	Raw  string `json:"raw"`
	User struct {
		DisplayName string `json:"display_name"`
		Nickname    string `json:"nickname"`
	}
}

type DiffResp struct {
	Values []Diff `json:"values"`
	Next   string `json:"next"`
}

type Diff struct {
	Status string
	New    struct {
		Path string `json:"path"`
	} `json:"new"`
	Old struct {
		Path string `json:"path"`
	} `json:"old"`
}

// Not being used today
// func generateBitbucketAccessToken(clientID, clientSecret, refreshToken string) (accessToken string, err error) {

// 	// Function not being used today

// 	ctx := context.Background()
// 	conf := &oauth2.Config{
// 		ClientID:     clientID,
// 		ClientSecret: clientSecret,
// 		Endpoint:     bitbucket.Endpoint,
// 	}

// 	tokenSource := conf.TokenSource(ctx, &oauth2.Token{
// 		RefreshToken: refreshToken,
// 	})

// 	tok, err := tokenSource.Token()
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error generating token", err)
// 		return
// 	}

// 	accessToken = tok.AccessToken

// 	return
// }

func getBitbucketAccessToken(token string) (string, error) {

	type BitbucketToken struct {
		AccessToken string `json:"access_token"`
	}

	var bitbucketToken BitbucketToken

	if err := json.Unmarshal([]byte(token), &bitbucketToken); err != nil {
		return "", err
	}

	return bitbucketToken.AccessToken, nil
}
