package common

import (
	"fmt"
	"log"
	"maps"
	"reflect"
	"strconv"
	"strings"

	"github.com/precize/logger"
)

func InsertValueIntoJson(flattenedData map[string]any, assetsDataMap map[string]any, entity<PERSON>son map[string]any, mappings []map[string]any, serviceType string) {
	for _, mapping := range mappings {
		key, keyOk := mapping["key"].(string)
		path, pathOk := mapping["path"].(string)

		if !keyOk || !pathOk {
			log.Print(logger.ERROR, "Invalid mapping entry for service", serviceType, mapping)
			continue
		}

		if path == ALL_PATH {
			maps.Copy(entityJson, flattenedData)
			continue
		}

		if value, found := GetValueFromPath(flattenedData, assetsDataMap, path); found {
			entityJson[key] = value
		}
	}
}

func GetValueFromPath(flattenedData map[string]any, originalData map[string]any, path string) (any, bool) {

	normalizedPath := strings.ToLower(path)
	if value, exists := flattenedData[normalizedPath]; exists {
		return value, true
	}

	parts := strings.Split(normalizedPath, ".")
	var current any = originalData

	for _, part := range parts {
		openBracket := strings.Index(part, "[")
		closeBracket := strings.Index(part, "]")

		if openBracket > 0 && closeBracket > openBracket {

			keyPart := part[:openBracket]
			indexPart := part[openBracket+1 : closeBracket]

			currentMap, ok := current.(map[string]any)
			if !ok {
				return nil, false
			}

			sliceVal, exists := currentMap[keyPart]
			if !exists {
				return nil, false
			}

			slice, ok := sliceVal.([]any)
			if !ok {
				return nil, false
			}

			index, err := strconv.Atoi(indexPart)
			if err != nil || index < 0 || index >= len(slice) {
				return nil, false
			}

			current = slice[index]

		} else {

			currentMap, ok := current.(map[string]any)
			if !ok {
				return nil, false
			}

			value, exists := currentMap[part]
			if !exists {
				return nil, false
			}
			current = value
		}
	}

	return current, true
}

func ConvertKeysToLowerCase(data any) map[string]any {

	if data == nil {
		return make(map[string]any)
	}

	v := reflect.ValueOf(data)
	if v.Kind() != reflect.Map {
		return make(map[string]any)
	}

	result := make(map[string]any)
	for _, key := range v.MapKeys() {
		keyStr := fmt.Sprintf("%v", key.Interface())
		lowerKey := strings.ToLower(keyStr)
		value := v.MapIndex(key).Interface()
		result[lowerKey] = convertValue(value)
	}
	return result
}

func convertValue(data any) any {
	v := reflect.ValueOf(data)

	switch v.Kind() {
	case reflect.Map:
		return ConvertKeysToLowerCase(data)

	case reflect.Slice, reflect.Array:
		result := make([]any, v.Len())
		for i := 0; i < v.Len(); i++ {
			result[i] = convertValue(v.Index(i).Interface())
		}
		return result

	default:
		return data
	}
}

func addToFlattened(flattened map[string]any, key string, value any) {
	if existingValue, exists := flattened[key]; exists {
		switch existing := existingValue.(type) {
		case []any:
			if !contains(existing, value) {
				flattened[key] = append(existing, value)
			}
		default:
			if !contains([]any{existing}, value) {
				flattened[key] = []any{existing, value}
			}
		}
	} else {
		flattened[key] = value
	}
}

func FlattenJSON(jsonData map[string]any, flattened map[string]any, prefix string) {
	for key, value := range jsonData {
		fullKey := strings.ToLower(key)
		if prefix != "" {
			fullKey = prefix + "." + fullKey
		}

		switch v := value.(type) {
		case map[string]any:
			FlattenJSON(v, flattened, fullKey)
		case []any:
			for i, item := range v {
				arrayKey := fmt.Sprintf("%s[%d]", fullKey, i)
				if nestedMap, ok := item.(map[string]any); ok {
					FlattenJSON(nestedMap, flattened, arrayKey)
				} else {
					processedValue := processValue(item)
					addToFlattened(flattened, arrayKey, processedValue)
				}
			}
		default:
			processedValue := processValue(v)
			addToFlattened(flattened, fullKey, processedValue)
		}
	}
}

func processValue(value any) any {
	if str, ok := value.(string); ok {
		return strings.ToLower(strings.ReplaceAll(str, " ", ""))
	}
	return value
}
