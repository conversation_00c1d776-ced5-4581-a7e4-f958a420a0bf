package common

const (
	ALL_PATH = "-all-"
)

type UnsupportedData struct {
	AssetTypes        map[string]struct{}
	EntityIDNormalize map[string]struct{}
	EntityJson        map[string]struct{}
}

func NewUnsupportedData() *UnsupportedData {
	return &UnsupportedData{
		AssetTypes:        make(map[string]struct{}),
		EntityIDNormalize: make(map[string]struct{}),
		EntityJson:        make(map[string]struct{}),
	}
}

func contains(slice []any, item any) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
