package gcp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	bufferTime    = 48 * time.Hour
	THREAT        = "THREAT"
	VULNERABILITY = "VULNERABILITY"
	UNSPECIFIED   = "UNSPECIFIED"
)

type secCmdCenterRequest struct {
	ParentID      string `json:"parentId"`
	NextPageToken string `json:"nextPageToken"`
	Filter        string `json:"filter"`
	PageSize      int    `json:"pageSize"`
}

type secCmdCenterFindingsResp struct {
	Status int              `json:"status"`
	Data   secCmdCenterData `json:"data"`
}

type secCmdCenterData struct {
	Findings      []secCmdCenterFinding `json:"listFindingsResults"`
	NextPageToken string                `json:"nextPageToken"`
}

type Timestamp struct {
	Seconds string  `json:"seconds"`
	Nanos   float64 `json:"nanos"`
}

type SourceLogID struct {
	ProjectID         string    `json:"projectId"`
	ResourceContainer string    `json:"resourceContainer"`
	Timestamp         Timestamp `json:"timestamp"`
	InsertID          string    `json:"insertId"`
	LogID             string    `json:"logId"`
}

type Evidence struct {
	SourceLogID SourceLogID `json:"sourceLogId"`
}

type srcPropResource struct {
	GcpResourceName string `json:"gcpResourceName"`
}

type sourceID struct {
	ProjectNumber              string `json:"projectNumber"`
	CustomerOrganizationNumber string `json:"customerOrganizationNumber"`
}

type detectionCategory struct {
	RuleName string `json:"ruleName"`
}

type uriWithDisplay struct {
	DisplayName string `json:"displayName"`
	URL         string `json:"url"`
}

type contextURIs struct {
	MitreUri             uriWithDisplay   `json:"mitreUri"`
	CloudLoggingQueryUri []uriWithDisplay `json:"cloudLoggingQueryUri"`
	RelatedFindingUri    map[string]any   `json:"relatedFindingUri"`
}

type sourceProperties struct {
	SourceID          sourceID          `json:"sourceId"`
	DetectionCategory detectionCategory `json:"detectionCategory"`
	DetectionPriority string            `json:"detectionPriority"`
	AffectedResources []srcPropResource `json:"affectedResources"`
	Evidence          []Evidence        `json:"evidence"`
	Properties        map[string]any    `json:"properties"`
	FindingID         string            `json:"findingId"`
	ContextUris       contextURIs       `json:"contextUris"`
}

type securityMarks struct {
	Name string `json:"name"`
}

type mitreAttack struct {
	PrimaryTactic     string `json:"primaryTactic"`
	PrimaryTechniques []any  `json:"primaryTechniques"`
}

type callerGeo struct {
	RegionCode string `json:"regionCode"`
}

type access struct {
	PrincipalEmail string    `json:"principalEmail"`
	CallerIP       string    `json:"callerIp"`
	CallerIPGeo    callerGeo `json:"callerIpGeo"`
	ServiceName    string    `json:"serviceName"`
	MethodName     string    `json:"methodName"`
	UserAgent      string    `json:"userAgent"`
}

type cloudLoggingEntry struct {
	InsertID          string `json:"insertId"`
	LogID             string `json:"logId"`
	ResourceContainer string `json:"resourceContainer"`
	Timestamp         string `json:"timestamp"`
}

type logEntry struct {
	CloudLoggingEntry cloudLoggingEntry `json:"cloudLoggingEntry"`
}

type resource struct {
	Name               string `json:"name"`
	ProjectName        string `json:"projectName"`
	ProjectDisplayName string `json:"projectDisplayName"`
	ParentName         string `json:"parentName"`
	ParentDisplayName  string `json:"parentDisplayName"`
	Type               string `json:"type"`
	DisplayName        string `json:"displayName"`
}

type secCmdCenterFinding struct {
	Finding  finding  `json:"finding"`
	Resource resource `json:"resource"`
}

type finding struct {
	Name              string           `json:"name"`
	Parent            string           `json:"parent"`
	ResourceName      string           `json:"resourceName"`
	State             string           `json:"state"`
	Category          string           `json:"category"`
	SourceProperties  sourceProperties `json:"sourceProperties"`
	SecurityMarks     securityMarks    `json:"securityMarks"`
	EventTime         string           `json:"eventTime"`
	CreateTime        string           `json:"createTime"`
	Severity          string           `json:"severity"`
	CanonicalName     string           `json:"canonicalName"`
	Mute              string           `json:"mute"`
	FindingClass      any              `json:"findingClass"`
	MuteUpdateTime    string           `json:"muteUpdateTime"`
	MitreAttack       mitreAttack      `json:"mitreAttack"`
	Access            access           `json:"access"`
	ParentDisplayName string           `json:"parentDisplayName"`
	LogEntries        []logEntry       `json:"logEntries"`
	Description       string           `json:"description"`
}

const (
	DEFAULT_PAGE_SIZE = 1000
)

func ProcessSecCommandCenterFindings(tenantID, envID, accountID string, isOrgOnboarded bool, secCmdCenterStartTime, secCmdCenterEndTime, tenantStartTime time.Time) {

	var (
		nextPageToken      string
		firstEverIteration bool
	)

	if secCmdCenterEndTime.Sub(secCmdCenterStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.SECURITY_COMMAND_CENTER_FINDINGS, tenantStartTime)

	if (secCmdCenterEndTime.Sub(secCmdCenterStartTime)) >= secCmdCenterEndTime.Sub(defaultTime) {
		secCmdCenterStartTime = defaultTime
		firstEverIteration = true
	}

	logger.Print(logger.INFO, "Fetching Security Command Center findings from "+common.DateTime(secCmdCenterStartTime)+" to "+common.DateTime(secCmdCenterEndTime), []string{tenantID, accountID})

	for {

		secCmdCenterUrl := `/precize/private/gcp/securityCommandCenter/findings/environment/` + envID

		filter := `NOT mute="MUTED"`

		if firstEverIteration {
			filter += ` AND state="ACTIVE"`
		}

		filter += ` AND event_time > "` + elastic.DateTime(secCmdCenterStartTime) + `" AND event_time < "` + elastic.DateTime(secCmdCenterEndTime) + `" AND NOT severity = "Low"`

		secCmdCenterReq := secCmdCenterRequest{
			NextPageToken: nextPageToken,
			Filter:        filter,
			PageSize:      DEFAULT_PAGE_SIZE,
		}

		if isOrgOnboarded {
			secCmdCenterReq.ParentID = "organizations/" + accountID + "/sources/-"
		} else {
			secCmdCenterReq.ParentID = "projects/" + accountID + "sources/-"
		}

		var (
			secCmdCenterFindingsBuf bytes.Buffer
			secCmdCenterResp        secCmdCenterFindingsResp
		)

		err := json.NewEncoder(&secCmdCenterFindingsBuf).Encode(secCmdCenterReq)
		if err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
			break
		}

		resp, err := transport.SendRequestToServer("POST", secCmdCenterUrl, nil, &secCmdCenterFindingsBuf)
		if err != nil {
			break
		}

		if err = json.Unmarshal(resp, &secCmdCenterResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err, resp)
			continue
		}

		if err := insertsecCmdCenterEventsConcurrent(secCmdCenterResp.Data.Findings, tenantID, secCmdCenterEndTime); err != nil {
			logger.Print(logger.INFO, "Failed query ", []string{tenantID}, filter)
			break
		}

		if len(secCmdCenterResp.Data.NextPageToken) > 0 {
			nextPageToken = secCmdCenterResp.Data.NextPageToken
		} else {
			break
		}
	}

	firstEverIteration = false
	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.SECURITY_COMMAND_CENTER_FINDINGS, secCmdCenterEndTime)
}

func insertsecCmdCenterEventsConcurrent(secCmdCenterFindings []secCmdCenterFinding, tenantID string, insertTime time.Time) error {

	logger.Print(logger.INFO, "Starting Batch", []string{tenantID}, len(secCmdCenterFindings))

	const maxGoroutines = 20

	incidentsChan := make(chan *common.Incident, len(secCmdCenterFindings))
	errorsChan := make(chan error, len(secCmdCenterFindings))

	// semaphore to limit no of goroutines
	sem := make(chan struct{}, maxGoroutines)

	for i, finding := range secCmdCenterFindings {
		sem <- struct{}{}

		go func(f secCmdCenterFinding, index int) {
			defer func() { <-sem }()

			incident, err := processFinding(f, tenantID, insertTime)
			if err != nil {
				errorsChan <- err
				return
			}
			if incident != nil {
				incidentsChan <- incident
			}

		}(finding, i)
	}

	for i := 0; i < maxGoroutines; i++ {
		sem <- struct{}{}
	}

	close(incidentsChan)
	close(errorsChan)

	var (
		incidents       []*common.Incident
		bulkInsertQuery string
	)

	for err := range errorsChan {
		if err != nil {
			logger.Print(logger.ERROR, "Error processing finding", []string{tenantID}, err)
		}
	}

	for incident := range incidentsChan {
		incidents = append(incidents, incident)
	}

	// Build bulk insert query
	for _, incident := range incidents {
		incidentBytes, _ := json.Marshal(incident)
		bulkInsertQuery += fmt.Sprintf(`{"index": {"_id": "%s"}}%s`, incident.ID, "\n")
		bulkInsertQuery += string(incidentBytes) + "\n"
	}

	if len(incidents) > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			return err
		}

		logger.Print(logger.INFO, fmt.Sprintf("Cloud Incident bulk API Successful for Security Command Center findings %d records", len(incidents)), []string{tenantID})
	}

	logger.Print(logger.INFO, "Completed Batch", []string{tenantID})

	return nil
}

func processFinding(finding secCmdCenterFinding, tenantID string, insertTime time.Time) (*common.Incident, error) {
	var (
		sourceRisk, status string
		isIncident         = false
	)

	incidentDocID := common.GenerateCombinedHashID(tenantID, finding.Finding.Name)

	if sourceRisk = finding.Finding.Severity; len(sourceRisk) > 0 {
		sourceRisk = common.ConvertToTitleCase(sourceRisk)
		if sourceRisk == "Severity unspecified" {
			sourceRisk = common.NONE_RISK
		}
	} else {
		sourceRisk = common.NOTEVALUATED_RISK
	}

	if _, ok := common.ValidRiskLevel[sourceRisk]; !ok {
		sourceRisk = common.NOTEVALUATED_RISK
	}

	status = common.INCIDENT_STATUS_OPEN
	if finding.Finding.State != "ACTIVE" {
		status = common.INCIDENT_STATUS_RESOLVED
	}

	createdAt, err := time.Parse(time.RFC3339, finding.Finding.CreateTime)
	if err != nil {
		return nil, fmt.Errorf("error parsing created date: %v", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, finding.Finding.EventTime)
	if err != nil {
		return nil, fmt.Errorf("error parsing updated date: %v", err)
	}

	sourceInfo := map[string]any{
		"mitreAttack":  finding.Finding.MitreAttack,
		"mute":         finding.Finding.Mute,
		"access":       finding.Finding.Access,
		"resource":     finding.Resource,
		"findingClass": finding.Finding.FindingClass,
	}

	sourceJson, err := json.Marshal(sourceInfo)
	if err != nil {
		return nil, fmt.Errorf("error marshalling source info: %v", err)
	}

	findingClass := fmt.Sprintf("%v", finding.Finding.FindingClass)
	if findingClass == THREAT || findingClass == VULNERABILITY {
		isIncident = true
	}

	category := UNSPECIFIED
	// to avoid numbers eg: findingClass = 7
	if len(findingClass) > 3 {
		category = findingClass
	}

	incident := &common.Incident{
		ID:            incidentDocID,
		AlertID:       finding.Finding.Name,
		Issue:         finding.Finding.Category,
		EntityID:      finding.Resource.Name,
		EntityType:    finding.Resource.Type,
		Source:        common.SECURITY_COMMAND_CENTER_SOURCE,
		IssueSeverity: sourceRisk,
		SourceRisk:    sourceRisk,
		CreatedAt:     elastic.DateTime(createdAt),
		UpdatedAt:     elastic.DateTime(updatedAt),
		ServiceID:     common.GCP_SERVICE_ID_INT,
		Category:      category,
		Description:   finding.Finding.Description,
		Status:        status,
		Stage:         "dc",
		TenantID:      tenantID,
		SourceJson:    string(sourceJson),
		InsertTime:    elastic.DateTime(insertTime),
		AccountName:   finding.Resource.ParentName,
		IsIncident:    isIncident,
	}

	rscIDPath := finding.Resource.Name
	rscName := finding.Resource.DisplayName
	rscID := finding.Resource.Name

	// compute.googleapis.com/projects/{}/regions -> projects/regions/{}
	prefix := "projects/"
	if index := strings.Index(rscIDPath, prefix); index != -1 {
		rscIDPath = rscIDPath[index:]
	}

	// projects/{}/regions/{}/instance/id -> id
	parts := strings.Split(rscIDPath, "/")
	if len(parts) > 0 {
		rscID = parts[len(parts)-1]
	}

	crsDoc, err := common.GetCRSDocFromResourceIDAndName(rscIDPath, rscID, rscName, tenantID, common.GCP_SERVICE_ID)
	if err != nil {
		return nil, fmt.Errorf("error getting CRS doc: %v", err)
	}

	if len(crsDoc.ID) > 0 {
		incident.CrsID = crsDoc.ID
		incident.EntityType = crsDoc.EntityType
		incident.Environment = crsDoc.Environment
		incident.Owner = crsDoc.Owner
		incident.ResourceName = crsDoc.ResourceName
		incident.AccountName = crsDoc.AccountName
		incident.EntityID = crsDoc.EntityID
		incident.AccountID = crsDoc.AccountID
	} else {
		logger.Print(logger.INFO, "Crs Doc not found", []string{tenantID}, incident.AlertID)
	}

	return incident, nil
}
