package gitlab

import (
	"encoding/json"
	"strconv"
	"strings"

	sanathyaml "github.com/sanathkr/yaml"
	"github.com/xanzy/go-gitlab"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func processGitlabCommit(gitlabClient *gitlab.Client, commit *gitlab.Commit, project *gitlab.Project, tenantID, accessToken string, preCommitCron bool, globalError *error) {

	var (
		projectIDString = strconv.Itoa(project.ID)
		refsPage        = 1
		branches        []string
	)

	for {

		refs, refsResp, err := gitlabClient.Commits.GetCommitRefs(project.ID, commit.ID, &gitlab.GetCommitRefsOptions{
			Type: gitlab.String("branch"),
			ListOptions: gitlab.ListOptions{
				Page:    refsPage,
				PerPage: 90,
			},
		})
		if err != nil {
			if strings.Contains(err.<PERSON>rror(), "401") {
				gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
				refs, refsResp, err = gitlabClient.Commits.GetCommitRefs(project.ID, commit.ID, &gitlab.GetCommitRefsOptions{
					Type: gitlab.String("branch"),
					ListOptions: gitlab.ListOptions{
						Page:    refsPage,
						PerPage: 90,
					},
				})
				if err != nil {
					*globalError = err
					logger.Print(logger.ERROR, "Got error getting commit refs", []string{tenantID, projectIDString}, err)
					break
				}
			} else {
				logger.Print(logger.ERROR, "Got error getting commit refs", []string{tenantID, projectIDString}, err)
				break
			}
		}

		for _, ref := range refs {
			branches = append(branches, ref.Name)
		}

		if refsResp == nil || refsResp.NextPage == 0 {
			break
		}

		refsPage = refsResp.NextPage
	}

	if len(branches) <= 0 {
		branches = []string{"<unknown>"}
	}

	diffPage := 1

	for {

		commitDiff, commitDiffResp, err := gitlabClient.Commits.GetCommitDiff(project.ID, commit.ID, &gitlab.GetCommitDiffOptions{
			Page:    diffPage,
			PerPage: 90,
		})
		if err != nil {
			if strings.Contains(err.Error(), "401") {
				gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
				commitDiff, commitDiffResp, err = gitlabClient.Commits.GetCommitDiff(project.ID, commit.ID, &gitlab.GetCommitDiffOptions{
					Page:    diffPage,
					PerPage: 90,
				})
				if err != nil {
					*globalError = err
					logger.Print(logger.ERROR, "Got error getting commit diff", []string{tenantID, projectIDString}, err)
					break
				}
			} else {
				logger.Print(logger.ERROR, "Got error getting commit diff", []string{tenantID, projectIDString}, err)
				break
			}
		}

		for _, diffFile := range commitDiff {

			filePath := diffFile.NewPath

			if strings.HasSuffix(filePath, ".yaml") || strings.HasSuffix(filePath, ".yml") ||
				strings.HasSuffix(filePath, ".json") || strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json") || strings.HasSuffix(filePath, ".sh") {

				fileContent, _, err := gitlabClient.RepositoryFiles.GetRawFile(project.ID, filePath, &gitlab.GetRawFileOptions{Ref: gitlab.String(commit.ID)})
				if err != nil && !(diffFile.DeletedFile && strings.Contains(err.Error(), "File Not Found")) {
					if strings.Contains(err.Error(), "401") {
						gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
						fileContent, _, err = gitlabClient.RepositoryFiles.GetRawFile(project.ID, filePath, &gitlab.GetRawFileOptions{Ref: gitlab.String(commit.ID)})
						if err != nil && !(diffFile.DeletedFile && strings.Contains(err.Error(), "File Not Found")) {
							logger.Print(logger.ERROR, "Got error getting commit diff", []string{tenantID, projectIDString}, err)
							break
						}
					} else {
						logger.Print(logger.ERROR, "Got error getting commit diff", []string{tenantID, projectIDString}, err)
						break
					}
				}

				gitFileHash, err := common.GetFileGitHash(fileContent)
				if err != nil {
					logger.Print(logger.ERROR, "Got error generating file git hash", []string{tenantID, projectIDString}, err)
					// continue
				}

				fileStatus := "modified"
				if diffFile.NewFile || diffFile.RenamedFile {
					fileStatus = "created"
				} else if diffFile.DeletedFile {
					fileStatus = "deleted"
				}

				pathSlice := strings.Split(filePath, "/")
				fileName := pathSlice[len(pathSlice)-1]

				if strings.HasSuffix(filePath, ".tf") || strings.HasSuffix(filePath, ".tfvars") || strings.HasSuffix(filePath, ".tfvars.json") || strings.HasSuffix(filePath, ".sh") {

					// Terraform

					var (
						docID string
						csp   string
					)

					for _, branch := range branches {

						if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
							CommitID:      commit.ID,
							CommitSummary: commit.Title,
							Author:        commit.AuthorName,
							AuthorEmail:   commit.AuthorEmail,
							Filename:      filePath,
							GitFileHash:   gitFileHash,
							CommitTime:    elastic.DateTime(*commit.AuthoredDate),
							FileStatus:    fileStatus,
							RepoName:      project.Name,
							Branch:        branch,
							IACType:       common.TERRAFORM,
							GitClient:     common.GITLAB,
							TenantID:      tenantID,
							FileContent:   string(fileContent),
						}); err != nil {
							continue
						}

						csp, err = common.ProcessTerraformCommitResources(string(fileContent), diffFile.Diff, docID, tenantID, elastic.DateTime(*commit.AuthoredDate), common.GITLAB, &common.GitlabApiClient{
							GitlabClient: gitlabClient,
							CommitID:     commit.ID,
							ProjectID:    project.ID,
							Branch:       branch,
							FilePath:     filePath,
							TenantID:     tenantID,
							RepoName:     project.Name,
							FileStatus:   fileStatus,
							AccessToken:  accessToken,
						})
						if err != nil {
							continue
						}

						if strings.HasSuffix(fileName, "variables.tf") || strings.HasSuffix(fileName, "var.tf") || strings.HasSuffix(fileName, ".tfvars") || strings.HasSuffix(fileName, ".tfvars.json") || strings.HasSuffix(fileName, "variable.tf") || strings.HasSuffix(fileName, "vars.tf") || strings.HasSuffix(filePath, ".sh") {
							continue
						}

						if preCommitCron {

							commitTime := *commit.AuthoredDate
							err := common.ProcessPreCommitEvents(tenantID, csp, commitTime)
							if err != nil {
								logger.Print(logger.ERROR, "Error while Processing Pre Commit Cron TF Commits", []string{tenantID, project.Name, commit.ID}, err)
								continue
							}
						}
					}

				} else {

					// Cloudformation/ARM

					if !strings.HasSuffix(filePath, ".json") {

						fileContent, err = sanathyaml.YAMLToJSON(fileContent)
						if err != nil {
							logger.Print(logger.ERROR, "Got error converting to json", []string{tenantID, projectIDString}, err)
							// continue
						}
					}

					minifiedJSONHash, err := common.GetMinifiedJSONFileHash(fileContent)
					if err != nil {
						logger.Print(logger.ERROR, "Got error generating minified json hash", []string{tenantID, projectIDString}, err)
						// continue
					}

					if len(fileContent) > 0 && strings.HasPrefix(string(fileContent), "{") {
						var r map[string]any
						err = json.Unmarshal(fileContent, &r)
						if err != nil {
							logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID, projectIDString}, err)
							continue
						}
						if _, ok := r["AWSTemplateFormatVersion"]; ok {

							var docID string

							for _, branch := range branches {

								priorityConfigs, err := common.ProcessCftTemplate(r, tenantID)
								if err != nil {
									logger.Print(logger.INFO, "Error parsing cft tempalte", []string{tenantID, project.Name}, err)
									continue
								}

								if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
									CommitID:         commit.ID,
									CommitSummary:    commit.Title,
									Author:           commit.AuthorName,
									AuthorEmail:      commit.AuthorEmail,
									Filename:         filePath,
									GitFileHash:      gitFileHash,
									MinifiedJSONHash: minifiedJSONHash,
									CommitTime:       elastic.DateTime(*commit.AuthoredDate),
									FileStatus:       fileStatus,
									RepoName:         project.Name,
									Branch:           branch,
									IACType:          "aws_cft",
									GitClient:        "gitlab",
									TenantID:         tenantID,
									FileContent:      string(fileContent),
									PriorityConfigs:  priorityConfigs,
								}); err != nil {
									continue
								}
							}

							if err = common.MapCommitToCFTResources(tenantID, gitFileHash, common.GitUser{
								Name: common.GitIdentity(commit.AuthorEmail, commit.AuthorName, tenantID), Client: "gitlab",
								Action: fileStatus, CommitTime: elastic.DateTime(*commit.AuthoredDate), DocID: docID},
							); err != nil {
								continue
							}

						} else if _, ok = r["$schema"]; ok {

							var docID string

							for _, branch := range branches {

								if docID, err = elastic.InsertDocument(tenantID, elastic.IAC_GIT_COMMITS_INDEX, common.IACGitCommitDoc{
									CommitID:         commit.ID,
									CommitSummary:    commit.Title,
									Author:           commit.AuthorName,
									AuthorEmail:      commit.AuthorEmail,
									Filename:         filePath,
									GitFileHash:      gitFileHash,
									MinifiedJSONHash: minifiedJSONHash,
									CommitTime:       elastic.DateTime(*commit.AuthoredDate),
									FileStatus:       fileStatus,
									RepoName:         project.Name,
									Branch:           branch,
									IACType:          "azure_arm",
									GitClient:        "gitlab",
									TenantID:         tenantID,
									FileContent:      string(fileContent),
								}); err != nil {
									continue
								}
							}

							if err = common.MapCommitToARMResources(tenantID, minifiedJSONHash, common.GitUser{
								Name: common.GitIdentity(commit.AuthorEmail, commit.AuthorName, tenantID), Client: "gitlab",
								Action: fileStatus, CommitTime: elastic.DateTime(*commit.AuthoredDate), DocID: docID},
							); err != nil {
								continue
							}

						}
					}
				}
			}
		}

		if commitDiffResp == nil || commitDiffResp.NextPage == 0 {
			break
		}

		diffPage = commitDiffResp.NextPage
	}

	return
}
