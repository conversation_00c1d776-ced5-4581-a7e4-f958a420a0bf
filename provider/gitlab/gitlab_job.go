package gitlab

import (
	"io"
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/xanzy/go-gitlab"
)

func processGitlabJob(gitlabClient *gitlab.Client, job *gitlab.Job, project *gitlab.Project, tenantID, accessToken string, globalError *error, parentRscNameToID map[string]string, missingNormalizeRsc map[string]string) {

	// fetch job by id
	job, _, err := gitlabClient.Jobs.GetJob(project.ID, job.ID)
	if err != nil {
		if strings.Contains(err.Error(), "401") {
			gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
			job, _, err = gitlabClient.Jobs.GetJob(project.ID, job.ID)
			if err != nil {
				*globalError = err
				logger.Print(logger.ERROR, "Got error listing jobs", []string{tenantID, strconv.Itoa(project.ID)}, err)
				return
			}
		}
	}

	// fetch job trace
	logFile, _, err := gitlabClient.Jobs.GetTraceFile(project.ID, job.ID)
	if err != nil {
		if strings.Contains(err.Error(), "401") {
			gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
			logFile, _, err = gitlabClient.Jobs.GetTraceFile(project.ID, job.ID)
			if err != nil {
				*globalError = err
				logger.Print(logger.ERROR, "Got error fetching job trace", []string{tenantID, strconv.Itoa(project.ID), strconv.Itoa(job.ID)}, err)
				return
			}
		}
	}

	bytesData, err := io.ReadAll(logFile)
	if err != nil {
		return
	}

	content := strings.ToLower(string(bytesData))
	if strings.Contains(content, "terraform") || strings.Contains(content, "terragrunt") {

		name := job.User.Name
		if job.User.Name == "" {
			name = job.User.Username
		}
		jobInfo := common.DeplopymentInfo{
			Name:        name,
			Email:       job.User.Email,
			StartTime:   elastic.DateTime(*job.CreatedAt),
			EndTime:     elastic.DateTime(*job.FinishedAt),
			Duration:    job.Duration,
			GitClient:   "gitlab",
			JobID:       strconv.Itoa(job.ID),
			ProjectID:   strconv.Itoa(project.ID),
			ProjectName: project.Name,
		}
		common.ProcessTraceFile(content, tenantID, jobInfo, parentRscNameToID, missingNormalizeRsc)
	}

}
