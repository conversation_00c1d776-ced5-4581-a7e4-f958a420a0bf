package gitlab

import (
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/xanzy/go-gitlab"
)

func onboardGitlab(tenantID, accessToken string, gitlabClient *gitlab.Client) error {
	envQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"serviceId":"` + common.GITLAB_ID + `"}}],"must_not":[],"should":[]}}}`
	envDocs, err := elastic.ExecuteSearchQuery([]string{elastic.ENVIRONMENTS_INDEX}, envQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Got error while fetching environments", tenantID, err)
		return err
	}
	if len(envDocs) <= 0 {

		var (
			projectsPage = 1
			projectList  = make([]string, 0)
			tenantName   = ""
		)

		for {

			projects, projectsResp, err := gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
				Membership: gitlab.Bool(true),
				ListOptions: gitlab.ListOptions{
					Page:    projectsPage,
					PerPage: 90,
				},
			})
			if err != nil {
				if strings.Contains(err.Error(), "401") {
					gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
					projects, projectsResp, err = gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
						Membership: gitlab.Bool(true),
						ListOptions: gitlab.ListOptions{
							Page:    projectsPage,
							PerPage: 90,
						},
					})
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
						return err
					}
				}
			}

			for _, project := range projects {
				supportedTfExtensions := []string{"*terragrunt.hcl", "*tf", "*tfvars"}
				fetchedTfRelatedFiles := make([]*gitlab.Blob, 0)
				for _, ext := range supportedTfExtensions {
					srchContents, _, err := gitlabClient.Search.BlobsByProject(project.ID, "filename:"+ext, &gitlab.SearchOptions{})
					if err != nil {
						if strings.Contains(err.Error(), "401") {
							gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
							srchContents, _, err = gitlabClient.Search.BlobsByProject(project.ID, "filename:"+ext, &gitlab.SearchOptions{})
						}
						if err != nil {
							logger.Print(logger.INFO, "Error searching terragrunt file: ", []string{strconv.Itoa(project.ID), project.Name})
							continue
						}
					}
					fetchedTfRelatedFiles = append(fetchedTfRelatedFiles, srchContents...)
				}

				if len(fetchedTfRelatedFiles) > 0 {
					projectList = append(projectList, project.Name)
					for _, fetchedTfRelatedFile := range fetchedTfRelatedFiles {
						if strings.HasSuffix(fetchedTfRelatedFile.Filename, "terragrunt.hcl") {
							contentString, _, err := gitlabClient.RepositoryFiles.GetRawFile(project.ID, fetchedTfRelatedFile.Filename, &gitlab.GetRawFileOptions{})
							if err != nil {
								if strings.Contains(err.Error(), "401") {
									gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
									contentString, _, err = gitlabClient.RepositoryFiles.GetRawFile(project.ID, fetchedTfRelatedFile.Filename, &gitlab.GetRawFileOptions{})
									if err != nil {
										logger.Print(logger.ERROR, "Got error getting commit raw content", []string{tenantID, strconv.Itoa(project.ID)}, err)
										continue
									}
								} else {
									logger.Print(logger.ERROR, "Got error fetching raw file for gitlab", []string{tenantID, strconv.Itoa(project.ID), fetchedTfRelatedFile.Filename}, err)
									continue
								}
							}

							logger.Print(logger.INFO, "Processing Terragrunt File", fetchedTfRelatedFile.Filename)
							err = common.ProcessTerragruntFile(string(contentString), tenantID, common.GITLAB, &common.GitlabApiClient{
								GitlabClient: gitlabClient,
								ProjectID:    project.ID,
								FilePath:     fetchedTfRelatedFile.Filename,
								TenantID:     tenantID,
								RepoName:     project.Name,
								AccessToken:  accessToken,
							})
							if err != nil {
								return err
							}
						}
					}
				}

			}

			if projectsResp == nil || projectsResp.NextPage == 0 {
				break
			}

			projectsPage = projectsResp.NextPage
		}

		tenantQuery := `{"query":{"bool":{"filter":[{"match":{"id.keyword":"` + tenantID + `"}}]}}}`

		tenantDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TENANTS_INDEX}, tenantQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error fetching tenant doc", []string{tenantID}, err)
			return err
		}

		if len(tenantDocs) > 0 {
			for _, tenantDoc := range tenantDocs {
				if name, ok := tenantDoc["companyName"].(string); ok {
					tenantName = name
				}
			}
		}

		if len(projectList) > 0 {
			elastic.InsertDocument(tenantID, elastic.ENVIRONMENTS_INDEX, common.EnvironmentDoc{
				TenantID:     tenantID,
				ServiceID:    common.GITLAB_ID_INT,
				Name:         tenantName,
				Repositories: []string{},
			})
		}
	}

	return nil
}
