package okta

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/okta/okta-sdk-golang/v2/okta/query"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

type oktaLogin struct {
	Name   string `json:"name"`
	Region string `json:"region"`
	Time   string `json:"time"`

	EpochTime int64 `json:"-"`
}

func ProcessOktaEvents(tenantID string, oktaEnv tenant.OktaEnvironment, oktaEventsStartTime, oktaEventsEndTime, tenantStartTime time.Time) {

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.OKTA_EVENT, tenantStartTime)

	if (oktaEventsEndTime.Sub(oktaEventsStartTime)) > oktaEventsEndTime.Sub(defaultTime) {
		oktaEventsStartTime = defaultTime
	}

	domainUrl, accessToken, clientID, err := getOktaCreds(tenantID, oktaEnv.Token)
	if err != nil {
		return
	}

	_, client, err := okta.NewClient(
		context.TODO(),
		okta.WithOrgUrl(domainUrl),
		okta.WithAuthorizationMode("Bearer"),
		okta.WithToken(accessToken),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating okta client", []string{tenantID}, err)
		return
	}

	logger.Print(logger.INFO, "Fetching okta events from "+elastic.DateTime(oktaEventsStartTime)+" to "+elastic.DateTime(oktaEventsEndTime), []string{tenantID})

	domain := strings.TrimPrefix(domainUrl, "https://")
	domain = strings.TrimPrefix(domain, "http://")

	params := query.Params{
		Since: elastic.DateTime(oktaEventsStartTime),
		Until: elastic.DateTime(oktaEventsEndTime),
		Limit: 200,
	}

	events, eventsResp, err := client.LogEvent.GetLogs(context.TODO(), &params)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting logs", []string{tenantID}, err)
		return
	}

	userLogins := make(map[string]oktaLogin)

	for _, event := range events {
		if event.Client.Id != clientID && event.Actor.AlternateId != clientID {
			if err = insertEventDoc(client, event, tenantID, oktaEnv.ID, domain, userLogins); err != nil {
				continue
			}
		}
	}

	for {

		if eventsResp.HasNextPage() {

			if rateLimitReached(eventsResp, client, tenantID, oktaEnv.ID) {
				logger.Print(logger.INFO, "Okta events api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
				time.Sleep(1 * time.Minute)
			}

			var paginatedEvents []*okta.LogEvent
			eventsResp, err = eventsResp.Next(context.TODO(), &paginatedEvents)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting events", []string{tenantID}, err)
				break
			}

			for _, event := range paginatedEvents {
				if event.Client.Id != clientID && event.Actor.AlternateId != clientID {
					if err = insertEventDoc(client, event, tenantID, oktaEnv.ID, domain, userLogins); err != nil {
						continue
					}
				}
			}
		} else {
			break
		}
	}

	var (
		bulkIdentityRequest string
		recordsCount        int
		maxRecords          = 10000
	)

	for email, login := range userLogins {

		identityDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, "okta", email, domain, common.OKTA_USER_IDENTITY_TYPE)

		loginJson, err := json.Marshal(login)
		if err != nil {
			continue
		}

		identitiesUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
		identityUpdateDoc := `{"doc":{"lastReadActivity":` + string(loginJson) + `,"lastReadTime":` + strconv.Itoa(int(login.EpochTime)) + `}}`

		bulkIdentityRequest = bulkIdentityRequest + identitiesUpdateMetadata + "\n" + identityUpdateDoc + "\n"
		recordsCount++

		if recordsCount >= maxRecords {

			if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentityRequest); err != nil {
				return
			}

			logger.Print(logger.INFO, "Identity bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})

			recordsCount = 0
			bulkIdentityRequest = ""
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentityRequest); err != nil {
			return
		}

		logger.Print(logger.INFO, "Identity bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{tenantID})
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.OKTA_EVENT, oktaEventsEndTime)
}

func insertEventDoc(client *okta.Client, event *okta.LogEvent, tenantID, envID, domain string, userLogins map[string]oktaLogin) (err error) {

	if event.Actor.Type == "User" {
		if len(event.Actor.Id) > 0 && event.Actor.Id != "unknown" {

			email := getLoginEmailFromUserID(event.Actor.Id, domain, tenantID)
			if len(email) > 0 {

				userLogins[email] = oktaLogin{
					Name:      event.EventType,
					Region:    "NA",
					Time:      elastic.DateTime(*event.Published),
					EpochTime: (*event.Published).UnixMilli(),
				}
			}
		}
	}

	var insertEvent = true

	if strings.HasPrefix(event.EventType, "app.oauth2.token.grant.") ||
		strings.HasPrefix(event.EventType, "user.authentication.auth") {
		insertEvent = false
	} else {

		switch event.EventType {

		case "user.session.start", "policy.evaluate_sign_on", "app.oauth2.authorize.code", "user.authentication.verify":
			insertEvent = false

		case "application.user_membership.add", "application.user_membership.remove":

			for _, t := range event.Target {

				if t.Type == "User" {
					userApps, err := fetchAppsOfUser(client, t.Id, tenantID, envID)
					if err != nil {
						continue
					}

					if err = elastic.UpdateDocument(
						elastic.IDP_USERS_INDEX,
						common.GenerateCombinedHashID(t.Id, domain, tenantID),
						struct {
							Apps []common.IDPUserApp `json:"apps"`
						}{
							Apps: userApps,
						},
					); err != nil {
						continue
					}

					email := getLoginEmailFromUserID(t.Id, domain, tenantID)
					if len(email) > 0 {

						userDoc, err := elastic.GetDocument(elastic.IDENTITIES_INDEX, common.GenerateCombinedHashIDCaseSensitive(tenantID, "okta", email, domain, common.OKTA_USER_IDENTITY_TYPE))
						if err != nil {
							continue
						}

						if addDetails, ok := userDoc["additionalDetails"].(map[string]any); ok {

							var (
								additionalDetails  = make(map[string]string)
								identityAppsAccess []common.IdentityApplicationAccess
							)

							for k, v := range addDetails {
								additionalDetails[k], _ = v.(string)
							}

							for _, app := range userApps {
								if app.Active {
									identityAppsAccess = append(identityAppsAccess, common.IdentityApplicationAccess{
										Name:     app.AppName,
										Label:    app.AppLabel,
										Username: app.AppUsername,
									})
								}
							}

							var appAccess []byte

							appAccess, err = json.Marshal(identityAppsAccess)
							if err != nil {
								continue
							}

							additionalDetails["appAccess"] = string(appAccess)

							if err = elastic.UpdateDocument(
								elastic.IDENTITIES_INDEX,
								common.GenerateCombinedHashIDCaseSensitive(tenantID, "okta", email, domain, common.OKTA_USER_IDENTITY_TYPE),
								struct {
									AdditionalDetails map[string]string `json:"additionalDetails"`
								}{
									AdditionalDetails: additionalDetails,
								},
							); err != nil {
								continue
							}
						}
					}
				}
			}

		case "group.user_membership.add", "group.user_membership.remove":

			for _, t := range event.Target {

				if t.Type == "User" {
					userGroups, err := fetchGroupsOfUser(client, t.Id, tenantID, envID)
					if err != nil {
						continue
					}

					if err = elastic.UpdateDocument(
						elastic.IDP_USERS_INDEX,
						common.GenerateCombinedHashID(t.Id, domain, tenantID),
						struct {
							Groups []string `json:"groups"`
						}{
							Groups: userGroups,
						},
					); err != nil {
						continue
					}
				}
			}

		case "user.lifecycle.delete.completed", "user.lifecycle.deactivate":

			for _, t := range event.Target {

				if t.Type == "User" {

					status := "DELETED"

					if event.EventType == "user.lifecycle.deactivate" {
						status = "DEPROVISIONED"
					}

					if err = elastic.UpdateDocument(
						elastic.IDP_USERS_INDEX,
						common.GenerateCombinedHashID(t.Id, domain, tenantID),
						struct {
							Status  string `json:"status"`
							Deleted bool   `json:"deleted"`
						}{
							Status:  status,
							Deleted: true,
						},
					); err != nil {
						continue
					}

					email := getLoginEmailFromUserID(t.Id, domain, tenantID)
					if len(email) > 0 {

						if err = elastic.UpdateDocument(
							elastic.IDENTITIES_INDEX,
							common.GenerateCombinedHashIDCaseSensitive(tenantID, "okta", email, domain, common.OKTA_USER_IDENTITY_TYPE),
							struct {
								Deleted bool `json:"deleted"`
							}{
								Deleted: true,
							},
						); err != nil {
							continue
						}
					}
				}
			}

		case "application.lifecycle.delete":

			for _, t := range event.Target {

				if strings.Contains(strings.ToLower(t.Type), "app") {

					if err = elastic.UpdateDocument(
						elastic.IDP_APPS_INDEX,
						common.GenerateCombinedHashID(t.Id, domain, tenantID),
						struct {
							Deleted bool `json:"deleted"`
						}{
							Deleted: true,
						},
					); err != nil {
						continue
					}
				}
			}

		case "group.lifecycle.delete":

			for _, t := range event.Target {

				if t.Type == "UserGroup" {

					if err = elastic.UpdateDocument(
						elastic.IDP_GROUPS_INDEX,
						common.GenerateCombinedHashID(t.Id, domain, tenantID),
						struct {
							Deleted bool `json:"deleted"`
						}{
							Deleted: true,
						},
					); err != nil {
						continue
					}
				}
			}
		}
	}

	if insertEvent {

		var targets []common.IDPEventTarget

		for _, target := range event.Target {
			targets = append(targets, common.IDPEventTarget{
				TargetID:   target.Id,
				TargetType: target.Type,
			})
		}

		if _, err = elastic.InsertDocument(tenantID, elastic.IDP_EVENTS_INDEX, common.IDPEventsDoc{
			EventID:             event.Uuid,
			EventType:           event.EventType,
			EventDisplayMessage: event.DisplayMessage,
			ActorID:             event.Actor.Id,
			ActorType:           event.Actor.Type,
			PublishedTime:       elastic.DateTime(*event.Published),
			ClientID:            event.Client.Id,
			Targets:             targets,
			SourceIP:            event.Client.IpAddress,
			IDPType:             common.OKTA_IDP_TYPE,
			TenantID:            tenantID,
			Domain:              domain,
		}, common.GenerateCombinedHashID(event.Uuid, domain, tenantID)); err != nil {
			return
		}
	}

	return
}
