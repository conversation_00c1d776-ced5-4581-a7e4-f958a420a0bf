package okta

import (
	"context"
	"time"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/okta/okta-sdk-golang/v2/okta/query"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func fetchUsersOfGroup(client *okta.Client, groupID, tenantID, envID string, userGroups map[string][]string) {

	listGroupUsersParams := query.NewQueryParams(
		query.WithLimit(200),
	)

	groupUsers, groupUsersResp, err := client.Group.ListGroupUsers(context.TODO(), groupID, listGroupUsersParams)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting group users", []string{tenantID}, err)
		return
	}

	if rateLimitReached(groupUsersResp, client, tenantID, envID) {
		logger.Print(logger.INFO, "Okta group users api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
		time.Sleep(1 * time.Minute)
	}

	for _, groupUser := range groupUsers {
		userGroups[groupUser.Id] = append(userGroups[groupUser.Id], groupID)
	}

	for {

		if groupUsersResp.HasNextPage() {

			if rateLimitReached(groupUsersResp, client, tenantID, envID) {
				logger.Print(logger.INFO, "Okta group users api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
				time.Sleep(1 * time.Minute)
			}

			var paginatedGroupUsers []*okta.User
			groupUsersResp, err = groupUsersResp.Next(context.TODO(), &paginatedGroupUsers)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting group users", []string{tenantID}, err)
				break
			}

			for _, groupUser := range paginatedGroupUsers {
				userGroups[groupUser.Id] = append(userGroups[groupUser.Id], groupID)
			}
		} else {
			break
		}
	}

	return
}

func insertGroupDoc(group *okta.Group, tenantID, domain string, insertTime time.Time) (err error) {

	if _, err = elastic.InsertDocument(tenantID, elastic.IDP_GROUPS_INDEX, common.IDPGroupsDoc{
		GroupID:               group.Id,
		Name:                  group.Profile.Name,
		Description:           group.Profile.Description,
		Type:                  group.Type,
		CreatedTime:           elastic.DateTime(*group.Created),
		LastUpdatedTime:       elastic.DateTime(*group.LastUpdated),
		LastMembershipUpdated: elastic.DateTime(*group.LastMembershipUpdated),
		IDPType:               common.OKTA_IDP_TYPE,
		Deleted:               false,
		TenantID:              tenantID,
		Domain:                domain,
		InsertTime:            elastic.DateTime(insertTime),
	}, common.GenerateCombinedHashID(group.Id, domain, tenantID)); err != nil {
		return
	}

	return
}
