package okta

import (
	"context"
	"encoding/json"
	"strconv"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

func rateLimitReached(resp *okta.Response, client *okta.Client, tenantID, envID string) bool {

	limitStr := resp.Header.Get("X-Rate-Limit-Limit")
	limitRemainingStr := resp.Header.Get("X-Rate-Limit-Remaining")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		logger.Print(logger.ERROR, "Got error converting rate limit to int", err)
		return false
	}

	limitRemaining, err := strconv.Atoi(limitRemainingStr)
	if err != nil {
		logger.Print(logger.ERROR, "Got error converting rate limit to int", err)
		return false
	}

	rateLimitUsedPercentage := ((limit - limitRemaining) * 100) / limit

	if rateLimitUsedPercentage > 50 {
		// Refresh credentials
		tenantData, err := tenant.GetTenantData(tenantID, false)
		if err != nil {
			return false
		}

		for _, oktaEnv := range tenantData.OktaAccounts.Environment {
			if oktaEnv.ID == envID {
				domainUrl, accessToken, _, err := getOktaCreds(tenantID, oktaEnv.Token)
				if err != nil {
					return false
				}

				_, newClient, err := okta.NewClient(
					context.TODO(),
					okta.WithOrgUrl(domainUrl),
					okta.WithAuthorizationMode("Bearer"),
					okta.WithToken(accessToken),
				)
				if err != nil {
					logger.Print(logger.ERROR, "Got error creating okta client", []string{tenantID}, err)
					return false
				}

				*client = *newClient
				break
			}
		}

		return true
	}

	return false
}

func getOktaCreds(tenantID, token string) (string, string, string, error) {

	type OktaToken struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		ClientID    string `json:"clientId"`
		Domain      string `json:"domain"`
	}

	var oktaToken OktaToken

	if err := json.Unmarshal([]byte(token), &oktaToken); err != nil {
		logger.Print(logger.ERROR, "Error while unmarshalling gitlab token", tenantID, err)
		return "", "", "", err
	}

	return oktaToken.Domain, oktaToken.AccessToken, oktaToken.ClientID, nil
}

func getLoginEmailFromUserID(userID, domain, tenantID string) (email string) {

	idpDoc, err := elastic.GetDocument(elastic.IDP_USERS_INDEX, common.GenerateCombinedHashID(userID, domain, tenantID))
	if err != nil {
		return
	}

	email, _ = idpDoc["email"].(string)
	return
}
