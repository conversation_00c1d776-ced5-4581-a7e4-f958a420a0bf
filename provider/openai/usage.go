package openai

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/precize/common"
	"github.com/precize/logger"
)

func collectCompletionUsage(aiMetaData *OpenAIMetaData, openAIStartTime, openAIEndTime time.Time) {

	var (
		nextPage            string
		headers             = map[string]string{"Authorization": "Bearer " + aiMetaData.AdminKey}
		usersAIResourceDocs []common.AIResourcesDoc
	)

	for {

		if openAIStartTime.IsZero() {
			openAIStartTime = openAIEndTime.AddDate(0, -1, 0)
		}

		queryParams := make(map[string]string)
		queryParams = map[string]string{
			"limit":        "31",
			"start_time":   strconv.FormatInt(openAIStartTime.Unix(), 10),
			"end_time":     strconv.FormatInt(openAIEndTime.Unix(), 10),
			"bucket_width": "1d",
			"group_by":     "project_id,model",
		}

		if len(nextPage) > 0 {
			queryParams["page"] = nextPage
		}

		resp, err := sendOpenAIOrgRequest("GET", baseURL+"/organization/usage/completions", queryParams, headers, nil)
		if err != nil {
			return
		}

		var usageResponse CompletionsUsageResp

		if err = json.Unmarshal(resp, &usageResponse); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{}, err)
			break
		}

		for _, usageResults := range usageResponse.Data {

			for _, usage := range usageResults.Results {

				usage.StartTime = usageResults.StartTime
				usage.EndTime = usageResults.EndTime

				entityJson, err := json.Marshal(usage)
				if err != nil {
					continue
				}

				if usage.Model == nil || usage.ProjectID == nil {
					continue
				}

				aiResourcesDoc := common.AIResourcesDoc{
					EntityID:       *usage.Model,
					EntityType:     common.OPENAI_COMPLETIONUSAGE_RESOURCE_TYPE,
					AccountID:      *usage.ProjectID,
					StageCompleted: "dc",
					CollectedAt:    aiMetaData.CollectedAt,
					ServiceID:      common.OPENAI_SERVICE_ID_INT,
					TenantID:       aiMetaData.TenantID,
					EntityJson:     string(entityJson),
				}

				aiResourcesDoc.ID = common.GenerateCombinedHashID(aiResourcesDoc.EntityID+fmt.Sprint(usageResults.StartTime)+fmt.Sprint(usageResults.EndTime), aiResourcesDoc.EntityType, aiResourcesDoc.AccountID, aiResourcesDoc.TenantID)
				usersAIResourceDocs = append(usersAIResourceDocs, aiResourcesDoc)
			}
		}

		if usageResponse.HasMore {
			nextPage = *usageResponse.NextPage
		} else {
			break
		}
	}

	if err := insertOpenAIResourceToDB(aiMetaData.TenantID, common.OPENAI_COMPLETIONUSAGE_RESOURCE_TYPE, "", usersAIResourceDocs); err != nil {
		return
	}

	return
}
