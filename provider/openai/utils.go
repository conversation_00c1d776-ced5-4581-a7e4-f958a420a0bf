package openai

import (
	"github.com/precize/elastic"
)

func getOrganizationTierForTenant(tenantID string) (tier string) {
	cmpMetaDataQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	cmpMetaDataDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, cmpMetaDataQuery)
	if err != nil {
		return
	}

	for _, cmpMetaDataDoc := range cmpMetaDataDocs {
		if addnInfoStruct, ok := cmpMetaDataDoc["additionalInfo"].(map[string]any); ok {
			if openAIStruct, ok := addnInfoStruct["openai"].(map[string]any); ok {
				if tier, ok := openAIStruct["orgTier"].(string); ok {
					return tier
				}
			}
		}
	}

	return
}
