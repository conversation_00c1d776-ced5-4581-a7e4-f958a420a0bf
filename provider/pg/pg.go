package pg

// import (
// 	"database/sql"
// 	"encoding/json"
// 	"fmt"
// 	"os"
// 	// "time"

// 	_ "github.com/lib/pq"

// "github.com/precize/logger"
// )

// type DBConfig struct {
// 	DBHost     string `json:"dbHost"`
// 	DBPort     int    `json:"dbPort"`
// 	DBUser     string `json:"dbUser"`
// 	DBPassword string `json:"dbPassword"`
// 	DBName     string `json:"dbName"`
// 	DBSSLMode  string `json:"dbSSLMode"`
// }

// func ConnectToPostgresDB(configPath string) (db *sql.DB, err error) {

// 	var config DBConfig

// 	conf, err := os.ReadFile(configPath)
// 	if err == nil {
// 		if err = json.Unmarshal(conf, &config); err != nil {
// 			logger.Print(logger.ERROR,"Got error calling unmarshal", err)
// 			return
// 		}
// 	} else {
// 		config = DBConfig{
// 			DBHost:     "localhost",
// 			DBPort:     5432,
// 			DBUser:     "postgres",
// 			DBPassword: "postgres",
// 			DBName:     "postgres",
// 			DBSSLMode:  "disable",
// 		}
// 	}

// 	logger.Print(logger.ERROR,"DB Config", config)

// 	pgInfo := fmt.Sprintf(
// 		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
// 		config.DBHost, config.DBPort, config.DBUser, config.DBPassword,
// 		config.DBName, config.DBSSLMode)

// 	if db, err = sql.Open("postgres", pgInfo); err != nil {
// 		logger.Print(logger.ERROR,"Got error calling sql open", err)
// 		return
// 	}

// 	return
// }

// func InitDB(DB *sql.DB) error {

// 	query := `CREATE TABLE IF NOT EXISTS cft_stacks ("id" TEXT PRIMARY KEY, "stack_id" TEXT, "username" TEXT, "template_git_hash" TEXT, "execution_time" TIMESTAMP)`

// 	_, err := DB.Exec(query)
// 	if err != nil {
// 		logger.Print(logger.ERROR,"Got error calling create table query", query, err)
// 		return err
// 	}

// 	// rows, err := DB.Query("Select * from cft_stacks")
// 	// if err != nil {
// 	// 	logger.Print(logger.ERROR,"Got error calling select query", query, err)
// 	// 	return err
// 	// }

// 	// defer rows.Close()

// 	// for rows.Next() {
// 	// 	var (
// 	// 		stackID       string
// 	// 		username      string
// 	// 		git_hash      string
// 	// 		executionTime time.Time
// 	// 	)
// 	// 	if err := rows.Scan(&stackID, &username, &git_hash, &executionTime); err != nil {
// 	// 		logger.Print(logger.ERROR,"Got error scannning row", err)
// 	// 		return err
// 	// 	}

// 	// 	logger.Print(logger.INFO,"Values", stackID, username, git_hash, executionTime)
// 	// }

// 	query = `CREATE TABLE IF NOT EXISTS stack_events ("event_id" TEXT PRIMARY KEY, "cloudtrail_stack_event_id" TEXT, "stack_id" TEXT, "resource_id" TEXT, "resource_name" TEXT, "resource_type" TEXT, "resource_status" TEXT, "event_time" TIMESTAMP, "resource_properties" TEXT)`

// 	_, err = DB.Exec(query)
// 	if err != nil {
// 		logger.Print(logger.ERROR,"Got error calling create table query", query, err)
// 		return err
// 	}

// 	query = `CREATE TABLE IF NOT EXISTS git_cft_files ("repo_name" TEXT, "file_path" TEXT, "commit_id" TEXT, "git_hash" TEXT, "author" TEXT, "commit_date" TIMESTAMP)`
// 	_, err = DB.Exec(query)
// 	if err != nil {
// 		logger.Print(logger.ERROR,"Got error calling create table query", query, err)
// 		return err
// 	}

// 	return nil
// }
