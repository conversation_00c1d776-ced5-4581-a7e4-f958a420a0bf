package prismacloud

import (
	"bytes"
	"encoding/json"
	"strconv"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const PRISMA_ENTERPRISE_URL = "https://api.prismacloud.io"

func GeneratePrismaCloudEnterpriseToken(prismaCloudEnv tenant.PrismaCloudEnvironment, tenantID string) (token string, err error) {

	payload := struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}{
		Username: prismaCloudEnv.AccessKeyID,
		Password: prismaCloudEnv.SecretAccessKey,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshaling json", []string{tenantID}, err)
		return
	}

	loginResp, err := transport.SendRequest("POST", prismaCloudEnv.URL+"/login", nil, nil, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Print(logger.ERROR, "Error in login api", []string{tenantID}, payload)
		return
	}

	var loginRes struct {
		Token string `json:"token"`
	}

	if err = json.Unmarshal(loginResp, &loginRes); err != nil {
		logger.Print(logger.ERROR, "Error unmarshaling login response", []string{tenantID}, err)
		return
	}

	token = loginRes.Token
	return
}

func validateEnterpriseAuth(prismaCloudEnv tenant.PrismaCloudEnvironment, authValidation map[string]bool, tenantID string) (err error) {

	var healthResp []byte

	token, err := GeneratePrismaCloudEnterpriseToken(prismaCloudEnv, tenantID)
	if err != nil {
		authValidation[prismaCloudEnv.AccessKeyID] = false
		return
	}

	header := make(map[string]string)
	header["x-redlock-auth"] = token
	header["Accept"] = "application/json; charset=UTF-8"

	if healthResp, err = transport.SendRequest("GET", prismaCloudEnv.URL+"/check", nil, header, nil); err != nil {
		logger.Print(logger.ERROR, "Error in login api", []string{tenantID})
		authValidation[prismaCloudEnv.AccessKeyID] = false
		return
	}

	var healthRes struct {
		Message string `json:"message"`
	}

	if err = json.Unmarshal(healthResp, &healthRes); err != nil {
		logger.Print(logger.ERROR, "Error unmarshaling health check response", []string{tenantID}, err)
		authValidation[prismaCloudEnv.AccessKeyID] = false
		return
	}

	authValidation[prismaCloudEnv.AccessKeyID] = true
	return
}

func ProcessPrismaEnterpriseAlerts(tenantID string, csp string, prismaCloudEnv tenant.PrismaCloudEnvironment, alertStartTime, alertEndTime time.Time) {

	if alertEndTime.Sub(alertStartTime) < bufferTime {
		return
	}

	token, err := GeneratePrismaCloudEnterpriseToken(prismaCloudEnv, tenantID)
	if err != nil {
		return
	}

	header := map[string]string{
		"x-redlock-auth": token,
	}

	logger.Print(logger.INFO, "Fetching Prisma Alerts from "+common.DateTime(alertStartTime)+" to "+common.DateTime(alertEndTime), []string{tenantID})

	var (
		nextPageToken   string
		urlParams       = make(map[string]string)
		bulkInsertQuery string
		currentCount    int
	)

	urlParams["detailed"] = "true"
	urlParams["limit"] = LIMIT

	for {
		urlParams["timeType"] = "absolute"
		urlParams["timeStart"] = strconv.FormatInt(alertStartTime.Unix()*1000, 10)
		urlParams["timeEnd"] = strconv.FormatInt(alertEndTime.Unix()*1000, 10)

		if nextPageToken != "" {
			urlParams["pageToken"] = nextPageToken
		}

		response, err := transport.SendRequest("GET", PRISMA_ENTERPRISE_URL+"/v2/alert", urlParams, header, nil)
		if err != nil {
			logger.Print(logger.ERROR, "Error Prisma API", []string{tenantID}, urlParams, header)
			break
		}

		var alertData map[string]any
		err = json.Unmarshal(response, &alertData)
		if err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling response", []string{tenantID}, err)
			break
		}

		err = processEnterpriseAlerts(alertData, tenantID, alertEndTime, &bulkInsertQuery, &currentCount)
		if err != nil {
			break
		}

		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk insertion", []string{tenantID}, err)
			break
		}

		logger.Print(logger.INFO, "Bulk API successful for Prisma Alerts", []string{tenantID})

		if token, ok := alertData["nextPageToken"].(string); ok {
			currentCount = 0
			bulkInsertQuery = ""
			nextPageToken = token
		} else {
			break
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			logger.Print(logger.ERROR, "Error in final bulk insertion", []string{tenantID}, err)
		}
		logger.Print(logger.INFO, "Final bulk API successful", []string{tenantID})
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.PRISMA_ALERT, alertEndTime)
}

func processEnterpriseAlerts(alertData map[string]any, tenantID string, insertTime time.Time, bulkInsertQuery *string, currentCount *int) error {

	if alerts, ok := alertData["items"].([]any); ok {
		for _, alert := range alerts {

			var prismaAlert common.PrismaAlert
			alertBytes, err := json.Marshal(alert)
			if err != nil {
				return err
			}
			if err := json.Unmarshal(alertBytes, &prismaAlert); err != nil {
				return err
			}

			for _, alertRule := range prismaAlert.AlertRules {

				incident := common.Incident{
					ID:            common.GenerateCombinedHashID(tenantID, prismaAlert.ID),
					AlertID:       prismaAlert.ID,
					Issue:         alertRule.Name,
					EntityID:      prismaAlert.Resource.ID,
					EntityType:    prismaAlert.Resource.ResourceType,
					Source:        "PrismaCloud",
					IssueSeverity: prismaAlert.Policy.Severity,
					// Category:      ,
					CreatedAt:  strconv.Itoa(int(prismaAlert.EventOccurred)),
					UpdatedAt:  strconv.Itoa(int(prismaAlert.LastUpdated)),
					Status:     prismaAlert.Status,
					TenantID:   tenantID,
					InsertTime: elastic.DateTime(insertTime),
				}

				incidentBytes, _ := json.Marshal(incident)
				*bulkInsertQuery += `{"index": {"_id": "` + incident.ID + `"}}\n` + string(incidentBytes) + "\n"
				*currentCount++
			}
		}
	}
	return nil
}
