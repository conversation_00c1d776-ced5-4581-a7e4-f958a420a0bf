package prismacloud

import (
	"time"

	"github.com/precize/provider/tenant"
)

const (
	LIMIT      = "5000"
	bufferTime = 24 * time.Hour
)

func ValidateAuth(prismaCloudEnv tenant.PrismaCloudEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	if len(prismaCloudEnv.Username) > 0 {
		err = validateComputeAuth(prismaCloudEnv, authValidation, tenantID)
	} else {
		err = validateEnterpriseAuth(prismaCloudEnv, authValidation, tenantID)
	}

	return
}
