package provide

import (
	"strconv"
	"sync"
	"time"

	"github.com/precize/email"
	"github.com/precize/logger"
	"github.com/precize/provider/bitbucket"
	"github.com/precize/provider/github"
	"github.com/precize/provider/gitlab"
	"github.com/precize/provider/tenant"

	"github.com/robfig/cron"
)

// Collect commits which where commited locally at most a week back before pushing them
func fetchGitPreCommits() {

	logger.Print(logger.INFO, "Starting pre-commit cronjob")

	c := cron.New()

	c.AddFunc("0 0 0 * * *",

		func() {

			var wg = &sync.WaitGroup{}

			tenantIDs, err := tenant.GetAllTenantIDs()
			if err != nil {
				return
			}

			tenantStartTime := time.Now().UTC()
			endTime := time.Now().UTC()
			startTime := endTime.AddDate(0, 0, -7)

			wg.Add(len(tenantIDs))
			waitChan := make(chan struct{}, MAX_TENANT_THREADS)

			for _, tenantID := range tenantIDs {

				waitChan <- struct{}{}

				go func(tenantID string) {

					defer func() {

						if r := recover(); r != nil {
							logger.Print(logger.ERROR, "Panic occured", []string{tenantID}, r)
							email.SendPanicEmail("provider")
						}

						<-waitChan
						wg.Done()

					}()

					retryCount := 0
					for {
						if _, ok := processingTenants.Load(tenantID); ok {
							retryCount++

							logger.Print(logger.INFO, "Tenant is being processed in normal flow, waiting before retrying pre-commit", []string{tenantID}, strconv.Itoa(retryCount))

							time.Sleep(2 * time.Minute)
							continue
						} else {
							break
						}
					}

					processingTenants.Store(tenantID, struct{}{})
					defer processingTenants.Delete(tenantID)

					logger.Print(logger.INFO, "Pre-commit cronjob started for tenant", []string{tenantID})

					tenantData, err := tenant.GetTenantData(tenantID, false)
					if err != nil {
						return
					}

					for _, githubEnv := range tenantData.GithubAccounts.Environment {
						github.ProcessGithubData(
							tenantID,
							githubEnv,
							startTime,
							endTime,
							tenantStartTime,
							true,
						)
					}

					for _, gitlabEnv := range tenantData.GitlabAccounts.Environment {
						gitlab.ProcessGitlabData(
							tenantID,
							gitlabEnv,
							startTime,
							endTime,
							tenantStartTime,
							true,
						)
					}

					for _, bitbucketEnv := range tenantData.BitbucketAccounts.Environment {
						bitbucket.ProcessBitbucketData(
							tenantID,
							bitbucketEnv,
							startTime,
							endTime,
							tenantStartTime,
							true,
						)
					}

					logger.Print(logger.INFO, "Pre-commit cronjob ended for tenant", []string{tenantID})

				}(tenantID)
			}

			wg.Wait()
		},
	)

	c.Start()
	logger.Print(logger.INFO, "Finished pre-commit cronjob")
}
