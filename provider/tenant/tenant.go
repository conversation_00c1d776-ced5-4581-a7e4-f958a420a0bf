package tenant

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

type TenantResponse struct {
	transport.ServerResponseInfo
	Data TenantData `json:"data"`
}

type TenantData struct {
	AWSAccounts             AWSAccount         `json:"aws"`
	AzureAccounts           AzureAccount       `json:"azure"`
	GCPAccounts             GCPAccount         `json:"gcp"`
	GithubAccounts          GithubAccount      `json:"github"`
	GitlabAccounts          GitlabAccount      `json:"gitlab"`
	BitbucketAccounts       BitbucketAccount   `json:"bitbucket"`
	JiraAccounts            JiraAccount        `json:"jira"`
	OktaAccounts            OktaAccount        `json:"okta"`
	OrcaAccounts            OrcaAccount        `json:"Orca"`
	OpenAIAccounts          OpenAIAccount      `json:"openai"`
	PrismaCloudEnvironments PrismaCloudAccount `json:"prisma"`
	WizEnvironments         WizAccount         `json:"wiz"`

	FetchProgress map[string]time.Time `json:"-"`
	TenantID      string               `json:"tenantId"`
}

type AWSAccount struct {
	Environment []AWSEnvironment `json:"environment"`
}

type AWSEnvironment struct {
	ID          string              `json:"id"`
	AccessKey   string              `json:"accessKey"`
	SecretToken string              `json:"secretToken"`
	ExternalID  string              `json:"externalId"`
	Accounts    []AWSAccountDetails `json:"accounts"`
}

type AWSAccountDetails struct {
	AssumedRole string `json:"assumedRole"`
	AccountID   string `json:"accountId"`
}

type AzureAccount struct {
	Environment []AzureEnvironment `json:"environment"`
}

type AzureEnvironment struct {
	ID            string   `json:"id"`
	Token         string   `json:"token"`
	Subscriptions []string `json:"subscriptions"`
}

type GCPAccount struct {
	Environment []GCPEnvironment `json:"environment"`
}

type GCPEnvironment struct {
	ID       string   `json:"id"`
	Token    string   `json:"token"`
	Projects []string `json:"projects"`
}

type GithubAccount struct {
	Environment []GithubEnvironment `json:"environment"`
}

type GithubEnvironment struct {
	Token        string   `json:"token"`
	Repositories []string `json:"repositories"`
}

type GitlabAccount struct {
	Environment []GitlabEnvironment `json:"environment"`
}

type GitlabEnvironment struct {
	ID       string   `json:"id"`
	Token    string   `json:"token"`
	Projects []string `json:"repositories"`
}

type BitbucketAccount struct {
	Environment []BitbucketEnvironment `json:"environment"`
}

type BitbucketEnvironment struct {
	ID           string   `json:"id"`
	Token        string   `json:"token"`
	Repositories []string `json:"repositories"`
}

type JiraAccount struct {
	Environment []JiraEnvironment `json:"environment"`
}

type JiraEnvironment struct {
	ID       string   `json:"id"`
	URL      string   `json:"url"`
	Username string   `json:"username"`
	Token    string   `json:"token"`
	Projects []string `json:"projects"`
}

type OktaAccount struct {
	Environment []OktaEnvironment `json:"environment"`
}

type OktaEnvironment struct {
	ID    string `json:"id"`
	Token string `json:"token"`
}

type OrcaAccount struct {
	Environment []OrcaEnvironment `json:"environment"`
}

type OrcaEnvironment struct {
	Token string `json:"token"`
}

type OpenAIAccount struct {
	Environment []OpenAIEnvironment `json:"environment"`
}

type OpenAIEnvironment struct {
	ID          string   `json:"id"`
	AdminTokens []string `json:"adminTokens"`
	Projects    []struct {
		ProjectID         string `json:"projectId"`
		ServiceAccountKey string `json:"serviceAccountKey"`
	} `json:"projects"`
}

type PrismaCloudAccount struct {
	Environment []PrismaCloudEnvironment `json:"environment"`
}

type PrismaCloudEnvironment struct {
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	Username        string `json:"username"`
	Password        string `json:"password"`
	URL             string `json:"url"`
}

type WizAccount struct {
	Environment []WizEnvironment `json:"environment"`
}

type WizEnvironment struct {
	ClientID     string `json:"clientId"`
	ClientSecret string `json:"clientSecret"`
	URL          string `json:"url"`
}

func FetchAttributeDefaultTime(provider string, t time.Time) time.Time {
	return providerAttributeFetchDefaults[provider](t)
}

func GetAllTenantIDs() (tenantIDs []string, err error) {

	aggregatedTenantsQuery := `{"query":{"bool":{"must":[{"match_all":{}}]}},"from":0,"size":0,"aggs":{"tenant":{"terms":{"field":"tenantId.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]}}}}`

	eventAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.SERVICEAUTH_INDEX}, aggregatedTenantsQuery)
	if err != nil {
		return
	}

	var aggregatedTenants = struct {
		Tenant struct {
			Buckets []struct {
				Key string `json:"key"`
			} `json:"buckets"`
		} `json:"tenant"`
	}{}

	b, err := json.Marshal(eventAggregation)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to marshal", err)
		return
	}

	if err = json.Unmarshal(b, &aggregatedTenants); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	for _, aggregatedTenantBucket := range aggregatedTenants.Tenant.Buckets {
		tenantIDs = append(tenantIDs, aggregatedTenantBucket.Key)
	}

	return
}

func GetTenantData(tenantID string, progressiveFetch bool) (tenantData TenantData, err error) {

	logger.Print(logger.INFO, "Calling cred api for", tenantID)

	resp, err := transport.SendRequestToServer("GET", "/precize/private/"+tenantID+"/cred", nil, nil)
	if err != nil {
		return
	}

	var tenantResp TenantResponse

	if err = json.Unmarshal(resp, &tenantResp); err != nil {
		logger.Print(logger.ERROR, "Got error calling unmarshal", []string{tenantID}, err)
		return
	}

	if tenantResp.Status != 200 {
		err = errors.New(tenantResp.Error)
		logger.Print(logger.ERROR, "Got error from server", []string{tenantID}, tenantResp.Status, err)
		return
	}

	if progressiveFetch {

		fetchProgressMap := make(map[string]time.Time)

		for k, v := range providerAttributeFetchProgress[tenantID] {

			if v.IsZero() {
				v = providerAttributeFetchDefaults[k](time.Now().UTC())
			}

			fetchProgressMap[k] = v
		}

		tenantResp.Data.FetchProgress = fetchProgressMap
	}

	tenantData = tenantResp.Data

	return
}

func GetEnabledServices(tenantData TenantData) map[string]bool {

	enabledServices := make(map[string]bool)

	if len(tenantData.AWSAccounts.Environment) > 0 {
		enabledServices[common.AWS_SERVICE_CODE] = true
	}

	if len(tenantData.AzureAccounts.Environment) > 0 {
		enabledServices[common.AZURE_SERVICE_CODE] = true
	}

	if len(tenantData.GCPAccounts.Environment) > 0 {
		enabledServices[common.GCP_SERVICE_CODE] = true
	}

	if len(tenantData.GithubAccounts.Environment) > 0 {
		enabledServices[common.GITHUB_SERVICE_CODE] = true
	}

	if len(tenantData.GitlabAccounts.Environment) > 0 {
		enabledServices[common.GITLAB_SERVICE_CODE] = true
	}

	if len(tenantData.BitbucketAccounts.Environment) > 0 {
		enabledServices[common.BITBUCKET_SERVICE_CODE] = true
	}

	if len(tenantData.JiraAccounts.Environment) > 0 {
		enabledServices[common.JIRA_SERVICE_CODE] = true
	}

	if len(tenantData.OktaAccounts.Environment) > 0 {
		enabledServices[common.OKTA_SERVICE_CODE] = true
	}

	if len(tenantData.OrcaAccounts.Environment) > 0 {
		enabledServices[common.ORCA_SERVICE_CODE] = true
	}

	if len(tenantData.OpenAIAccounts.Environment) > 0 {
		enabledServices[common.OPENAI_SERVICE_CODE] = true
	}

	if len(tenantData.PrismaCloudEnvironments.Environment) > 0 {
		enabledServices[common.PRISMA_SERVICE_CODE] = true
	}

	return enabledServices
}

// Deprecated function. Not being used
func GetTenantsData() (map[string]TenantData, error) {

	tenantsData := make(map[string]TenantData)

	searchQuery := `{"query":{"match_all":{}}}`

	tenantDocs, err := elastic.ExecuteSearchQuery([]string{elastic.TENANTS_INDEX}, searchQuery)
	if err != nil {
		return nil, err
	}

	for _, tenant := range tenantDocs {

		tenantID := tenant["_id"].(string)

		logger.Print(logger.INFO, "Calling cred api for", tenantID)

		resp, err := transport.SendRequestToServer("GET", "/precize/private/"+tenantID+"/cred", nil, nil)
		if err != nil {
			continue
		}

		var tenantResp TenantResponse

		if err = json.Unmarshal(resp, &tenantResp); err != nil {
			logger.Print(logger.ERROR, "Got error calling unmarshal", []string{tenantID}, err)
			return nil, err
		}

		if tenantResp.Status != 200 {
			err = errors.New(tenantResp.Error)
			logger.Print(logger.ERROR, "Got error from server", []string{tenantID}, tenantResp.Status, err)
			return nil, err
		}

		fetchProgressMap := make(map[string]time.Time)

		for k, v := range providerAttributeFetchProgress[tenantID] {

			if v.IsZero() {
				v = providerAttributeFetchDefaults[k](time.Now().UTC())
			}

			fetchProgressMap[k] = v
		}

		tenantResp.Data.FetchProgress = fetchProgressMap
		tenantsData[tenantID] = tenantResp.Data
	}

	return tenantsData, nil
}
