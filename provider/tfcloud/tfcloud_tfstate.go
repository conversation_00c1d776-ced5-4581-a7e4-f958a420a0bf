package tfcloud

import (
	"github.com/precize/common"
)

type TFCloudStateVersionDoc struct {
	StateVersionID        string                 `json:"stateVersionId"`
	Workspace             string                 `json:"workspace"`
	Organization          string                 `json:"organization"`
	ConfigurationsGithash []string               `json:"configurationsGithash"`
	CreatedBy             string                 `json:"createdBy"`
	CreatedAt             string                 `json:"createdAt"`
	TenantID              string                 `json:"tenantId"`
	ResourceEvents        []common.ResourceEvent `json:"resourceEvents"`
}

// Not being used
// func processStateVersion(client *tfe.Client, stateVersion *tfe.StateVersion, workspace, organization, tenantID string) (err error) {

// 	var resourceEvents []common.ResourceEvent

// 	run, err := client.Runs.ReadWithOptions(context.TODO(), stateVersion.Run.ID,
// 		&tfe.RunReadOptions{
// 			Include: []tfe.RunIncludeOpt{tfe.RunConfigVer, tfe.RunCreatedBy},
// 		})
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error reading run", []string{
// 			tenantID, organization, workspace}, err)
// 		return
// 	}

// 	stateFileContent, err := client.StateVersions.Download(context.TODO(), stateVersion.DownloadURL)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error downloading state file", []string{
// 			tenantID, organization, workspace}, err)
// 		return
// 	}

// 	var stateFile common.TerraformStateFile

// 	if err = json.Unmarshal(stateFileContent, &stateFile); err != nil {
// 		logger.Print(logger.ERROR, "Got error unmarshaling state", []string{
// 			tenantID, organization, workspace}, err)
// 		return
// 	}

// 	for _, resource := range stateFile.Resources {

// 		if resource.Mode == common.RESOURCE_MODE_MANAGED {

// 			for _, instance := range resource.Instances {

// 				resourceEvent := common.GetResourceMetadataFromTerraform(instance.Attributes, resource.Type, resource.Provider)
// 				resourceEvents = append(resourceEvents, resourceEvent)
// 			}
// 		}
// 	}

// 	tfConfigZip, err := client.ConfigurationVersions.Download(context.TODO(), run.ConfigurationVersion.ID)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error downloading configuration file", []string{
// 			tenantID, organization, workspace}, err)
// 		return
// 	}

// 	r := bytes.NewReader(tfConfigZip)

// 	zr, err := gzip.NewReader(r)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Got error reading zipped configuration file", []string{
// 			tenantID, organization, workspace}, err)
// 		return
// 	}

// 	tr := tar.NewReader(zr)

// 	var (
// 		configurationsGitHash []string
// 		gitUsersList          []common.GitUser
// 	)

// 	for {

// 		header, err := tr.Next()
// 		if err == io.EOF {
// 			break
// 		}
// 		if err != nil {
// 			logger.Print(logger.ERROR, "Got error reading tar file content", []string{
// 				tenantID, organization, workspace}, err)
// 			break
// 		}

// 		name := header.Name

// 		if strings.HasSuffix(name, ".tf") && header.Typeflag == tar.TypeReg {

// 			tfConfig, err := ioutil.ReadAll(tr)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error reading tf file", []string{
// 					tenantID, organization, workspace}, err)
// 				break
// 			}

// 			tempFile := "/tmp/tf_config_" + uuid.New().String()

// 			if err = os.WriteFile(tempFile, tfConfig, 0644); err != nil {
// 				logger.Print(logger.ERROR, "Got error calling WriteFile", []string{
// 					tenantID, organization, workspace}, err)
// 				break
// 			}

// 			gitHashResponse, err := exec.Command("git", "hash-object", tempFile).CombinedOutput()
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error calling exec command", []string{
// 					tenantID, organization, workspace}, string(gitHashResponse), err)
// 				break
// 			}

// 			gitHash := strings.TrimSuffix(string(gitHashResponse), "\n")
// 			configurationsGitHash = append(configurationsGitHash, gitHash)

// 			os.Remove(tempFile)

// 			gitUsers, err := common.FetchGitFileAuthorsWithGitHash(tenantID, gitHash)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error fetching git file authors", []string{
// 					tenantID, organization, workspace}, err)
// 				break
// 			}

// 			gitUsersList = append(gitUsersList, gitUsers...)
// 		}
// 	}

// 	for _, resourceEvent := range resourceEvents {

// 		for _, gitUser := range gitUsersList {

// 			if _, err = elastic.InsertDocument(tenantID, elastic.RESOURCE_USER_EVENTS_INDEX, common.ResourceUserEventDoc{
// 				ResourceEvent: resourceEvent,
// 				Action:        gitUser.Action,
// 				TenantID:      tenantID,
// 				User:          gitUser.Name,
// 				UserType:      gitUser.Client,
// 			}); err != nil {
// 				continue
// 			}
// 		}
// 	}

// 	if _, err = elastic.InsertDocument(tenantID, elastic.TFCLOUD_TFSTATEVERSIONS_INDEX, TFCloudStateVersionDoc{
// 		StateVersionID:        stateVersion.ID,
// 		Workspace:             workspace,
// 		Organization:          organization,
// 		ConfigurationsGithash: configurationsGitHash,
// 		CreatedBy:             run.CreatedBy.Username,
// 		CreatedAt:             elastic.DateTime(stateVersion.CreatedAt),
// 		TenantID:              tenantID,
// 		ResourceEvents:        resourceEvents,
// 	}); err != nil {
// 		return
// 	}

// 	return
// }
