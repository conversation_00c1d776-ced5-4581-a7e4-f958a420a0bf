name: Precize context for Terraform Configurations

on:
  push:
    branches: # Specify terraform deployment branches
      - main
      - release

jobs:
  precize:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        name: Checkout repo
        with:
          token: ${{ secrets.GIT_PUSH_TOKEN }}
          fetch-depth: 0
          ref: ${{ github.head_ref }}
      - name: Run precize context action
        uses: precize/context-action@v0.06
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}