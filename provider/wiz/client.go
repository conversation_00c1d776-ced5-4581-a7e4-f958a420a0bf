package wiz

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/precize/logger"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func SendWizRequest(method, url string, urlParams, headers map[string]string, reqBody io.Reader) ([]byte, error) {

	var (
		retryCount = 0
		maxRetries = 7
		backoff    = 1 * time.Second
	)

	for retryCount <= maxRetries {

		resp, err := transport.SendRequest("POST", url, nil, headers, reqBody)
		if err == nil {
			return resp, nil
		}

		var result types.APIErrorResponse
		if unmarshalErr := json.Unmarshal(resp, &result); unmarshalErr == nil {

			for _, apiErr := range result.Errors {
				if apiErr.Extensions.Code == "429" ||
					strings.Contains(strings.ToLower(apiErr.Message), "too many requests") {

					if retryCount < maxRetries {
						retryCount++
						logger.Print(logger.INFO, fmt.Sprintf("Rate limited (429). Retry %d/%d. Backing off for %v seconds",
							retryCount, maxRetries, backoff.Seconds()))

						time.Sleep(backoff)
						backoff *= 2

						break
					}
				}
			}

		}

		logger.Print(logger.ERROR, "Error from Wiz API", url, err)
		return resp, err
	}

	return nil, nil
}
