package wiz

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/precize/common"
	extCr "github.com/precize/common/ext_cr"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	provCommon "github.com/precize/provider/common"
	"github.com/precize/provider/wiz/types"
)

func NormalizeWizResourceData(tenantID string, resource types.WizCloudResource, serviceCode int, endTime string, isDeleted bool, parentRscNameToID map[string]string, unsupportedData *provCommon.UnsupportedData) (extRsc extCr.ExternalCloudResource, err error) {

	flattenedData := make(map[string]any)
	provCommon.FlattenJSON(resource.GraphEntity.Properties, flattenedData, "")

	assetsDataMap := provCommon.ConvertKeysToLowerCase(resource.GraphEntity.Properties)

	if accID, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "subscriptionExternalId"); ok {
		if acctID, ok := accID.(string); ok {
			extRsc.AccountName = acctID
			extRsc.AccountID = acctID
			if accID, ok := parentRscNameToID[acctID]; ok {
				extRsc.AccountID = accID
			}
		}
	}

	if reg, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "region"); ok {
		if regStr, ok := reg.(string); ok {
			extRsc.Region = regStr
		}
	}

	var tags []extCr.Tag
	if tagsMap, ok := resource.GraphEntity.Properties["tags"].(map[string]any); ok {
		for key, value := range tagsMap {
			tags = append(tags, extCr.Tag{
				Key:   key,
				Value: fmt.Sprintf("%v", value),
			})
		}
	}
	extRsc.Tags = tags

	resourceID := resource.GraphEntity.ProviderUniqueID
	resourceType := resource.GraphEntity.Type

	if len(resourceID) <= 0 {
		if rscID, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "externalId"); ok {
			if rscIDStr, ok := rscID.(string); ok {
				resourceID = strings.ToLower(rscIDStr)
			}
		}
	}

	if len(resourceType) <= 0 {
		if natType, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "nativeType"); ok {
			if natTypeStr, ok := natType.(string); ok && len(natTypeStr) > 0 {
				resourceType = natTypeStr
			}
		}
	}

	extRsc.ResourceName = resource.Name
	if len(extRsc.ResourceName) <= 0 {
		extRsc.ResourceName = resource.GraphEntity.Name
	}

	if len(extRsc.ResourceName) <= 0 {
		if rscName, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "name"); ok {
			if rscNameStr, ok := rscName.(string); ok && len(rscNameStr) > 0 {
				extRsc.ResourceName = rscNameStr
			}
		}
	}

	buildEntityJson(flattenedData, assetsDataMap, resourceType, &extRsc, unsupportedData)

	resourceID, resourceType = FetchResourceIDAndType(resource.GraphEntity.Properties, extRsc.AccountID, extRsc.Region, resourceID, resourceType, extRsc.ResourceName, resource.GraphEntity.Type, unsupportedData)

	if len(resourceID) <= 0 {
		return extCr.ExternalCloudResource{}, fmt.Errorf("empty resource ID")
	}

	if createdAt, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "creationDate"); ok {
		if createdAtStr, ok := createdAt.(string); ok {
			if parsedTime, err := time.Parse(elastic.WIZ_DATE_FORMAT, strings.ToUpper(createdAtStr)); err == nil {
				extRsc.CreatedDate = parsedTime.UTC().Format(elastic.DATE_FORMAT)
			}
		}
	}

	// Handle updatedDate
	if updatedAt, ok := provCommon.GetValueFromPath(flattenedData, assetsDataMap, "updatedAt"); ok {
		if updatedAtStr, ok := updatedAt.(string); ok {
			if parsedTime, err := time.Parse(elastic.WIZ_DATE_FORMAT, strings.ToUpper(updatedAtStr)); err == nil {
				extRsc.UpdatedDate = parsedTime.UTC().Format(elastic.DATE_FORMAT)
			}
		}
	}

	resourceDocID := common.GenerateCombinedHashIDCaseSensitive(tenantID, fmt.Sprintf("%d", serviceCode), extRsc.AccountID, resourceID, resourceType)

	extRsc.ID = resourceDocID
	extRsc.EntityType = resourceType
	extRsc.EntityID = resourceID
	extRsc.Deleted = isDeleted
	extRsc.TenantID = tenantID
	extRsc.InsertTime = endTime
	extRsc.Source = common.WIZ_SOURCE
	extRsc.TagsCount = len(tags)
	extRsc.ServiceID = serviceCode

	return extRsc, nil
}

func buildEntityJson(flattenedData map[string]any, assetsDataMap map[string]any, resourceType string, precizeAsset *extCr.ExternalCloudResource, unsupportedData *provCommon.UnsupportedData) {
	entityJson := make(map[string]any)

	propertyMappings, ok := serviceToPropertyMap[resourceType]
	if !ok {
		unsupportedData.EntityJson[resourceType] = struct{}{}
		return
	}

	mappings, ok := propertyMappings.([]map[string]any)
	if !ok {
		log.Print(logger.ERROR, "Invalid property mapping format for service type: %s", resourceType)
		return
	}

	provCommon.InsertValueIntoJson(flattenedData, assetsDataMap, entityJson, mappings, resourceType)

	entityJsonBytes, err := json.Marshal(entityJson)
	if err != nil {
		logger.Print(logger.ERROR, "Error Marshalling asset json", err)
		return
	}

	precizeAsset.EntityJson = string(entityJsonBytes)
}
