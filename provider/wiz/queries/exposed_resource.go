package queries

const ExposedResourceQuery = `
query NetworkExposuresTable($filterBy: NetworkExposureFilters, $first: Int, $after: String) {
	networkExposures(filterBy: $filterBy, first: $first, after: $after) {
		nodes {
			id
			exposedEntity {
				id
				name
				type
				properties
			}
			accessibleFrom {
				id
				name
				type
				properties
			}
			path {
				id
				name
				type
				properties
				}
			sourceIpRange
			destinationIpRange
			portRange
			appProtocols
			networkProtocols
			customIPRanges {
				id
				name
				ipRanges
			}
			firstSeenAt
			applicationEndpoints {
				id
				name
				type
				properties
			}
			type
		}
		pageInfo {
			hasNextPage
			endCursor
		}
	}
}
`
