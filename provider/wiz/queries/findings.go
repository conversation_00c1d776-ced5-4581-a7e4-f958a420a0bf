package queries

const ConfigurationFindingsQuery = `
query CloudConfigurationFindingsPage(
  $filterBy: ConfigurationFindingFilters
  $first: Int
  $after: String
  $orderBy: ConfigurationFindingOrder
) {
    configurationFindings(
      filterBy: $filterBy
      first: $first
      after: $after
      orderBy: $orderBy
    ) {
      nodes {
        id
        targetExternalId
        deleted
        targetObjectProviderUniqueId
        firstSeenAt
        severity
        result
        status
        remediation
        resource {
          id
          providerId
          name
          nativeType
          type
          region
          subscription {
            id
            name
            externalId
            cloudProvider
          }
          projects {
            id
            name
            riskProfile {
              businessImpact
            }
          }
          tags {
            key
            value
          }
        }
        rule {
          id
          shortId
          graphId
          name
          description
          remediationInstructions
          functionAsControl
          builtin
          targetNativeTypes
          supportsNRT
          subjectEntityType
          serviceType
        }
        securitySubCategories {
          id
          title
          category {
            id
            name
            framework {
              id
              name
            }
          }
        }
        ignoreRules{
          id
          name
          enabled
          expiredAt
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`
