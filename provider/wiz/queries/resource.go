package queries

const CloudResourceSearchQuery = `
	query CloudResourceSearch(
		$filterBy: CloudResourceFilters
		$first: Int
		$after: String
	) {
		cloudResources(
		filterBy: $filterBy
		first: $first
		after: $after
		) {
			nodes {
				...CloudResourceFragment
			}
			pageInfo {
				hasNextPage
				endCursor
			}
		}
	}
	fragment CloudResourceFragment on CloudResource {
		id
		name
		type
		subscriptionId
		subscriptionExternalId
		graphEntity{
			id
			providerUniqueId
			name
			type
			projects {
				id
			}
			properties
			firstSeen
			lastSeen
		}
	}
`
