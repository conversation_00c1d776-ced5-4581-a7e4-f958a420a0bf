package queries

const VulnerabilityFindingsQuery = `
query VulnerabilityFindingsPage($filterBy: VulnerabilityFindingFilters, $first: Int, $after: String, $orderBy: VulnerabilityFindingOrder) {
  vulnerabilityFindings(
    filterBy: $filterBy
    first: $first
    after: $after
    orderBy: $orderBy
  ) {
    nodes {
      id
      portalUrl
      name
      CVEDescription
      CVSSSeverity
      score
      exploitabilityScore
      severity
      nvdSeverity
      weightedSeverity
      impactScore
      dataSourceName
      hasExploit
      hasCisaKevExploit
      status
      isHighProfileThreat
      vendorSeverity
      firstDetectedAt
      lastDetectedAt
      resolvedAt
      description
      remediation
      detailedName
      version
      fixedVersion
      detectionMethod
      link
      locationPath
      artifactType {
      ...SBOMArtifactTypeFragment
      }
      resolutionReason
      epssSeverity
      epssPercentile
      epssProbability
      validatedInRuntime
      layerMetadata {
        id
        details
        isBaseLayer
      }
      projects {
        id
        name
        slug
        businessUnit
        riskProfile {
          businessImpact
        }
      }
      ignoreRules {
        id
        name
        enabled
        expiredAt
      }
      cvssv2 {
        attackVector
        attackComplexity
        confidentialityImpact
        integrityImpact
        privilegesRequired
        userInteractionRequired
      }
      cvssv3 {
        attackVector
        attackComplexity
        confidentialityImpact
        integrityImpact
        privilegesRequired
        userInteractionRequired
      }
      relatedIssueAnalytics {
        issueCount
        criticalSeverityCount
        highSeverityCount
        mediumSeverityCount
        lowSeverityCount
        informationalSeverityCount
      }
      cnaScore
      vulnerableAsset {
        ... on VulnerableAssetBase {
          id
          type
          name
          region
          providerUniqueId
          cloudProviderURL
          cloudPlatform
          nativeType
          status
          subscriptionName
          subscriptionExternalId
          subscriptionId
          tags
          hasLimitedInternetExposure
          hasWideInternetExposure
          isAccessibleFromVPN
          isAccessibleFromOtherVnets
          isAccessibleFromOtherSubscriptions
        }
        ... on VulnerableAssetVirtualMachine {
          operatingSystem
          ipAddresses
          imageName
          computeInstanceGroup {
            id
            externalId
            name
            replicaCount
            tags
          }
        }
        ... on VulnerableAssetServerless {
          runtime
        }
        ... on VulnerableAssetContainerImage {
          imageId
          scanSource
          registry {
            name
            externalId
          }
          repository {
            name
            externalId
          }
          executionControllers {
            id
            name
            entityType
            externalId
            providerUniqueId
            name
            subscriptionExternalId
            subscriptionId
            subscriptionName
            ancestors {
              id
              name
              entityType
              externalId
              providerUniqueId
            }
          }
        }
        ... on VulnerableAssetContainer {
          ImageExternalId
          VmExternalId
          ServerlessContainer
          PodNamespace
          PodName
          NodeName
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
  fragment SBOMArtifactTypeFragment on SBOMArtifactType {
    group
    codeLibraryLanguage
    osPackageManager
    hostedTechnology {
      name
    }
    plugin
    custom
}
`
