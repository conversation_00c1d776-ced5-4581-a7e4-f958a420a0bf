package wiz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	provCommon "github.com/precize/provider/common"
	"github.com/precize/provider/tenant"
	wizCommon "github.com/precize/provider/wiz/common"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/provider/wiz/types"
	"github.com/precize/transport"
)

func CollectCloudResources(tenantID, token string, wizEnv tenant.WizEnvironment, startTime, endTime, tenantStartTime time.Time, tenantData tenant.TenantData) {

	if endTime.Sub(startTime) < resourceBufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.WIZ_RESOURCE, tenantStartTime)

	if (endTime.Sub(startTime)) > endTime.Sub(defaultTime) {
		startTime = defaultTime
	}

	unsupportedData := provCommon.NewUnsupportedData()

	logger.Print(logger.INFO, "Starting Wiz Cloud Resources collection from "+common.DateTime(startTime)+" to "+common.DateTime(endTime), []string{tenantID}, endTime.UnixMilli())

	var (
		hasNextPage     bool = true
		cursor          string
		batchSize       int = 500
		bulkInsertQuery string
		currentCount    int
	)

	headers := map[string]string{
		"accept":        "application/json",
		"authorization": "Bearer " + token,
		"content-Type":  "application/json",
	}

	enabledCsps := make(map[string]bool)

	if len(tenantData.AWSAccounts.Environment) > 0 || true {
		enabledCsps["aws"] = true
	}

	if len(tenantData.AzureAccounts.Environment) > 0 || true {
		enabledCsps["azure"] = true
	}

	if len(tenantData.GCPAccounts.Environment) > 0 || true {
		enabledCsps["gcp"] = true
	}

	var (
		crsQuery          = `{"_source":["resourceName","entityId"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_PROJECT_RESOURCE_TYPE + `","` + common.GCP_FOLDER_RESOURCE_TYPE + `","` + common.GCP_ORG_RESOURCE_TYPE + `","` + common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `","` + common.AWS_ACCOUNT_RESOURCE_TYPE + `","` + common.AWS_ORG_RESOURCE_TYPE + `","` + common.AZURE_RG_RESOURCE_TYPE + `","` + common.AZURE_TENANT_RESOURCE_TYPE + `","` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `","` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"]}}]}}}`
		searchAfter       any
		parentRscNameToID = make(map[string]string)
	)

	for {
		crsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, crsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(crsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, crsDoc := range crsDocs {
			if resourceName, ok := crsDoc["resourceName"].(string); ok {
				if entityID, ok := crsDoc["entityId"].(string); ok {
					if len(resourceName) > 0 && len(entityID) > 0 {
						parentRscNameToID[resourceName] = entityID
					}
				}
			}
		}
	}

	logger.Print(logger.INFO, "Starting Wiz cloud resources collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"updatedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.CloudResourceSearchQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
		}

		var result types.WizCloudResourcesResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
		}

		processWizResources(tenantID, result.Data.CloudResources.Nodes, enabledCsps, parentRscNameToID, &bulkInsertQuery, &currentCount, endTime, false, unsupportedData)

		if len(bulkInsertQuery) > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err, bulkInsertQuery)

				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "External Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
			bulkInsertQuery = ""
			currentCount = 0
		}

		hasNextPage = result.Data.CloudResources.PageInfo.HasNextPage
		cursor = result.Data.CloudResources.PageInfo.EndCursor

		logger.Print(logger.INFO, fmt.Sprintf("Collected %d resources from Wiz", len(result.Data.CloudResources.Nodes)),
			[]string{tenantID})

		time.Sleep(500 * time.Millisecond)
	}

	hasNextPage = true
	cursor = ""

	logger.Print(logger.INFO, "Starting Wiz cloud resources deletion collection", []string{tenantID})

	for hasNextPage {
		variables := map[string]any{
			"first": batchSize,
			"filterBy": map[string]any{
				"deletedAt": map[string]string{
					"before": elastic.DateTime(endTime),
					"after":  elastic.DateTime(startTime),
				},
			},
		}

		if cursor != "" {
			variables["after"] = cursor
		}

		graphqlQuery := types.GraphQLQuery{
			Query:     queries.CloudResourceSearchQuery,
			Variables: variables,
		}

		payload, err := json.Marshal(graphqlQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		}

		resp, err := transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
		if err != nil {
			logger.Print(logger.ERROR, "Error querying Wiz API", []string{tenantID}, err)
		}

		var result types.WizCloudResourcesResponse
		if err = json.Unmarshal(resp, &result); err != nil {
			logger.Print(logger.ERROR, "Error unmarshaling Wiz API response", []string{tenantID}, err)
		}

		processWizResources(tenantID, result.Data.CloudResources.Nodes, enabledCsps, parentRscNameToID, &bulkInsertQuery, &currentCount, endTime, true, unsupportedData)

		if len(bulkInsertQuery) > 0 {
			if err = elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of external cloud resources", []string{tenantID}, err, bulkInsertQuery)

				currentCount = 0
				bulkInsertQuery = ""

				continue
			}

			logger.Print(logger.INFO, "External Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
			bulkInsertQuery = ""
			currentCount = 0
		}

		hasNextPage = result.Data.CloudResources.PageInfo.HasNextPage
		cursor = result.Data.CloudResources.PageInfo.EndCursor

		logger.Print(logger.INFO, fmt.Sprintf("Collected %d resources from Wiz", len(result.Data.CloudResources.Nodes)),
			[]string{tenantID})

		time.Sleep(500 * time.Millisecond)
	}

	if len(unsupportedData.AssetTypes) > 0 {
		logger.Print(logger.ERROR, "Unsupported asset types", []string{tenantID}, unsupportedData.AssetTypes)
	}

	if len(unsupportedData.EntityIDNormalize) > 0 {
		logger.Print(logger.ERROR, "Unsupported entity ID normalization", []string{tenantID}, unsupportedData.EntityIDNormalize)
	}

	if len(unsupportedData.EntityJson) > 0 {
		logger.Print(logger.ERROR, "Unsupported entity JSON", []string{tenantID}, unsupportedData.EntityJson)
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.WIZ_RESOURCE, endTime)

	return
}

func processWizResources(tenantID string, resources []types.WizCloudResource, enabledCsps map[string]bool, parentRscNameToID map[string]string, bulkInsertQuery *string, currentCount *int, endTime time.Time, isDeleted bool, unsupportedData *provCommon.UnsupportedData) error {

	logger.Print(logger.INFO, "Starting Wiz resource processing for batch of "+strconv.Itoa(len(resources))+" records", []string{tenantID})

	for _, resource := range resources {

		if err := storeWizResource(tenantID, resource, enabledCsps, parentRscNameToID, bulkInsertQuery, currentCount, endTime, isDeleted, unsupportedData); err != nil {
			logger.Print(logger.ERROR, "Error processing Wiz resource %s", []string{tenantID}, resource.ID, err)
			continue
		}
	}

	logger.Print(logger.INFO, "Completed Wiz resource processing for batch of "+strconv.Itoa(len(resources))+" records", []string{tenantID})

	return nil
}

func storeWizResource(tenantID string, resource types.WizCloudResource, enabledCsps map[string]bool, parentRscNameToID map[string]string, bulkInsertQuery *string, currentCount *int, endTime time.Time, isDeleted bool, unsupportedData *provCommon.UnsupportedData) error {
	if cloudPlatform, ok := resource.GraphEntity.Properties["cloudPlatform"].(string); ok {
		if enabledCsps[strings.ToLower(cloudPlatform)] {
			serviceCode := common.CspStrToIdIntMap[strings.ToLower(cloudPlatform)]

			externalResource, err := wizCommon.NormalizeWizResourceData(
				tenantID,
				resource,
				serviceCode,
				elastic.DateTime(endTime),
				isDeleted,
				parentRscNameToID,
				unsupportedData,
			)

			if err != nil {
				logger.Print(logger.ERROR, "Error normalizing Wiz resource", []string{tenantID}, err)
				return err
			}

			externalRscInsertMetadata := `{"index": {"_id": "` + externalResource.ID + `"}}`
			externalRscInsertDoc, err := json.Marshal(externalResource)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return err
			}

			*bulkInsertQuery = *bulkInsertQuery + externalRscInsertMetadata + "\n" + string(externalRscInsertDoc) + "\n"
			*currentCount++
		}
	}

	return nil
}

// Not being used
// func syncCloudResources(tenantID string, latestUpdateResourceDocIDs, deletedResourceDocIDs map[string]struct{}, endTime time.Time) error {

// 	crQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"source":"wiz"}}]}},"size":0,"aggs":{"by_collectedAt":{"terms":{"field":"collectedAt","order":{"_key":"desc"},"size":2}}}}`
// 	crAggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery)
// 	if err != nil {
// 		return err
// 	}

// 	penultimateCollectedAt := ""

// 	if crAggsResp, ok := crAggsResp["by_collectedAt"].(map[string]any); ok {
// 		if buckets, ok := crAggsResp["buckets"].([]any); ok {
// 			if len(buckets) > 1 {
// 				if bucket, ok := buckets[1].(map[string]any); ok {
// 					if keyFtr, ok := bucket["key"].(float64); ok {

// 						key := int64(keyFtr)
// 						penultimateCollectedAt = strconv.FormatInt(key, 10)

// 					}
// 				}
// 			}
// 		}
// 	}

// 	var (
// 		searchAfter     any
// 		bulkInsertQuery string
// 		currentCount    int
// 	)

// 	crQuery = `{"query":{"bool":{"must":[{"match":{"source.keyword":"wiz"}},{"match":{"collectedAt":"` + penultimateCollectedAt + `"}},{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

// 	for {
// 		crDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, crQuery, searchAfter)
// 		if err != nil {
// 			return err
// 		}

// 		if len(crDocs) > 0 {
// 			searchAfter = sortResponse
// 		} else {
// 			break
// 		}

// 		for crDocID, crDoc := range crDocs {

// 			if _, ok := latestUpdateResourceDocIDs[crDocID]; ok {
// 				continue
// 			}

// 			crDocBytes, err := json.Marshal(crDoc)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
// 				continue
// 			}

// 			var crDocStruct types.CloudResource
// 			if err = json.Unmarshal(crDocBytes, &crDocStruct); err != nil {
// 				logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
// 				continue
// 			}

// 			if _, ok := deletedResourceDocIDs[crDocStruct.EntityID]; ok {
// 				continue
// 			}

// 			crDocStruct.CollectedAt = endTime.UnixMilli()
// 			crDocStruct.ID = common.GenerateCombinedHashIDCaseSensitive(tenantID, fmt.Sprintf("%d", crDocStruct.CollectedAt), crDocStruct.AccountID, crDocStruct.EntityID, crDocStruct.EntityType)

// 			cloudResourcesInsertMetadata := `{"index": {"_id": "` + crDocStruct.ID + `"}}`
// 			cloudResourcesInsertDoc, err := json.Marshal(crDocStruct)
// 			if err != nil {
// 				logger.Print(logger.ERROR, "Got error marshalling document", err)
// 				return err
// 			}

// 			bulkInsertQuery = bulkInsertQuery + cloudResourcesInsertMetadata + "\n" + string(cloudResourcesInsertDoc) + "\n"
// 			currentCount++
// 		}

// 		if len(bulkInsertQuery) > 0 {
// 			if err = elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_RESOURCES_INDEX, bulkInsertQuery); err != nil {
// 				logger.Print(logger.ERROR, "Error in bulk insertion of cloud resources", []string{tenantID}, err, bulkInsertQuery)

// 				currentCount = 0
// 				bulkInsertQuery = ""

// 				continue
// 			}

// 			logger.Print(logger.INFO, "Cloud resources bulk API Successful for ", []string{tenantID}, currentCount)
// 			bulkInsertQuery = ""
// 			currentCount = 0
// 		}
// 	}

// 	return nil
// }
