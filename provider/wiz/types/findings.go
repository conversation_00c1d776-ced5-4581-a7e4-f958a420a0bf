package types

import "time"

type WizConfigFindingsResponse struct {
	Data struct {
		ConfigurationFindings struct {
			Nodes    []ConfigurationFinding `json:"nodes"`
			PageInfo PageInfo               `json:"pageInfo"`
		} `json:"configurationFindings"`
	} `json:"data"`
}

type ConfigurationFinding struct {
	ID                           string                `json:"id"`
	TargetExternalID             string                `json:"targetExternalId"`
	TargetObjectProviderUniqueID string                `json:"targetObjectProviderUniqueId"`
	FirstSeenAt                  time.Time             `json:"firstSeenAt"`
	Severity                     string                `json:"severity"`
	Result                       string                `json:"result"`
	Status                       string                `json:"status"`
	Remediation                  string                `json:"remediation"`
	Resource                     Resource              `json:"resource"`
	Rule                         Rule                  `json:"rule"`
	SecuritySubCategories        []SecuritySubCategory `json:"securitySubCategories"`
}

type Resource struct {
	ID           string       `json:"id"`
	ProviderID   string       `json:"providerId"`
	Name         string       `json:"name"`
	NativeType   string       `json:"nativeType"`
	Type         string       `json:"type"`
	Region       string       `json:"region"`
	Subscription Subscription `json:"subscription"`
	Projects     []Project    `json:"projects"`
	Tags         []Tag        `json:"tags"`
}

type Subscription struct {
	ID            string `json:"id"`
	Name          string `json:"name"`
	ExternalID    string `json:"externalId"`
	CloudProvider string `json:"cloudProvider"`
}

type Tag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Rule struct {
	ID                      string `json:"id"`
	GraphID                 string `json:"graphId"`
	Name                    string `json:"name"`
	Description             string `json:"description"`
	RemediationInstructions string `json:"remediationInstructions"`
	FunctionAsControl       bool   `json:"functionAsControl"`
	Builtin                 bool   `json:"builtin"`
}
