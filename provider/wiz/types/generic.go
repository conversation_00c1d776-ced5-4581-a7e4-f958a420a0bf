package types

type GraphQLQuery struct {
	Query     string         `json:"query"`
	Variables map[string]any `json:"variables"`
}

type SecuritySubCategory struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Category struct {
		ID        string `json:"id"`
		Name      string `json:"name"`
		Framework struct {
			ID   string `json:"id"`
			Name string `json:"name"`
		} `json:"framework"`
	} `json:"category"`
}

type Project struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Slug         string `json:"slug"`
	BusinessUnit string `json:"businessUnit"`
	RiskProfile  struct {
		BusinessImpact string `json:"businessImpact"`
	} `json:"riskProfile"`
}

type PageInfo struct {
	HasNextPage bool   `json:"hasNextPage"`
	EndCursor   string `json:"endCursor"`
}
