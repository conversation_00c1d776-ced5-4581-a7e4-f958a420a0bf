package types

import (
	"time"

	"github.com/precize/common"
)

type APIErrorResponse struct {
	Errors []struct {
		Message    string `json:"message"`
		Extensions struct {
			Code string `json:"code"`
		} `json:"extensions"`
	} `json:"errors"`
}

type WizIssuesResponse struct {
	Data struct {
		Issues struct {
			Nodes    []Issue  `json:"nodes"`
			PageInfo PageInfo `json:"pageInfo"`
		} `json:"issues"`
	} `json:"data"`
}

type Issue struct {
	ID              string          `json:"id"`
	SourceRule      SourceRule      `json:"sourceRule"`
	CreatedAt       time.Time       `json:"createdAt"`
	UpdatedAt       time.Time       `json:"updatedAt"`
	DueAt           *time.Time      `json:"dueAt"`
	Type            string          `json:"type"`
	ResolvedAt      *time.Time      `json:"resolvedAt"`
	StatusChangedAt *time.Time      `json:"statusChangedAt"`
	Projects        []Project       `json:"projects"`
	Status          string          `json:"status"`
	Severity        string          `json:"severity"`
	EntitySnapshot  EntitySnapshot  `json:"entitySnapshot"`
	ServiceTickets  []ServiceTicket `json:"serviceTickets"`
	Notes           []Note          `json:"notes"`
}

type SourceRule struct {
	TypeName                 string                `json:"__typename"`
	ID                       string                `json:"id"`
	Name                     string                `json:"name"`
	ControlDescription       string                `json:"controlDescription"`
	ResolutionRecommendation string                `json:"resolutionRecommendation"`
	SecuritySubCategories    []SecuritySubCategory `json:"securitySubCategories"`
}

type EntitySnapshot struct {
	ID                      string             `json:"id"`
	Type                    string             `json:"type"`
	NativeType              string             `json:"nativeType"`
	Name                    string             `json:"name"`
	Status                  string             `json:"status"`
	CloudPlatform           string             `json:"cloudPlatform"`
	CloudProviderURL        string             `json:"cloudProviderURL"`
	ProviderID              string             `json:"providerId"`
	Region                  string             `json:"region"`
	ResourceGroupExternalID string             `json:"resourceGroupExternalId"`
	SubscriptionExternalID  string             `json:"subscriptionExternalId"`
	SubscriptionName        string             `json:"subscriptionName"`
	SubscriptionTags        *map[string]string `json:"subscriptionTags"`
	Tags                    map[string]string  `json:"tags"`
	ExternalID              string             `json:"externalId"`
}

type ServiceTicket struct {
	ExternalID string `json:"externalId"`
	Name       string `json:"name"`
	URL        string `json:"url"`
}

type Note struct {
	CreatedAt      time.Time       `json:"createdAt"`
	UpdatedAt      time.Time       `json:"updatedAt"`
	Text           string          `json:"text"`
	User           *User           `json:"user"`
	ServiceAccount *ServiceAccount `json:"serviceAccount"`
}

type User struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type ServiceAccount struct {
	Name string `json:"name"`
}

type WizAdditionalInfo struct {
	IsInternetFacing       bool                            `json:"isInternetFacing"`
	Type                   string                          `json:"type"`
	RecommendationFindings []common.RecommendationFindings `json:"recommendationFindings"`
	HasExploit             bool                            `json:"hasExploit"`
	HasCisaKevExploit      bool                            `json:"hasCisaKevExploit"`
}
