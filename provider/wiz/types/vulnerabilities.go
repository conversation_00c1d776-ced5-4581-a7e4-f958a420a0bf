package types

import "time"

type WizVulnerabilityFindingsResponse struct {
	Data struct {
		VulnerabilityFindings struct {
			Nodes    []VulnerabilityFinding `json:"nodes"`
			PageInfo PageInfo               `json:"pageInfo"`
		} `json:"vulnerabilityFindings"`
	} `json:"data"`
}

type VulnerabilityFinding struct {
	ID                    string         `json:"id"`
	PortalURL             string         `json:"portalUrl"`
	Name                  string         `json:"name"`
	CVEDescription        string         `json:"CVEDescription"`
	CVSSSeverity          string         `json:"CVSSSeverity"`
	Score                 float64        `json:"score"`
	ExploitabilityScore   float64        `json:"exploitabilityScore"`
	Severity              string         `json:"severity"`
	NvdSeverity           string         `json:"nvdSeverity"`
	WeightedSeverity      *string        `json:"weightedSeverity"`
	ImpactScore           float64        `json:"impactScore"`
	DataSourceName        *string        `json:"dataSourceName"`
	HasExploit            bool           `json:"hasExploit"`
	HasCisaKevExploit     bool           `json:"hasCisaKevExploit"`
	Status                string         `json:"status"`
	VendorSeverity        string         `json:"vendorSeverity"`
	FirstDetectedAt       time.Time      `json:"firstDetectedAt"`
	LastDetectedAt        time.Time      `json:"lastDetectedAt"`
	ResolvedAt            *string        `json:"resolvedAt"`
	Description           string         `json:"description"`
	Remediation           string         `json:"remediation"`
	DetailedName          string         `json:"detailedName"`
	Version               string         `json:"version"`
	FixedVersion          string         `json:"fixedVersion"`
	DetectionMethod       string         `json:"detectionMethod"`
	Link                  string         `json:"link"`
	LocationPath          string         `json:"locationPath"`
	ResolutionReason      *string        `json:"resolutionReason"`
	EpssSeverity          string         `json:"epssSeverity"`
	EpssPercentile        float64        `json:"epssPercentile"`
	EpssProbability       float64        `json:"epssProbability"`
	ValidatedInRuntime    *bool          `json:"validatedInRuntime"`
	CVSSV2                CVSSV2         `json:"cvssv2"`
	CVSSV3                CVSSV3         `json:"cvssv3"`
	RelatedIssueAnalytics RelatedRscInfo `json:"relatedIssueAnalytics"`
	VulnerableAsset       VulnAsset      `json:"vulnerableAsset"`
}

type CVSSV2 struct {
	AttackVector            string `json:"attackVector"`
	AttackComplexity        string `json:"attackComplexity"`
	ConfidentialityImpact   string `json:"confidentialityImpact"`
	IntegrityImpact         string `json:"integrityImpact"`
	PrivilegesRequired      string `json:"privilegesRequired"`
	UserInteractionRequired bool   `json:"userInteractionRequired"`
}

type CVSSV3 struct {
	AttackVector            string `json:"attackVector"`
	AttackComplexity        string `json:"attackComplexity"`
	ConfidentialityImpact   string `json:"confidentialityImpact"`
	IntegrityImpact         string `json:"integrityImpact"`
	PrivilegesRequired      string `json:"privilegesRequired"`
	UserInteractionRequired bool   `json:"userInteractionRequired"`
}

type RelatedRscInfo struct {
	IssueCount                 int `json:"issueCount"`
	CriticalSeverityCount      int `json:"criticalSeverityCount"`
	HighSeverityCount          int `json:"highSeverityCount"`
	MediumSeverityCount        int `json:"mediumSeverityCount"`
	LowSeverityCount           int `json:"lowSeverityCount"`
	InformationalSeverityCount int `json:"informationalSeverityCount"`
}

type VulnAsset struct {
	ID                         string            `json:"id"`
	Type                       string            `json:"type"`
	Name                       string            `json:"name"`
	Region                     string            `json:"region"`
	ProviderUniqueId           string            `json:"providerUniqueId"`
	CloudProviderURL           string            `json:"cloudProviderURL"`
	CloudPlatform              string            `json:"cloudPlatform"`
	NativeType                 string            `json:"nativeType"`
	Status                     string            `json:"status"`
	SubscriptionName           string            `json:"subscriptionName"`
	SubscriptionExternalId     string            `json:"subscriptionExternalId"`
	SubscriptionId             string            `json:"subscriptionId"`
	Tags                       map[string]string `json:"tags"`
	HasLimitedInternetExposure bool              `json:"hasLimitedInternetExposure"`
	HasWideInternetExposure    bool              `json:"hasWideInternetExposure"`
	OperatingSystem            string            `json:"operatingSystem"`
	IPAddresses                []string          `json:"ipAddresses"`
}
