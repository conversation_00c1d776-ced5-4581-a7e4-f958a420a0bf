package wiz

import (
	"bytes"
	"encoding/json"
	"time"

	common "github.com/precize/common/wiz"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/provider/wiz/queries"
	"github.com/precize/transport"
)

const (
	bufferTime         = 24 * time.Hour
	resourceBufferTime = 48 * time.Hour
)

func ValidateAuth(wizEnv tenant.WizEnvironment, tenantID string) (authValidation map[string]bool, err error) {
	authValidation = make(map[string]bool)

	token, err := common.GenerateWizToken(wizEnv, tenantID)
	if err != nil {
		logger.Print(logger.ERROR, "Error generating Wiz token", []string{tenantID}, err)
		authValidation[wizEnv.ClientID] = false
		return authValidation, err
	}

	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": "Bearer " + token,
		"Content-Type":  "application/json",
	}

	graphqlQuery := map[string]any{
		"query": queries.IssuesQuery,
		"variables": map[string]any{
			"first": 1,
		},
	}

	payload, err := json.Marshal(graphqlQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshaling GraphQL query", []string{tenantID}, err)
		authValidation[wizEnv.ClientID] = false
		return authValidation, err
	}

	_, err = transport.SendRequest("POST", wizEnv.URL, nil, headers, bytes.NewReader(payload))
	if err != nil {
		logger.Print(logger.ERROR, "Error validating Wiz authentication", []string{tenantID}, err)
		authValidation[wizEnv.ClientID] = false
		return authValidation, err
	}

	authValidation[wizEnv.ClientID] = true
	logger.Print(logger.INFO, "Wiz authentication successful", []string{tenantID}, nil)
	return authValidation, nil
}

func ProcessWizData(tenantID string, wizEnv tenant.WizEnvironment, fetchProgress map[string]time.Time, endTime, tenantStartTime time.Time, tenantData tenant.TenantData) {

	token, err := common.GenerateWizToken(wizEnv, tenantID)
	if err != nil {
		return
	}

	CollectCloudResources(tenantID, token, wizEnv, fetchProgress[tenant.WIZ_RESOURCE], endTime, tenantStartTime, tenantData)

	// CollectExposedResources(tenantID, token, wizEnv, fetchProgress[tenant.WIZ_RESOURCE], endTime, tenantStartTime, tenantData)

	CollectIssues(tenantID, token, wizEnv, fetchProgress[tenant.WIZ_ISSUE], endTime, tenantStartTime)

	// CollectVulnerabilities(tenantID, token, wizEnv, fetchProgress[tenant.WIZ_VULN], endTime, tenantStartTime)

	CollectRscConfigFindings(tenantID, token, wizEnv, fetchProgress[tenant.WIZ_CONFIG_FINDING], endTime, tenantStartTime)

}
