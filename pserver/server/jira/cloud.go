package jira

import (
	"bytes"
	"encoding/json"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func listJiraCloudProjects(w http.ResponseWriter, reqBody jiraBaseRequest) {
	var (
		projectSearchUrl = reqBody.Jira.URL + "/rest/api/3/project/search"
		basicAuth        = strings.TrimSpace(reqBody.Jira.Username) + ":" + strings.TrimSpace(reqBody.Jira.Token)
	)

	if reqBody.MaxResults <= 0 {
		reqBody.MaxResults = 50
	}

	projectsResp, err := transport.SendRequest(
		"GET",
		projectSearchUrl,
		map[string]string{"startAt": strconv.Itoa(reqBody.StartAt), "maxResults": strconv.Itoa(reqBody.MaxResults), "expand": "issueTypes"},
		map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
		nil,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var jiraListProjectsResp jiraListProjectsResponse

	if err = json.Unmarshal(projectsResp, &jiraListProjectsResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Data: jiraListProjectsResp})
	logger.Print(logger.INFO, "Response sent", jiraListProjectsResp)
}

func listJiraCloudIssueTypeFields(w http.ResponseWriter, reqBody jiraListIssueTypeFieldsRequest) {

	var (
		createMetaUrl = reqBody.Jira.URL + "/rest/api/3/issue/createmeta/" + reqBody.Project + "/issuetypes/" + reqBody.IssueType
		basicAuth     = strings.TrimSpace(reqBody.Jira.Username) + ":" + strings.TrimSpace(reqBody.Jira.Token)
	)

	if reqBody.MaxResults <= 0 {
		reqBody.MaxResults = 50
	}

	fieldsResp, err := transport.SendRequest(
		"GET",
		createMetaUrl,
		map[string]string{"startAt": strconv.Itoa(reqBody.StartAt), "maxResults": strconv.Itoa(reqBody.MaxResults)},
		map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
		nil,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var jiraIssueTypeFieldResp jiraListIssueTypeFieldsResponse

	if err = json.Unmarshal(fieldsResp, &jiraIssueTypeFieldResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Data: jiraIssueTypeFieldResp})
	logger.Print(logger.INFO, "Response sent", jiraIssueTypeFieldResp)
}

func createJiraCloudIssue(w http.ResponseWriter, reqBody jiraCreateIssueRequest) {

	var (
		csvData        = [][]string{{"Resource ID", "Resource Name", "Resource Type", "Account ID", "Account Name"}}
		searchAfter    any
		incidentDocIDs []string
	)

	incidentsWithNoTicketQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + reqBody.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + reqBody.HerostatDBKey + `"}},{"terms":{"serviceId":[` + strings.Join(reqBody.ServiceIDs.Include, `,`) + `]}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"exists":{"field":"pmTicket"}}]}}}`

	if len(reqBody.ServiceIDs.Exclude) > 0 {
		incidentsWithNoTicketQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + reqBody.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + reqBody.HerostatDBKey + `"}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"exists":{"field":"pmTicket"}},{"terms":{"serviceId":[` + strings.Join(reqBody.ServiceIDs.Exclude, `,`) + `]}}]}}}`
	}

	for {

		incidentsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_INCIDENTS_INDEX}, incidentsWithNoTicketQuery, searchAfter)
		if err != nil {
			return
		}

		if len(incidentsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, incidentsDoc := range incidentsDocs {

			incidentsJson, err := json.Marshal(incidentsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal", err)
				continue
			}

			var incidentDoc common.Incident

			if err = json.Unmarshal(incidentsJson, &incidentDoc); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				continue
			}

			owners := incidentDoc.Owner
			envs := incidentDoc.Environment

			if len(owners) >= 2 {
				owners = owners[:2]
			}

			csvData = append(csvData, []string{
				incidentDoc.EntityID,
				incidentDoc.ResourceName,
				incidentDoc.EntityType,
				incidentDoc.AccountID,
				incidentDoc.AccountName,
				strings.Join(owners, " | "),
				strings.Join(envs, " | "),
			})

			incidentDocIDs = append(incidentDocIDs, incidentDoc.ID)
		}
	}

	if len(csvData) < 2 {
		// If only heading of csv is present
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Message: "Ticket already present with all open incidents"})
		logger.Print(logger.INFO, "Response sent", "Ticket already present with all open incidents")
		return
	}

	var (
		createIssueUrl             = reqBody.Jira.URL + "/rest/api/3/issue"
		basicAuth                  = strings.TrimSpace(reqBody.Jira.Username) + ":" + strings.TrimSpace(reqBody.Jira.Token)
		jiraCreateIssueExternalReq jiraCreateIssueExternalRequest
		createIssueReqBuf          bytes.Buffer
	)

	jiraCreateIssueExternalReq.Fields = make(map[string]any)

	for k, v := range reqBody.Fields {
		jiraCreateIssueExternalReq.Fields[k] = v
	}

	err := json.NewEncoder(&createIssueReqBuf).Encode(jiraCreateIssueExternalReq)
	if err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	createIssueResp, err := transport.SendRequest(
		"POST",
		createIssueUrl,
		nil,
		map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth), "Content-Type": "application/json"},
		&createIssueReqBuf,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		jiraCreateIssueExternalResp jiraCreateIssueExternalResponse
		jiraCreateIssueResp         jiraCreateIssueResponse
	)

	if err = json.Unmarshal(createIssueResp, &jiraCreateIssueExternalResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		csvFilename      = strings.Join([]string{reqBody.TenantID, reqBody.HerostatDBKey, strconv.Itoa(int(time.Now().Unix()))}, "_") + ".csv"
		addAttachmentUrl = reqBody.Jira.URL + "/rest/api/3/issue/" + jiraCreateIssueExternalResp.Key + "/attachments"
	)

	if err = common.CreateCSVFile(csvFilename, csvData); err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	defer os.Remove(csvFilename)

	csvFile, _ := os.Open(csvFilename)
	defer csvFile.Close()

	// multipart/form-data content-type implementation for attachment
	addAttachmentReqBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(addAttachmentReqBuff)
	part, _ := writer.CreateFormFile("file", csvFilename)
	io.Copy(part, csvFile)
	writer.Close()

	_, err = transport.SendRequest(
		"POST",
		addAttachmentUrl,
		nil,
		map[string]string{
			"Authorization":     "Basic " + common.EncodeBase64(basicAuth),
			"Content-Type":      writer.FormDataContentType(),
			"X-Atlassian-Token": "no-check",
		},
		addAttachmentReqBuff,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		bulkIncidentRequest string
		recordsCount        int
		maxRecords          = 10000
	)

	for _, incidentDocID := range incidentDocIDs {

		incidentUpdateMetadata := `{"update": {"_id": "` + incidentDocID + `"}}`
		incidentUpdateDoc := `{"doc":{"pmTicket":"` + jiraCreateIssueExternalResp.Key + `"}}`

		bulkIncidentRequest = bulkIncidentRequest + incidentUpdateMetadata + "\n" + incidentUpdateDoc + "\n"

		recordsCount++

		if recordsCount >= maxRecords {

			if err := elastic.BulkDocumentsAPI(reqBody.TenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkIncidentRequest); err != nil {
				json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
				logger.Print(logger.INFO, "Response sent", err.Error())
				return
			}

			logger.Print(logger.INFO, "Cloud incident bulk API Successful (set pmTicket) for "+strconv.Itoa(recordsCount)+" records", []string{reqBody.TenantID})

			recordsCount = 0
			bulkIncidentRequest = ""
		}
	}

	if recordsCount > 0 {

		if err := elastic.BulkDocumentsAPI(reqBody.TenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkIncidentRequest); err != nil {
			json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
			logger.Print(logger.INFO, "Response sent", err.Error())
			return
		}

		logger.Print(logger.INFO, "Cloud incident bulk API Successful (set pmTicket) for "+strconv.Itoa(recordsCount)+" records", []string{reqBody.TenantID})
	}

	jiraCreateIssueResp.TicketID = jiraCreateIssueExternalResp.Key

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Message: "Ticket created", Data: jiraCreateIssueResp})
	logger.Print(logger.INFO, "Response sent", "Ticket created", jiraCreateIssueResp.TicketID)
}

func getJiraCloudClosedIssuesList(baseReq jiraBaseRequest, issues []string) (closedIssues []string, err error) {
	var (
		bulkIssueFetchUrl = baseReq.Jira.URL + "/rest/api/2/issue/bulkfetch"
		basicAuth         = strings.TrimSpace(baseReq.Jira.Username) + ":" + strings.TrimSpace(baseReq.Jira.Token)
		reqBuf            bytes.Buffer
		maxIssues         = 100
		deletedIssuesMap  = make(map[string]struct{})
	)

	for _, issue := range issues {
		deletedIssuesMap[issue] = struct{}{}
	}

	for i := 0; i < len(issues); i += maxIssues {
		// Passing 100 issue keys at a time

		last := i + maxIssues

		if last > len(issues) {
			last = len(issues)
		}

		var jiraBulkFetchIssuesReq = jiraBulkFetchIssuesRequest{
			IssueIdsOrKeys: issues[i:last],
			Fields:         []string{"status"},
		}

		if err = json.NewEncoder(&reqBuf).Encode(jiraBulkFetchIssuesReq); err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{baseReq.TenantID}, err)
			return nil, err
		}

		issuesResp, err := transport.SendRequest(
			"POST",
			bulkIssueFetchUrl,
			nil,
			map[string]string{"Authorization": "Basic " + common.EncodeBase64(basicAuth)},
			&reqBuf,
		)
		if err != nil {
			return nil, err
		}

		var jiraBulkFetchIssuesResp jiraBulkFetchIssuesResponse

		if err = json.Unmarshal(issuesResp, &jiraBulkFetchIssuesResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{baseReq.TenantID}, err)
			return nil, err
		}

		for _, fetchedIssue := range jiraBulkFetchIssuesResp.Issues {
			delete(deletedIssuesMap, fetchedIssue.Key)
			if fetchedIssue.Fields.Status.StatusCategory.Key == "done" {
				closedIssues = append(closedIssues, fetchedIssue.Key)
			}
		}
	}

	for deletedIssue := range deletedIssuesMap {
		closedIssues = append(closedIssues, deletedIssue)
	}

	return
}
