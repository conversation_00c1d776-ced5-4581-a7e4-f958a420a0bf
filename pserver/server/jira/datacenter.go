package jira

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

func listJiraDataCenterProjects(w http.ResponseWriter, reqBody jiraBaseRequest) {

	var (
		projectSearchUrl = reqBody.Jira.URL + "/rest/api/2/project"
		bearerToken      = strings.TrimSpace(reqBody.Jira.Token)
	)

	projectsResp, err := transport.SendRequest(
		"GET",
		projectSearchUrl,
		map[string]string{"expand": "issueTypes"},
		map[string]string{"Authorization": "Bearer " + bearerToken},
		nil,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.<PERSON>rror()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var jiraListProjectsResp jiraListProjectsResponse

	if err = json.Unmarshal(projectsResp, &jiraListProjectsResp.Values); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	for i, proj := range jiraListProjectsResp.Values {
		if len(proj.IssueTypes) <= 0 {
			issueTypes, err := getJiraDataCenterIssueTypesForProject(reqBody, proj.ID)
			if err != nil {
				json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
				logger.Print(logger.INFO, "Response sent", err.Error())
				return
			}
			jiraListProjectsResp.Values[i].IssueTypes = issueTypes
		}
	}

	jiraListProjectsResp.Total = len(jiraListProjectsResp.Values)

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Data: jiraListProjectsResp})
	logger.Print(logger.INFO, "Response sent", jiraListProjectsResp)
}

func listJiraDataCenterIssueTypeFields(w http.ResponseWriter, reqBody jiraListIssueTypeFieldsRequest) {

	var (
		createMetaUrl = reqBody.Jira.URL + "/rest/api/2/issue/createmeta/" + reqBody.Project + "/issuetypes/" + reqBody.IssueType
		bearerToken   = strings.TrimSpace(reqBody.Jira.Token)
	)

	if reqBody.MaxResults <= 0 {
		reqBody.MaxResults = 50
	}

	fieldsResp, err := transport.SendRequest(
		"GET",
		createMetaUrl,
		map[string]string{"startAt": strconv.Itoa(reqBody.StartAt), "maxResults": strconv.Itoa(reqBody.MaxResults)},
		map[string]string{"Authorization": "Bearer " + bearerToken},
		nil,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		jiraDataCenterIssueTypeFieldResp jiraDataCenterListIssueTypeFieldsResponse
		jiraIssueTypeFieldResp           jiraListIssueTypeFieldsResponse
	)

	if err = json.Unmarshal(fieldsResp, &jiraDataCenterIssueTypeFieldResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	jiraIssueTypeFieldResp.jiraBaseResponse = jiraDataCenterIssueTypeFieldResp.jiraBaseResponse
	jiraIssueTypeFieldResp.Fields = append(jiraIssueTypeFieldResp.Fields, jiraDataCenterIssueTypeFieldResp.Values...)

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Data: jiraIssueTypeFieldResp})
	logger.Print(logger.INFO, "Response sent", jiraIssueTypeFieldResp)
}

func createJiraDataCenterIssue(w http.ResponseWriter, reqBody jiraCreateIssueRequest) {

	var (
		csvData        = [][]string{{"Resource ID", "Resource Name", "Resource Type", "Account ID", "Account Name"}}
		searchAfter    any
		incidentDocIDs []string
	)

	incidentsWithNoTicketQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + reqBody.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + reqBody.HerostatDBKey + `"}},{"terms":{"serviceId":[` + strings.Join(reqBody.ServiceIDs.Include, `,`) + `]}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"exists":{"field":"pmTicket"}}]}}}`

	if len(reqBody.ServiceIDs.Exclude) > 0 {
		incidentsWithNoTicketQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + reqBody.TenantID + `"}},{"term":{"heroStatDbKey.keyword":"` + reqBody.HerostatDBKey + `"}},{"term":{"status.keyword":"` + common.INCIDENT_STATUS_OPEN + `"}}],"must_not":[{"exists":{"field":"pmTicket"}},{"terms":{"serviceId":[` + strings.Join(reqBody.ServiceIDs.Exclude, `,`) + `]}}]}}}`
	}

	for {

		incidentsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_INCIDENTS_INDEX}, incidentsWithNoTicketQuery, searchAfter)
		if err != nil {
			return
		}

		if len(incidentsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, incidentsDoc := range incidentsDocs {

			incidentsJson, err := json.Marshal(incidentsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal", err)
				continue
			}

			var incidentDoc common.Incident

			if err = json.Unmarshal(incidentsJson, &incidentDoc); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				continue
			}

			owners := incidentDoc.Owner
			envs := incidentDoc.Environment

			if len(owners) >= 2 {
				owners = owners[:2]
			}

			csvData = append(csvData, []string{
				incidentDoc.EntityID,
				incidentDoc.ResourceName,
				incidentDoc.EntityType,
				incidentDoc.AccountID,
				incidentDoc.AccountName,
				strings.Join(owners, " | "),
				strings.Join(envs, " | "),
			})

			incidentDocIDs = append(incidentDocIDs, incidentDoc.ID)
		}
	}

	if len(csvData) < 2 {
		// If only heading of csv is present
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Message: "Ticket already present with all open incidents"})
		logger.Print(logger.INFO, "Response sent", "Ticket already present with all open incidents")
		return
	}

	var (
		createIssueUrl             = reqBody.Jira.URL + "/rest/api/2/issue"
		bearerToken                = strings.TrimSpace(reqBody.Jira.Token)
		jiraCreateIssueExternalReq jiraCreateIssueExternalRequest
		createIssueReqBuf          bytes.Buffer
	)

	jiraCreateIssueExternalReq.Fields = make(map[string]any)

	for k, v := range reqBody.Fields {
		jiraCreateIssueExternalReq.Fields[k] = v
	}

	err := json.NewEncoder(&createIssueReqBuf).Encode(jiraCreateIssueExternalReq)
	if err != nil {
		logger.Print(logger.ERROR, "Got error encoding request body", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	createIssueResp, err := transport.SendRequest(
		"POST",
		createIssueUrl,
		nil,
		map[string]string{"Authorization": "Bearer " + bearerToken, "Content-Type": "application/json"},
		&createIssueReqBuf,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		jiraCreateIssueExternalResp jiraCreateIssueExternalResponse
		jiraCreateIssueResp         jiraCreateIssueResponse
	)

	if err = json.Unmarshal(createIssueResp, &jiraCreateIssueExternalResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		csvFilename      = strings.Join([]string{reqBody.TenantID, reqBody.HerostatDBKey, strconv.Itoa(int(time.Now().Unix()))}, "_") + ".csv"
		addAttachmentUrl = reqBody.Jira.URL + "/rest/api/2/issue/" + jiraCreateIssueExternalResp.Key + "/attachments"
	)

	if err = common.CreateCSVFile(csvFilename, csvData); err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	defer os.Remove(csvFilename)

	csvFile, _ := os.Open(csvFilename)
	defer csvFile.Close()

	// multipart/form-data content-type implementation for attachment
	addAttachmentReqBuff := &bytes.Buffer{}
	writer := multipart.NewWriter(addAttachmentReqBuff)
	part, _ := writer.CreateFormFile("file", csvFilename)
	io.Copy(part, csvFile)
	writer.Close()

	_, err = transport.SendRequest(
		"POST",
		addAttachmentUrl,
		nil,
		map[string]string{
			"Authorization":     "Bearer " + bearerToken,
			"Content-Type":      writer.FormDataContentType(),
			"X-Atlassian-Token": "no-check",
		},
		addAttachmentReqBuff,
	)
	if err != nil {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var (
		bulkIncidentRequest string
		recordsCount        int
		maxRecords          = 10000
	)

	for _, incidentDocID := range incidentDocIDs {

		incidentUpdateMetadata := `{"update": {"_id": "` + incidentDocID + `"}}`
		incidentUpdateDoc := `{"doc":{"pmTicket":"` + jiraCreateIssueExternalResp.Key + `"}}`

		bulkIncidentRequest = bulkIncidentRequest + incidentUpdateMetadata + "\n" + incidentUpdateDoc + "\n"

		recordsCount++

		if recordsCount >= maxRecords {

			if err := elastic.BulkDocumentsAPI(reqBody.TenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkIncidentRequest); err != nil {
				json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
				logger.Print(logger.INFO, "Response sent", err.Error())
				return
			}

			logger.Print(logger.INFO, "Cloud incident bulk API Successful (set pmTicket) for "+strconv.Itoa(recordsCount)+" records", []string{reqBody.TenantID})

			recordsCount = 0
			bulkIncidentRequest = ""
		}
	}

	if recordsCount > 0 {

		if err := elastic.BulkDocumentsAPI(reqBody.TenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkIncidentRequest); err != nil {
			json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
			logger.Print(logger.INFO, "Response sent", err.Error())
			return
		}

		logger.Print(logger.INFO, "Cloud incident bulk API Successful (set pmTicket) for "+strconv.Itoa(recordsCount)+" records", []string{reqBody.TenantID})
	}

	jiraCreateIssueResp.TicketID = jiraCreateIssueExternalResp.Key

	json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusOK), Data: jiraCreateIssueResp})
	logger.Print(logger.INFO, "Response sent", "Ticket created", jiraCreateIssueResp.TicketID)
}

func getJiraDataCenterClosedIssuesList(baseReq jiraBaseRequest, issues []string) (closedIssues []string, err error) {
	var (
		searchIssueUrl   = baseReq.Jira.URL + "/rest/api/2/search"
		bearerToken      = strings.TrimSpace(baseReq.Jira.Token)
		reqBuf           bytes.Buffer
		maxIssues        = 20
		deletedIssuesMap = make(map[string]struct{})
	)

	for _, issue := range issues {
		deletedIssuesMap[issue] = struct{}{}
	}

	for i := 0; i < len(issues); i += maxIssues {
		// Passing 20 issue keys at a time

		last := i + maxIssues
		if last > len(issues) {
			last = len(issues)
		}

		jqlQuery := "issueKey IN (" + strings.Join(issues[i:last], ", ") + ")"
		jiraSearchReq := map[string]any{
			"jql":        jqlQuery,
			"startAt":    0,
			"maxResults": maxIssues,
			"fields":     []string{"status"},
		}

		if err = json.NewEncoder(&reqBuf).Encode(jiraSearchReq); err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{baseReq.TenantID}, err)
			return nil, err
		}

		issuesResp, err := transport.SendRequest(
			"POST",
			searchIssueUrl,
			nil,
			map[string]string{"Authorization": "Bearer " + bearerToken},
			&reqBuf,
		)
		if err != nil {
			return nil, err
		}

		var jiraSearchResp jiraBulkFetchIssuesResponse

		if err = json.Unmarshal(issuesResp, &jiraSearchResp); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", []string{baseReq.TenantID}, err)
			return nil, err
		}

		for _, fetchedIssue := range jiraSearchResp.Issues {
			delete(deletedIssuesMap, fetchedIssue.Key)
			if fetchedIssue.Fields.Status.StatusCategory.Key == "done" {
				closedIssues = append(closedIssues, fetchedIssue.Key)
			}
		}
	}

	if len(closedIssues) <= 0 {
		for i, issueKey := range issues {
			issueUrl := fmt.Sprintf("%s/rest/api/2/issue/%s", baseReq.Jira.URL, issueKey)
			issueResp, err := transport.SendRequest(
				"GET",
				issueUrl,
				map[string]string{"expand": "status"},
				map[string]string{"Authorization": "Bearer " + bearerToken},
				nil,
			)
			if err != nil {
				delete(deletedIssuesMap, issueKey)
				continue
			}

			var jiraIssueResp jiraDataCenterIssueResponse

			if err = json.Unmarshal(issueResp, &jiraIssueResp); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal individual issue response", []string{baseReq.TenantID}, err)
				continue
			}

			if jiraIssueResp.Fields.Status.StatusCategory.Key == "done" {
				closedIssues = append(closedIssues, jiraIssueResp.Key)
			}

			// Sleep for 2 seconds after every 10 API calls so that rate limit is not hit (10 /sec)
			if i%10 == 0 {
				time.Sleep(1 * time.Second)
			}
		}
	}

	for deletedIssue := range deletedIssuesMap {
		closedIssues = append(closedIssues, deletedIssue)
	}

	return
}

func getJiraDataCenterIssueTypesForProject(baseReq jiraBaseRequest, projKey string) ([]common.JiraIssueType, error) {
	var (
		projectSearchUrl = baseReq.Jira.URL + "/rest/api/2/project/" + projKey + "/statuses"
		bearerToken      = strings.TrimSpace(baseReq.Jira.Token)
	)

	projectsResp, err := transport.SendRequest(
		"GET",
		projectSearchUrl,
		map[string]string{"expand": "issueTypes"},
		map[string]string{"Authorization": "Bearer " + bearerToken},
		nil,
	)
	if err != nil {
		return nil, err
	}

	var jiraListIssueTypeResp []common.JiraIssueType

	if err = json.Unmarshal(projectsResp, &jiraListIssueTypeResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{baseReq.TenantID}, err)
		return nil, err
	}
	return jiraListIssueTypeResp, nil
}
