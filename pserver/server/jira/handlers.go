package jira

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"github.com/precize/logger"
)

func ListJiraProjects(w http.ResponseWriter, r *http.Request) {

	logger.Print(logger.INFO, "Request received to list jira projects")

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var reqBody jiraBaseRequest

	if err = json.Unmarshal(req, &reqBody); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	if len(reqBody.Jira.URL) <= 0 {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if len(reqBody.Jira.Username) <= 0 {
		listJiraDataCenterProjects(w, reqBody)
	} else {
		listJiraCloudProjects(w, reqBody)
	}
}

func ListJiraIssueTypeFields(w http.ResponseWriter, r *http.Request) {

	logger.Print(logger.INFO, "Request received to list jira issuetype fields")

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var reqBody jiraListIssueTypeFieldsRequest

	if err = json.Unmarshal(req, &reqBody); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	if len(reqBody.Jira.URL) <= 0 || len(reqBody.Project) <= 0 || len(reqBody.IssueType) <= 0 {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if len(reqBody.Jira.Username) <= 0 {
		listJiraDataCenterIssueTypeFields(w, reqBody)
	} else {
		listJiraCloudIssueTypeFields(w, reqBody)
	}
}

func CreateJiraIssue(w http.ResponseWriter, r *http.Request) {

	logger.Print(logger.INFO, "Request received to create jira issue")

	req, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request", err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	var reqBody jiraCreateIssueRequest

	if err = json.Unmarshal(req, &reqBody); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", []string{reqBody.TenantID}, err)
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: err.Error()})
		logger.Print(logger.INFO, "Response sent", err.Error())
		return
	}

	if len(reqBody.Jira.URL) <= 0 {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if len(reqBody.HerostatDBKey) <= 0 || (len(reqBody.ServiceIDs.Include) <= 0 && len(reqBody.ServiceIDs.Exclude) <= 0) {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if _, ok := reqBody.Fields["project"]; !ok {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if _, ok := reqBody.Fields["summary"]; !ok {
		json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusBadRequest), Message: "Missing required parameters"})
		logger.Print(logger.INFO, "Response sent", "Missing required parameters")
		return
	}

	if len(reqBody.Jira.Username) <= 0 {
		if err = updateStatusOfExistingPrecizeTickets(reqBody.jiraBaseRequest, reqBody.HerostatDBKey, reqBody.ServiceIDs, JIRA_DATA_CENTER_REQUEST_TYPE); err != nil {
			json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
			logger.Print(logger.INFO, "Response sent", err.Error())
			return
		}

		createJiraDataCenterIssue(w, reqBody)
	} else {
		if err = updateStatusOfExistingPrecizeTickets(reqBody.jiraBaseRequest, reqBody.HerostatDBKey, reqBody.ServiceIDs, JIRA_CLOUD_REQUEST_TYPE); err != nil {
			json.NewEncoder(w).Encode(pserverJiraResponse{Status: strconv.Itoa(http.StatusInternalServerError), Message: err.Error()})
			logger.Print(logger.INFO, "Response sent", err.Error())
			return
		}

		createJiraCloudIssue(w, reqBody)
	}
}
