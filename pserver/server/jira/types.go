package jira

import (
	"github.com/precize/common"
	"github.com/precize/provider/tenant"
)

type pserverJiraResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

type jiraBaseRequest struct {
	TenantID   string                 `json:"tenantId"`
	Jira       tenant.JiraEnvironment `json:"jira"`
	StartAt    int                    `json:"startAt,omitempty"`
	MaxResults int                    `json:"maxResults,omitempty"`
}

type jiraBaseResponse struct {
	Total      int `json:"total,omitempty"`
	StartAt    int `json:"startAt,omitempty"`
	MaxResults int `json:"maxResults,omitempty"`
}

type jiraListProjectsResponse struct {
	jiraBaseResponse
	Values []common.JiraProject `json:"values"`
}

type jiraListIssueTypeFieldsRequest struct {
	jiraBaseRequest
	Project   string `json:"project"`
	IssueType string `json:"issueType"`
}

type jiraListIssueTypeFieldsResponse struct {
	jiraBaseResponse
	Fields []common.JiraIssueTypeField `json:"fields"`
}

type jiraCreateIssueRequest struct {
	jiraBaseRequest
	HerostatDBKey string         `json:"heroStatDbKey"`
	ServiceIDs    serviceIDMap   `json:"serviceIds"`
	Fields        map[string]any `json:"fields"`
}

type serviceIDMap struct {
	Include []string `json:"include"`
	Exclude []string `json:"exclude"`
}

type jiraCreateIssueResponse struct {
	jiraBaseResponse
	TicketID string `json:"ticketId"`
}

type jiraCreateIssueExternalRequest struct {
	Fields map[string]any `json:"fields"`
}

type jiraCreateIssueExternalResponse struct {
	ID   string `json:"id"`
	Key  string `json:"key"`
	Self string `json:"self"`
}

type jiraBulkFetchIssuesRequest struct {
	Expand         []string `json:"expand"`
	Fields         []string `json:"fields"`
	FieldsByKeys   bool     `json:"fieldsByKeys"`
	IssueIdsOrKeys []string `json:"issueIdsOrKeys"`
}

type jiraBulkFetchIssuesResponse struct {
	Issues []struct {
		ID     string          `json:"id"`
		Key    string          `json:"key"`
		Fields jiraIssueFields `json:"fields"`
	} `json:"issues"`
}

type jiraIssueFields struct {
	Status jiraFieldStatus `json:"status"`
}

type jiraFieldStatus struct {
	Key            string `json:"key"`
	StatusCategory struct {
		Key string `json:"key"`
	} `json:"statusCategory"`
}

type jiraDataCenterIssueResponse struct {
	Key    string          `json:"key"`
	Fields jiraIssueFields `json:"fields"`
}

type jiraDataCenterListIssueTypeFieldsResponse struct {
	jiraBaseResponse
	Values []common.JiraIssueTypeField `json:"values"`
}
