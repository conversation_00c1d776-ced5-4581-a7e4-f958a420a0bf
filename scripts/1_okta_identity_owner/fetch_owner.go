package main

import (
	"encoding/json"
	"fmt"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	TENANT_ID = "1SO49IoBbC0Roq8vMMWC"
)

type UserSearchResponse struct {
	Hits struct {
		Hits []struct {
			Source common.IDPUsersDoc `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

type EventAggregationResponse struct {
	Aggregations struct {
		ActorIds struct {
			Buckets []struct {
				Key      string `json:"key"`
				DocCount int    `json:"doc_count"`
			} `json:"buckets"`
		} `json:"actor_ids"`
	} `json:"aggregations"`
}

func getUserIdByEmail(email string) (string, error) {
	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"email.keyword": "%s"
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, email)

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return "", fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		return "", nil
	}

	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return "", fmt.Errorf("failed to marshal document: %w", err)
	}

	var user common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &user); err != nil {
		return "", fmt.Errorf("failed to unmarshal user document: %w", err)
	}

	return user.UserID, nil
}

func getActorIdsForTargetUser(targetUserId string) ([]string, error) {

	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"nested": {
							"path": "targets",
							"query": {
								"bool": {
									"must": [
										{
											"match": {
												"targets.targetId": "%s"
											}
										}
									]
								}
							}
						}
					}
				]
			}
		},
		"size": 0,
		"aggs": {
			"actor_ids": {
				"terms": {
					"field": "actorId.keyword",
					"size": 100
				}
			}
		}
	}`, TENANT_ID, targetUserId)

	aggResponse, err := elastic.ExecuteSearchForAggregation([]string{elastic.IDP_EVENTS_INDEX}, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute aggregation query: %w", err)
	}

	var actorIds []string
	if actorIdsAgg, ok := aggResponse["actor_ids"].(map[string]interface{}); ok {
		if buckets, ok := actorIdsAgg["buckets"].([]interface{}); ok {
			for _, bucket := range buckets {
				if bucketMap, ok := bucket.(map[string]interface{}); ok {
					if actorId, ok := bucketMap["key"].(string); ok {
						actorIds = append(actorIds, actorId)
					}
				}
			}
		}
	}

	return actorIds, nil
}

func printManagerDetails(managerEmail string) error {

	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"email.keyword": "%s"
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, managerEmail)

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		logger.Print(logger.INFO, "No manager details found for email: %s\n", managerEmail)
		return nil
	}

	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	var manager common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &manager); err != nil {
		return fmt.Errorf("failed to unmarshal manager document: %w", err)
	}

	logger.Print(logger.INFO, "Manager Email: %s\n", manager.Email)
	logger.Print(logger.INFO, "Manager Name: %s\n", manager.Name)
	logger.Print(logger.INFO, "Manager Department: %s\n", manager.Department)
	logger.Print(logger.INFO, "Manager Job Title: %s\n", manager.Title)

	return nil
}

func printActorDetails(actorId string, hit *bool, origEmail string) error {

	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"match": {
							"userId.keyword": "%s"
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, actorId)

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		logger.Print(logger.INFO, "No user details found for actor ID: %s\n", actorId)
		return nil
	}

	*hit = true

	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	var user common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &user); err != nil {
		return fmt.Errorf("failed to unmarshal user document: %w", err)
	}

	logger.Print(logger.INFO, "Onwer Information for identity", origEmail)
	logger.Print(logger.INFO, "Email: %s\n", user.Email)
	logger.Print(logger.INFO, "Name: %s\n", user.Name)
	logger.Print(logger.INFO, "Job Title: %s\n", user.Title)
	logger.Print(logger.INFO, "Department: %s\n", user.Department)

	if user.ManagerID != "" {
		logger.Print(logger.INFO, "\n--- Manager Details ---\n")
		err := printManagerDetails(user.ManagerID)
		if err != nil {
			logger.Print(logger.ERROR, "Error getting manager details: %v\n", err)
		}
	}

	return nil
}

func getManagerUserIdByEmail(email string) (string, error) {

	query := fmt.Sprintf(`{
		"query": {
			"bool": {
				"must": [
					{
						"match": {
							"tenantId.keyword": "%s"
						}
					},
					{
						"terms": {
							"email.keyword": ["%s"]
						}
					}
				]
			}
		},
		"size": 1
	}`, TENANT_ID, email)

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.IDP_USERS_INDEX}, query)
	if err != nil {
		return "", fmt.Errorf("failed to execute search query: %w", err)
	}

	if len(docs) == 0 {
		return "", nil
	}

	docBytes, err := json.Marshal(docs[0])
	if err != nil {
		return "", fmt.Errorf("failed to marshal document: %w", err)
	}

	var user common.IDPUsersDoc
	if err := json.Unmarshal(docBytes, &user); err != nil {
		return "", fmt.Errorf("failed to unmarshal user document: %w", err)
	}

	if user.ManagerID != "" {
		managerUserId, err := getUserIdByEmail(user.ManagerID)
		if err != nil {
			return "", fmt.Errorf("failed to get manager userId: %w", err)
		}
		return managerUserId, nil
	}

	return "", nil
}

func FetchOwnerFromOktaIdentity() {
	emails := []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}

	for _, email := range emails {
		logger.Print(logger.INFO, "\n=== Processing email: %s ===\n", email)

		userId, err := getUserIdByEmail(email)
		if err != nil {
			logger.Print(logger.ERROR, "Error finding user for email %s: %v\n", email, err)
			continue
		}

		if userId == "" {
			logger.Print(logger.INFO, "No user found for email: %s\n", email)
			continue
		}

		actorIds, err := getActorIdsForTargetUser(userId)
		if err != nil {
			logger.Print(logger.ERROR, "Error getting actor IDs for user %s: %v\n", userId, err)
			continue
		}

		if len(actorIds) == 0 {

			managerUserId, err := getManagerUserIdByEmail(email)
			if err != nil {
				logger.Print(logger.ERROR, "Error getting manager for email %s: %v\n", email, err)
				continue
			}

			if managerUserId != "" {
				actorIds = append(actorIds, managerUserId)
			} else {
				continue
			}
		}

		for _, actorId := range actorIds {
			hit := false
			if actorId != userId {
				err := printActorDetails(actorId, &hit, email)
				if err != nil {
					logger.Print(logger.ERROR, "Error getting details for actor %s: %v\n", actorId, err)
				}

				if hit {
					break
				}
			}
		}
	}
}
