package main

import (
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/transport"
)

// Date - 08/07/2025
// Target - IDP USERS and EVENTS
// This migration is to fetch owner info from okta identity

func main() {
	logger.InitializeLogs("migration", false)

	defaultConf, err := config.InitializeApplicationConfig("application.yml")
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", "application.yml")
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()
	gracefullyShutDown()

	FetchOwnerFromOktaIdentity()
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.ERROR, logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
