# Vader


### Monitor

Vader Monitor is responsible for monitoring and health surveillance of all Precize services and corresponding infra.
Vader Monitor is to be run as a Linux Cronjob

#### Command to run Vader Monitor 

- ##### Linux cronjob

  - Edit crontab

    `crontab -e`

  - Add monitor command in crontab with required interval (Below example is for every minute)

    `* * * * * /path/to/vader-m --config=/path/to/vader.json &`

- ##### Manually once

  `/path/to/vader-m --config=/path/to/vader.json &`


### Deploy

Vader Deploy is responsible for deploying all Precize services.

#### Command to run Vader Deploy 

 - Selected services

  `/path/to/vader-d --config=/path/to/vader.json --user=username --services=platform,provider,enhancer`
   
 - All services

  `/path/to/vader-d --config=/path/to/vader.json --user=username --all-services`
  
 - All provider services

  `/path/to/vader-d --config=/path/to/vader.json --user=username --provider-services`
