{"machine": "Production Server", "prod": true, "devicePaths": ["/", "/app"], "services": {"platform": {"name": "Platform", "processPath": "/app/precize-server/precize-aws-0.0.1-SNAPSHOT.jar", "processName": "precize-aws-0.0.1-SNAPSHOT.jar", "type": "platform", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner/_work/precize-production/precize-production/precize-aws/target/precize-aws-0.0.1-SNAPSHOT.jar", "monitor": true, "autoRestart": false}, "provider": {"name": "Provider", "processPath": "/app/precize-provider/precize-provider", "processName": "precize-provider", "type": "provider", "command": "/app/precize-provider/precize-provider --config /app/precize-server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-provider", "monitor": true, "autoRestart": true, "restartFailed": false}, "enhancer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/app/precize-server/precize-enhancer", "processName": "precize-enhancer", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-enhancer", "monitor": false}, "prioritiser": {"name": "Prioritiser", "processPath": "/app/precize-server/incident-prioritiser", "processName": "incident-prioritiser", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-prioritiser", "monitor": false}, "externaldc": {"name": "External DataCollector", "processPath": "/app/precize-server/precize-ext-dc", "processName": "precize-ext-dc", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-externaldc", "monitor": false}, "pserver": {"name": "Provider Server", "processPath": "/app/precize-provider/precize-pserver", "processName": "precize-pserver", "type": "provider", "command": "/app/precize-provider/precize-pserver --config /app/precize-server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-pserver", "monitor": true, "autoRestart": true, "restartFailed": false}, "analyzer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/app/precize-analyzer/precize-analyzer", "processName": "precize-analyzer", "type": "provider", "command": "/app/precize-analyzer/precize-analyzer --config /app/precize-server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-analyzer", "monitor": true, "autoRestart": true, "restartFailed": false}, "fetch8k": {"name": "Fetch 8k", "processPath": "/app/fetch8k/fetch8k", "processName": "fetch8k", "type": "other", "command": "/app/fetch8k/fetch8k >> /app/fetch8k/fetch8k.log 2>&1 &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/fetch8k", "monitor": true, "autoRestart": true, "restartFailed": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}}