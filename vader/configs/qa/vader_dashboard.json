{"machine": "dashboard", "prod": false, "devicePaths": ["/", "/app"], "services": {"platform": {"name": "Platform", "processPath": "/app/precize-server/precize-dashboard-0.0.1-SNAPSHOT.jar", "processName": "precize-dashboard-0.0.1-SNAPSHOT.jar", "type": "platform", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/servers/dashboard/target/precize-dashboard-0.0.1-SNAPSHOT.jar", "configFiles": ["application.yml"], "monitor": true, "autoRestart": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}, "jumpMachines": {"elastic-datanode-1": {"id": "data-node-01", "name": "Elastic Data Node 1", "devicePaths": ["/", "/app"], "type": "elastic", "monitor": true}, "elastic-datanode-2": {"id": "data-node-02", "name": "Elastic Data Node 2", "devicePaths": ["/", "/app"], "type": "elastic", "monitor": true}}, "files": {"application.yml": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/config/qa/application-dash.yml", "filePath": "/app/precize-server/application.yml"}}}