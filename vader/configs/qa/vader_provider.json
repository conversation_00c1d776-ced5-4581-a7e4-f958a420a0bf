{"machine": "provider", "prod": false, "devicePaths": ["/", "/app"], "services": {"provider": {"name": "Provider", "processPath": "/app/precize-provider/precize-provider", "processName": "precize-provider", "type": "provider", "command": "/app/precize-provider/precize-provider --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-provider", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "pserver": {"name": "Provider Server", "processPath": "/app/precize-pserver/precize-pserver", "processName": "precize-pserver", "type": "provider", "command": "/app/precize-pserver/precize-pserver --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-pserver", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "analyzer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/app/precize-analyzer/precize-analyzer", "processName": "precize-analyzer", "type": "provider", "command": "/app/precize-analyzer/precize-analyzer --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-analyzer", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}, "files": {"application.yml": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/config/qa/application-provider.yml", "filePath": "/app/precize-provider/application.yml"}}}