{"machine": "QA Server", "prod": false, "devicePaths": [], "services": {"platform": {"name": "Platform", "processPath": "/home/<USER>/server/precize-aws-0.0.1-SNAPSHOT.jar", "processName": "precize-aws-0.0.1-SNAPSHOT.jar", "type": "platform", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner/_work/precize-production/precize-production/precize-aws/target/precize-aws-0.0.1-SNAPSHOT.jar", "monitor": true, "autoRestart": false}, "provider": {"name": "Provider", "processPath": "/home/<USER>/provider/precize-provider", "processName": "precize-provider", "type": "provider", "command": "/home/<USER>/provider/precize-provider --config /home/<USER>/server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-provider", "monitor": true, "autoRestart": true, "restartFailed": false}, "enhancer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/home/<USER>/server/precize-enhancer", "processName": "precize-enhancer", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-enhancer", "monitor": false}, "prioritiser": {"name": "Prioritiser", "processPath": "/home/<USER>/server/incident-prioritiser", "processName": "incident-prioritiser", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-prioritiser", "monitor": false}, "externaldc": {"name": "External DataCollector", "processPath": "/home/<USER>/server/precize-ext-dc", "processName": "precize-ext-dc", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-externaldc", "monitor": false}, "pserver": {"name": "Provider Server", "processPath": "/home/<USER>/provider/precize-pserver", "processName": "precize-pserver", "type": "provider", "command": "/home/<USER>/provider/precize-pserver --config /home/<USER>/server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-pserver", "monitor": true, "autoRestart": true, "restartFailed": false}, "analyzer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/home/<USER>/analyzer/precize-analyzer", "processName": "precize-analyzer", "type": "provider", "command": "/home/<USER>/analyzer/precize-analyzer --config /home/<USER>/server/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-analyzer", "monitor": true, "autoRestart": true, "restartFailed": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/home/<USER>/vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}}