package main

import (
	"encoding/json"
	"flag"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func main() {

	logger.InitializeLogs("vader-d", false)
	logger.Print(logger.INFO, "-------------------------")
	logger.Print(logger.INFO, "Starting Vader Deployment")
	logger.Print(logger.INFO, "-------------------------")

	var (
		appConfigPath          = flag.String("config", "vader.json", "Path to vader.json")
		user                   = flag.String("user", "", "User triggering the deployment")
		bootup                 = flag.Bool("bootup", false, "If machine boot-up, set true")
		bounce                 = flag.Bool("bounce", false, "If no deployment required, only restart")
		deployAllServices      = flag.Bool("all-services", false, "To deploy all services")
		deployProviderServices = flag.Bool("provider-services", false, "To deploy all provider services")
		deployServices         = flag.String("services", "", "Service(s) to deploy")
	)

	flag.Parse()

	if len(*user) <= 0 {
		logger.Print(logger.INFO, "Flag 'user' has to be specified")
		os.Exit(1)
	} else {
		logger.Print(logger.INFO, "Deployment triggered by", *user)

		servicesLog := "Deployment triggered for "

		if *bounce {
			servicesLog = "Bounce triggered for "
		}

		if *deployAllServices {
			servicesLog += "all services"
		} else {

			var and string

			if *deployProviderServices {
				servicesLog += "all provider services "
				and = "and "
			}

			if len(*deployServices) > 0 {
				servicesLog += and + "services - " + *deployServices
			}
		}

		logger.Print(logger.INFO, servicesLog)
	}

	if *bootup && *bounce {
		logger.Print(logger.INFO, "Bootup and Bounce cannot work together")
		os.Exit(1)
	}

	configFile := *appConfigPath

	dat, err := os.ReadFile(configFile)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read file", err)
		return
	}

	var conf types.VaderConfig

	if err = json.Unmarshal(dat, &conf); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	services := strings.Split(*deployServices, ",")

	ex, _ := os.Executable()
	exPath := filepath.Dir(ex)
	artifactsDir := exPath + "/artifacts"
	if err = os.MkdirAll(artifactsDir, os.ModePerm); err != nil {
		logger.Print(logger.ERROR, "Failed to make artifacts directory", err)
		return
	}

	var downloadedFiles = make(map[string]struct{})

	for k, service := range conf.Services {

		if slices.Contains(services, k) ||
			*deployAllServices ||
			(*deployProviderServices && service.Type == "provider") {

			processSearchKeyword := service.ProcessPath
			if k == "platform" {
				processSearchKeyword = service.ProcessName
			}

			if *bounce {

				logger.Print(logger.INFO, "Bouncing service", []string{service.Name})

				existingPidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
					continue
				}

				if len(existingPidBytes) > 0 {

					logger.Print(logger.INFO, "Stopping existing process", []string{service.Name})

					if _, err = exec.Command("sh", "-c", "kill -9 $(ps -ef | grep '"+processSearchKeyword+
						"' | grep -v 'grep' | awk '{print $2}')").CombinedOutput(); err != nil {
						logger.Print(logger.ERROR, "Failed to stop service", []string{service.Name}, err)
						continue
					}
				}
				logger.Print(logger.INFO, "Starting process", []string{service.Name})

				exec.Command("sh", "-c", service.Command).Run()
				time.Sleep(2 * time.Second)

				pidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+
					"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Service restart pid command failed", []string{service.Name}, err)
					continue
				}

				if len(pidBytes) <= 0 {
					logger.Print(logger.ERROR, "Process start command failed", []string{service.Name}, err)
					continue
				}

				logger.Print(logger.INFO, "Started process", []string{service.Name})
				logger.Print(logger.INFO, "Bounced service", []string{service.Name})
			} else {

				logger.Print(logger.INFO, "Deploying service", service.Name)
				logger.Print(logger.INFO, "Downloading artifact", []string{service.Name})

				newArtifactPath := artifactsDir + "/" + service.ProcessName

				if _, err = exec.Command("sh", "-c", "scp "+service.ArtifactDownloadPath+" "+newArtifactPath).CombinedOutput(); err != nil {
					logger.Print(logger.ERROR, "Failed to download service artifact", []string{service.Name}, err)
					continue
				}

				existingPidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
					continue
				}

				if len(existingPidBytes) > 0 {

					logger.Print(logger.INFO, "Stopping existing process", []string{service.Name})

					if _, err = exec.Command("sh", "-c", "kill -9 $(ps -ef | grep '"+processSearchKeyword+
						"' | grep -v 'grep' | awk '{print $2}')").CombinedOutput(); err != nil {
						logger.Print(logger.ERROR, "Failed to stop service", []string{service.Name}, err)
						continue
					}
				}

				if _, err = os.Stat(service.ProcessPath); err == nil {

					logger.Print(logger.INFO, "Backing up old artifact", []string{service.Name})

					backupPath := strings.TrimSuffix(service.ProcessPath, filepath.Ext(service.ProcessPath)) +
						"_bkup" + filepath.Ext(service.ProcessPath)

					if err = os.Rename(service.ProcessPath, backupPath); err != nil {
						logger.Print(logger.ERROR, "Failed to backup old artifact", []string{service.Name}, err)
					}

					logger.Print(logger.INFO, "Replacing old artifact", []string{service.Name})

					if err = os.Rename(newArtifactPath, service.ProcessPath); err != nil {
						logger.Print(logger.ERROR, "Failed to replace artifact", []string{service.Name}, err)
						continue
					}
				} else {
					logger.Print(logger.INFO, "Old artifact not found. Initializing new artifact", []string{service.Name})

					if err = os.Rename(newArtifactPath, service.ProcessPath); err != nil {
						logger.Print(logger.ERROR, "Failed to initialize artifact", []string{service.Name}, err)
						continue
					}
				}

				for _, serviceConfigFile := range service.ConfigFiles {
					// Check if already downloaded
					if _, ok := downloadedFiles[serviceConfigFile]; !ok {
						if file, ok := conf.Files[serviceConfigFile]; ok {
							if _, err = exec.Command("sh", "-c", "scp "+file.DownloadPath+" "+file.FilePath).CombinedOutput(); err != nil {
								logger.Print(logger.ERROR, "Failed to download config file", []string{service.Name}, serviceConfigFile, err)
								continue
							}
							downloadedFiles[serviceConfigFile] = struct{}{}
						}
					}
				}

				if len(service.Command) > 0 && !*bootup {

					logger.Print(logger.INFO, "Starting process", []string{service.Name})

					exec.Command("sh", "-c", service.Command).Run()
					time.Sleep(2 * time.Second)

					pidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+
						"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
					if err != nil {
						logger.Print(logger.ERROR, "Service restart pid command failed", []string{service.Name}, err)
						continue
					}

					if len(pidBytes) <= 0 {
						logger.Print(logger.ERROR, "Process start command failed", []string{service.Name}, err)
						continue
					}

					logger.Print(logger.INFO, "Started process", []string{service.Name})
				}

				logger.Print(logger.INFO, "Service deployment completed", []string{service.Name})
			}
		}
	}

	logger.Print(logger.INFO, "Vader deployment completed")
}
