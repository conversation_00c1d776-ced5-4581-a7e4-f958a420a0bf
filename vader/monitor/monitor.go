package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

const (
	SERVICE_DOWN_SUBJECT          = "Critical service went down"
	LOW_DISK_SPACE_SUBJECT        = "Disk space exceeded threshold"
	SERVICE_RESTARTFAILED_SUBJECT = "Critical service restart failed"

	EMAIL_INTRO  = "Team,\n\n"
	EMAIL_ENDING = "\n\n- Vader"
)

func main() {

	logger.InitializeLogs("vader-m", false)
	logger.Print(logger.INFO, "-------------------------")
	logger.Print(logger.INFO, "Starting Vader Monitoring")
	logger.Print(logger.INFO, "-------------------------")

	var appConfigPath = flag.String("config", "vader.json", "Path to vader.json")
	flag.Parse()

	configFile := *appConfigPath

	dat, err := os.ReadFile(configFile)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read file", err)
		return
	}

	var conf types.VaderConfig

	if err = json.Unmarshal(dat, &conf); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	if appConfigFile, ok := conf.Files["application.yml"]; ok {
		config.InitializeApplicationConfig(appConfigFile.FilePath)
	}

	vaderPIdBytes, err := exec.Command("sh", "-c", "ps -ef | grep 'vader-m' | grep -v 'grep' | wc -l").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Vader monitor pid command failed", err)
		return
	}

	vaderInstanceCount, _ := strconv.Atoi(string(vaderPIdBytes))
	if vaderInstanceCount > 1 {
		// Another instance running
		logger.Print(logger.INFO, "A vader check is already in progress. Exiting")
		return
	}

	logger.Print(logger.INFO, "Checking deployment status")

	deploymentPIdBytes, err := exec.Command("sh", "-c", "ps -ef | grep 'vader-d' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Vader deployment pid command failed", err)
		return
	}

	if len(deploymentPIdBytes) > 0 {
		logger.Print(logger.INFO, "Deployment is in progress. Exiting Vader Monitoring")
		return
	}

	logger.Print(logger.INFO, "Starting service checks")

	for k, service := range conf.Services {

		if service.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{service.Name})

			processSearchKeyword := service.ProcessPath
			if k == "platform" {
				processSearchKeyword = service.ProcessName
			}

			pidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
				continue
			}

			tmp := conf.Services[k]

			if len(pidBytes) <= 0 {

				logger.Print(logger.INFO, "Service is not running", []string{service.Name})
				tmp.LastDownTime = time.Now()

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(service.LastDownEmail) > emailTimeDiff

				if service.AutoRestart {

					logger.Print(logger.INFO, "Restarting service", []string{service.Name})

					exec.Command("sh", "-c", service.Command).Run()
					time.Sleep(2 * time.Second)

					if pidBytes, err = exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+
						"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput(); err != nil {
						logger.Print(logger.ERROR, "Service restart pid command failed", []string{service.Name}, err)
						continue
					}

					if len(pidBytes) <= 0 {
						logger.Print(logger.ERROR, "Process restart command failed", []string{service.Name}, err)
						tmp.RestartFailed = true

						body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService '" + service.Name + "' restart failed. Attention Required"

					} else {
						logger.Print(logger.INFO, "Service restarted", []string{service.Name})

						body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService Restarted."
					}

					tmp.LastAutoRestartTime = time.Now()
				} else {

					logger.Print(logger.INFO, "Auto restart is disabled", []string{service.Name})

					body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
					body += "\nAuto restart is disabled for service. Attention required."
				}

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  service.Name,
				}
				sendEmail(content, noRecentEmail, conf.Prod, &tmp.LastDownEmail)

				if tmp.RestartFailed {
					continue
				}
			}

			tmp.RestartFailed = false
			conf.Services[k] = tmp

			logger.Print(logger.INFO, "Getting service stats", []string{service.Name})

			pid := strings.TrimSuffix(string(pidBytes), "\n")

			stats, err := exec.Command("sh", "-c", "ps -p "+pid+" -o %cpu,%mem").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Process statistics command failed", []string{service.Name}, err)
				continue
			}

			logger.Print(logger.INFO, "Stats for process", []string{service.Name}, "\n"+string(stats))
		}
	}

	logger.Print(logger.INFO, "Starting machine checks")

	for _, devicePath := range conf.DevicePaths {

		logger.Print(logger.INFO, "Checking disk space", []string{devicePath})

		totalSize, err := exec.Command("sh", "-c", "df -H | awk '$6 == \""+devicePath+"\" {print $2}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk total size command failed: ", []string{devicePath}, err)
			continue
		}

		if len(totalSize) <= 0 {
			continue
		}

		availableSize, err := exec.Command("sh", "-c", "df -H | awk '$6 == \""+devicePath+"\" {print $4}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk available size command failed: ", []string{devicePath}, err)
			continue
		}

		totalSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(totalSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Total size float conversion failed: ", []string{devicePath}, err)
			continue
		}

		availableSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(availableSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Available size float conversion failed: ", []string{devicePath}, err)
			continue
		}

		percentage := (availableSizeFloat * 100 / totalSizeFloat)

		logger.Print(logger.INFO, "Available space", []string{devicePath}, fmt.Sprintf("%.2f", percentage))

		if percentage <= 15 {

			logger.Print(logger.INFO, "Exceeded threshold disk space on device path", []string{devicePath})
			conf.LastThresholdExceededTime = time.Now()

			noRecentEmail := time.Now().Sub(conf.LastThresholdExceededEmail) > 12*time.Hour
			body := "Disk space left is " + fmt.Sprintf("%.2f", percentage) + " percent in machine " + conf.Machine + " for device path '" + devicePath + "'"
			body = EMAIL_INTRO + body + EMAIL_ENDING
			content := emailContent{
				Subject: LOW_DISK_SPACE_SUBJECT,
				Body:    body,
				Entity:  devicePath,
			}
			sendEmail(content, noRecentEmail, conf.Prod, &conf.LastThresholdExceededEmail)
		}
	}

	logger.Print(logger.INFO, "Starting jump machine checks")

	for _, machine := range conf.JumpMachines {

		if machine.Monitor {

			for _, devicePath := range machine.DevicePaths {

				logger.Print(logger.INFO, "Checking disk space for jump machine", []string{machine.ID, devicePath})

				var machineIP string

				if machine.Type == "elastic" {
					if len(config.AppConfig.Spring.ElasticSearch.Host) > 0 {
						machineIP = config.AppConfig.Spring.ElasticSearch.Host
					} else {
						continue
					}
				}

				totalSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" \"ssh -o StrictHostKeyChecking=no "+machine.ID+" 'df -H'\" | awk '$6 == \""+devicePath+"\" {print $2}'").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Disk total size command failed: ", []string{devicePath}, err)
					continue
				}

				if len(totalSize) <= 0 {
					continue
				}

				availableSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" \"ssh -o StrictHostKeyChecking=no "+machine.ID+" 'df -H'\" | awk '$6 == \""+devicePath+"\" {print $4}'").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Disk available size command failed: ", []string{devicePath}, err)
					continue
				}

				totalSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(totalSize), "G\n"), 64)
				if err != nil {
					logger.Print(logger.ERROR, "Total size float conversion failed: ", []string{devicePath}, err)
					continue
				}

				availableSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(availableSize), "G\n"), 64)
				if err != nil {
					logger.Print(logger.ERROR, "Available size float conversion failed: ", []string{devicePath}, err)
					continue
				}

				percentage := (availableSizeFloat * 100 / totalSizeFloat)

				logger.Print(logger.INFO, "Available space for alternate machine", []string{machine.ID, devicePath}, fmt.Sprintf("%.2f", percentage))

				if percentage <= 15 {

					logger.Print(logger.INFO, "Exceeded threshold disk space on device path for alternate machine", []string{machine.ID, devicePath})
					conf.LastThresholdExceededTime = time.Now()

					noRecentEmail := time.Now().Sub(conf.LastThresholdExceededEmail) > 12*time.Hour
					body := "Disk space left is " + fmt.Sprintf("%.2f", percentage) + " percent in machine " + machine.ID + " for device path '" + devicePath + "'"
					body = EMAIL_INTRO + body + EMAIL_ENDING
					content := emailContent{
						Subject: LOW_DISK_SPACE_SUBJECT,
						Body:    body,
						Entity:  devicePath,
					}
					sendEmail(content, noRecentEmail, conf.Prod, &conf.LastThresholdExceededEmail)
				}
			}
		}
	}

	if dat, err = json.MarshalIndent(conf, "", "\t"); err != nil {
		logger.Print(logger.ERROR, "Failed to marshal", err)
	}

	if err = os.WriteFile(configFile, dat, 0644); err != nil {
		logger.Print(logger.ERROR, "Failed to write to file", err)
	}

	logger.Print(logger.INFO, "Vader checks completed")
}

type emailContent struct {
	Subject string
	Body    string
	Entity  string
}

func sendEmail(mailContent emailContent, noRecentEmail, prod bool, lastEmailTime *time.Time) error {

	if prod && noRecentEmail {
		logger.Print(logger.INFO, "Sending email with subject", []string{mailContent.Entity}, mailContent.Subject)

		m := mail.NewV3Mail()

		from := mail.NewEmail("Vader Monitor", "<EMAIL>")
		content := mail.NewContent("text/plain", mailContent.Body)

		m.SetFrom(from)
		m.AddContent(content)

		personalization := mail.NewPersonalization()

		to1 := mail.NewEmail("Abhay", "<EMAIL>")
		to2 := mail.NewEmail("Sai", "<EMAIL>")
		to3 := mail.NewEmail("Prasad", "<EMAIL>")
		to4 := mail.NewEmail("Aniket", "<EMAIL>")

		personalization.AddTos(to1, to2, to3, to4)
		personalization.Subject = mailContent.Subject

		m.AddPersonalizations(personalization)

		request := sendgrid.GetRequest("*********************************************************************", "/v3/mail/send", "https://api.sendgrid.com")
		request.Method = "POST"
		request.Body = mail.GetRequestBody(m)
		response, err := sendgrid.API(request)
		if err != nil || (response.StatusCode < 200 && response.StatusCode >= 300) {
			logger.Print(logger.ERROR, "Email sending failed", err, response.StatusCode)
			return err
		} else {
			logger.Print(logger.INFO, "Email sent", []string{mailContent.Entity})
			*lastEmailTime = time.Now()
		}
	} else if !prod {
		logger.Print(logger.INFO, "Not production machine. Emails are disabled", []string{mailContent.Entity})
	} else {
		logger.Print(logger.INFO, "Email already sent at "+lastEmailTime.String(), []string{mailContent.Entity})
	}

	return nil
}
