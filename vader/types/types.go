package types

import (
	"time"
)

type VaderConfig struct {
	Machine                    string                 `json:"machine"`
	Prod                       bool                   `json:"prod"`
	DevicePaths                []string               `json:"devicePaths"`
	LastThresholdExceededTime  time.Time              `json:"lastThresholdExceededTime,omitempty"`
	LastThresholdExceededEmail time.Time              `json:"lastThresholdExceededEmail,omitempty"`
	Services                   map[string]Service     `json:"services"`
	JumpMachines               map[string]JumpMachine `json:"jumpMachines"`
	Files                      map[string]File        `json:"files"`
}

type Service struct {
	Name                 string    `json:"name"`
	ProcessPath          string    `json:"processPath"`
	ProcessName          string    `json:"processName"`
	Command              string    `json:"command,omitempty"`
	Type                 string    `json:"type"`
	ArtifactDownloadPath string    `json:"artifactDownloadPath"`
	ConfigFiles          []string  `json:"configFiles"`
	LastAutoRestartTime  time.Time `json:"lastAutoRestartTime,omitempty"`
	LastDownTime         time.Time `json:"lastDownTime,omitempty"`
	LastDownEmail        time.Time `json:"lastDownEmail,omitempty"`
	Monitor              bool      `json:"monitor"`
	AutoRestart          bool      `json:"autoRestart,omitempty"`
	RestartFailed        bool      `json:"restartFailed,omitempty"`
}

type JumpMachine struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	DevicePaths []string `json:"devicePaths"`
	Type        string   `json:"type"`
	Monitor     bool     `json:"monitor"`
}

type File struct {
	DownloadPath string `json:"downloadPath"`
	FilePath     string `json:"filePath"`
}
